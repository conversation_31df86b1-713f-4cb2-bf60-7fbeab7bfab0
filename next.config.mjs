/** @type {import('next').NextConfig} */
const nextConfig = {
    reactStrictMode: false,
    images: {
        remotePatterns: [
            {
                protocol: "https",
                hostname: "the-muse.org",
                pathname: "/wp-content/uploads/**",
            },
            {
                protocol: "https",
                hostname: "museassets-dev-public.s3.us-east-1.amazonaws.com",
                pathname: "/**",
            },
            {
                protocol: "https",
                hostname: "lh3.googleusercontent.com",
                pathname: "/**",
            },
            {
                protocol: "https",
                hostname: "themusewriterscenter--musedev.sandbox.file.force.com",
                pathname: "/**",
            },
            {
                protocol: "https",
                hostname: "**",
            }
        ],
        disableStaticImages: true,
    },
    experimental: {
        serverComponentsExternalPackages: ['@smithy', 'util-stream'],
    },
};

export default nextConfig;