name: Deploy to EC2

on:
  push:
    branches:
      - dev

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3

      - name: Install dependencies
        run: |
          npm ci
        continue-on-error: false

      - name: Build application
        run: |
          npm run build
        continue-on-error: false

      - name: Install SSH key
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.SSH_PRIVATE_KEY }}" > ~/.ssh/deploy_key
          chmod 600 ~/.ssh/deploy_key
          ssh-keyscan -H ${{ secrets.EC2_HOST }} >> ~/.ssh/known_hosts

      - name: Deploy to EC2
        run: |
          # Copy build files to EC2
          scp -i ~/.ssh/deploy_key -r ./build/* ${{ secrets.EC2_USERNAME }}@${{ secrets.EC2_HOST }}:/home/<USER>/workspaces/projects/frontend
          
          # SSH into EC2 and restart PM2
          ssh -i ~/.ssh/deploy_key ${{ secrets.EC2_USERNAME }}@${{ secrets.EC2_HOST }} '
            cd /home/<USER>/workspaces/projects/frontend
            pm2 restart muse-frontend || pm2 start npm --name "muse-frontend" -- start
          '