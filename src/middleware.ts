import { NextRequest, NextResponse } from 'next/server'
import { ROLES } from './types/enum'
import { RoutePattern } from './types/types'
import { isRouteMatched } from './utils/utils'

// Constants
const IMAGE_EXTENSIONS = ['.jpg', '.jpeg', '.png', '.svg'] as const
const SIGN_IN_URL = '/sign-in'
const DASHBOARD_URL = '/dashboard'
const HOME_URL = '/'

const nonLoginProtectedRoutes: RoutePattern[] = [
  '/dashboard',
  '/cart',
  '/credits',
  '/membership',
  '/my-classes',
  '/my-account',
  '/my-account/:id',
  '/teachers',
  // '/teachers/:id',
  '/orders',
  '/notifications',
  '/clubs',
  '/account',
  '/account/:id',
  '/my-kids',
  '/my-kids/:id',
  '/payment/success',
  '/payment/failed',
  '/classes-at-a-glance',
]

const studentProtectedRoutes: RoutePattern[] = [
  '/dashboard',
  '/classes',
  '/classes/:id',
  '/events',
  '/events/:id',
  '/clubs',
  '/clubs/:id',
  '/outreach',
  '/outreach/:id',
  '/cart',
  '/checkout',
  '/my-bookings',
  '/my-bookings/:id',
  '/credits',
  '/membership',
  '/my-account',
  '/teachers',
  '/teachers/:id',
  '/orders',
  '/payment/success',
  '/payment/failed',
  '/donations',
  '/notifications',
  '/account/:id',
  '/authors/:id',
  '/classes-at-a-glance',
]

const teacherProtectedRoutes: RoutePattern[] = [
  '/dashboard',
  '/my-classes/active',
  '/my-calendar',
  '/my-classes/previous',
  '/my-classes',
  '/my-kids',
  '/my-kids/:id',
  '/my-account',
  '/classes/:id',
  '/attendance',
  '/mailing-lists',
  '/account/my-public-profile',
  '/notifications',
  '/authors/:id',
  '/account/:id',
  '/donations',
  '/classes-at-a-glance',
]

const parentProtectedRoutes: RoutePattern[] = [
  '/dashboard',
  '/classes',
  '/classes/:id',
  '/events',
  '/events/:id',
  '/clubs',
  '/clubs/:id',
  '/outreach',
  '/outreach/:id',
  '/clubs',
  '/cart',
  '/orders',
  '/my-account',
  '/teachers',
  '/teachers/:id',
  '/authors/:id',
  '/my-kids/:id',
  '/my-kids/:id/edit',
  '/checkout',
  '/donations',
  '/classes-at-a-glance',
]

function isNonLoginProtectedRoute(
  path: string,
  isUserLoggedIn: boolean = false,
): boolean {
  return isRouteMatched(path, nonLoginProtectedRoutes, isUserLoggedIn)
}

function isProtectedRoute(path: string): boolean {
  return isRouteMatched(path, studentProtectedRoutes)
}

function isTeacherProtectedRoute(path: string): boolean {
  return isRouteMatched(path, teacherProtectedRoutes)
}

function isParentProtectedRoute(path: string): boolean {
  return isRouteMatched(path, parentProtectedRoutes)
}

function handleRoleBasedRouting(
  pathname: string,
  role: string,
  request: NextRequest,
): NextResponse | null {
  switch (role) {
    case ROLES.Teacher:
      if (!isTeacherProtectedRoute(pathname)) {
        return NextResponse.redirect(new URL(DASHBOARD_URL, request.url))
      }
      break
    case ROLES.Adult_Student:
    case ROLES.Youth_Student:
      if (!isProtectedRoute(pathname)) {
        return NextResponse.redirect(new URL(DASHBOARD_URL, request.url))
      }
      break
    case ROLES.Parent:
      if (!isParentProtectedRoute(pathname)) {
        return NextResponse.redirect(new URL(DASHBOARD_URL, request.url))
      }
      break
    default:
      return NextResponse.redirect(new URL(DASHBOARD_URL, request.url))
  }
  return null
}

// Simple cookie parser to avoid importing server actions
function parseUserFromCookie(request: NextRequest) {
  const userCookie = request.cookies.get('user')
  if (!userCookie?.value) return null

  try {
    return JSON.parse(userCookie.value)
  } catch {
    return null
  }
}

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // Skip middleware for image files and static assets
  if (IMAGE_EXTENSIONS.some((ext) => pathname.endsWith(ext))) {
    const response = NextResponse.next()
    response.headers.set('x-pathname', pathname)
    return response
  }

  // Skip middleware for API routes
  if (pathname.startsWith('/api/')) {
    const response = NextResponse.next()
    response.headers.set('x-pathname', pathname)
    return response
  }

  // Check for authentication using cookies directly
  const stackAccessCookie = request.cookies.get('stack-access')
  const userData = parseUserFromCookie(request)
  const role = userData?.role

  // Handle non-authenticated users
  if (!stackAccessCookie) {
    if (isNonLoginProtectedRoute(pathname)) {
      const redirectResponse = NextResponse.redirect(
        new URL(SIGN_IN_URL, request.url),
      )
      redirectResponse.headers.set('x-pathname', pathname)
      return redirectResponse
    }
    if (pathname === '/pages') {
      const redirectResponse = NextResponse.redirect(
        new URL(HOME_URL, request.url),
      )
      redirectResponse.headers.set('x-pathname', pathname)
      return redirectResponse
    }
    const response = NextResponse.next()
    response.headers.set('x-pathname', pathname)
    return response
  }

  // Handle authenticated users
  if (pathname === SIGN_IN_URL && userData) {
    const redirectResponse = NextResponse.redirect(
      new URL(DASHBOARD_URL, request.url),
    )
    redirectResponse.headers.set('x-pathname', pathname)
    return redirectResponse
  }

  if (role) {
    const redirectResponse = handleRoleBasedRouting(pathname, role, request)
    if (redirectResponse) {
      redirectResponse.headers.set('x-pathname', pathname)
      return redirectResponse
    }
  }

  const response = NextResponse.next()
  response.headers.set('x-pathname', pathname)
  return response
}

export const config = {
  matcher: ['/((?!api|_next/static|_next/image|images|favicon.ico).*)'],
}
