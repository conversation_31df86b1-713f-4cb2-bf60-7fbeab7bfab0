import Dashboard from '@/components/Icons/Dashboard'
import { SideNavItem } from './interface'
import Book from '@/components/Icons/Book'
import Bag from '@/components/Icons/Bag'
import Club from '@/components/Icons/Club'
import Order from '@/components/Icons/Order'
import Donation from '@/components/Icons/Donation'
import MyClassIcon from '@/components/Icons/MyClassIcon'
import Clock from '@/components/Icons/Clock'
import SettingIcon from '@/components/Icons/SettingIcon'
import GearIcon from '@/components/Icons/GearIcon'
import Person from '@/components/Icons/Person'
export const monthNames = [
  'January',
  'February',
  'March',
  'April',
  'May',
  'June',
  'July',
  'August',
  'September',
  'October',
  'November',
  'December',
]

export const TEEN_APPLICATION_QUESTIONS = [
  'What do you enjoy about writing?',
  'How do you see creative writing fitting into your future?',
  'Why do you want to participate in the Muse Fellowship?',
]

export const TEACHER_TABS = [
  {
    title: 'Attendance Pending',
    key: 'Attendance Pending',
  },
  {
    title: 'Active Classes',
    key: 'Active',
  },
  {
    title: 'Previous Classes',
    key: 'Previous',
  },
  {
    title: 'Mailing Lists',
    key: 'mailing-lists',
  },
]

export const CLASS_DETAILS_TABS = [
  {
    title: 'Class Details',
    key: 'class-details',
  },
  {
    title: 'Class Attendees',
    key: 'class-attendees',
  },
]

export type PaymentType = 'One-time' | 'Subscription'

export const loginAccessPaths = ['/', '/pages']

export const CLASS_TYPE = [
  { label: 'All', value: 'All' },
  { label: 'Adult Class', value: 'Adult Class' },
  { label: 'Youth Class', value: 'Youth Class' },
]

export const ICONS = {
  dashboard: Dashboard,
  classes: Book,
  events: Bag,
  clubs: Club,
  orders: Order,
  donations: Donation,
  myAccount: Person,
  myClass: MyClassIcon,
  calendar: Clock,
} as const

export const STUDENT_SIDE_NAVBAR: SideNavItem[] = [
  {
    title: 'Dashboard',
    link: '/dashboard',
    icon: 'dashboard',
  },
  {
    title: 'My Bookings',
    link: '/my-bookings',
    icon: 'myClass',
  },
  {
    title: 'Classes',
    link: '/classes',
    icon: 'classes',
  },
  {
    title: 'Events & Clubs',
    link: '/events',
    icon: 'events',
  },
  {
    title: 'My Account',
    link: '/my-account',
    icon: 'myAccount',
  },
]

export const TEACHER_SIDE_NAVBAR: SideNavItem[] = [
  {
    title: 'Dashboard',
    link: '/dashboard',
    icon: 'dashboard',
  },
  {
    title: 'My Calendar',
    link: '/my-calendar',
    icon: 'calendar',
  },
  {
    title: 'Classes',
    link: '/my-classes',
    icon: 'classes',
    subItems: [
      {
        title: 'Classes Teaching',
        link: '/my-classes/active',
      },
      {
        title: 'Classes Taught',
        link: '/my-classes/previous',
      },
    ],
  },
  {
    title: 'Attendance',
    link: '/attendance',
    icon: 'events',
  },
  {
    title: 'Mailing Lists',
    link: '/mailing-lists',
    icon: 'clubs',
  },
  {
    title: 'My Account',
    link: '/my-account',
    icon: 'myAccount',
  },
]

export const PARENT_SIDE_NAVBAR: SideNavItem[] = [
  {
    title: 'Dashboard',
    link: '/dashboard',
    icon: 'dashboard',
  },
  {
    title: 'Classes',
    link: '/classes',
    icon: 'classes',
  },
  {
    title: 'Events & Clubs',
    link: '/events',
    icon: 'events',
  },
  {
    title: 'My Account',
    link: '/my-account',
    icon: 'myAccount',
  },
]

export const PAGINATION_LIMIT = 18

export const URL_PATHS_NO_HEADER_FOOTER_SIDEBAR = ['/checkout', '/invoice']

export const NON_LOGGED_IN_PROTECTED_ROUTES = [
  '/',
  '/pages',
  '/pages/:id',
  '/blogs',
  '/blogs/:id',
  '/tuition-assistance',
  '/teacher-onboarding',
  '/teen-writers-application',
  '/teach',
  '/survey-form',
  '/survey-form/:id',
  '/muse-icons',
  '/subscription',
]

export const STUDENT_TABS: Object[] = [
  { key: 'About', value: 'class-details' },
  { key: 'Rewards', value: 'rewards' },
]

export const TEACHER_CLASS_TABS: Object[] = [
  { key: 'Schedule', value: 'schedule' },
  { key: 'About', value: 'class-details' },
  { key: 'Rewards', value: 'rewards' },
]

export const getStudentNavigation = (userRole?: string): SideNavItem[] => {
  const navigation = [...STUDENT_SIDE_NAVBAR]
  const classesIndex = navigation.findIndex((item) => item.title === 'Classes')

  if (classesIndex !== -1 && userRole) {
    const role = userRole.toLowerCase()

    let category = ''
    if (role.includes('adult student')) {
      category = 'Adult Class'
    } else if (role.includes('youth student')) {
      category = 'Youth Class'
    }

    if (category) {
      navigation[classesIndex] = {
        ...navigation[classesIndex],
        link: `/classes?category=${encodeURIComponent(category)}`, // ✅ automatically encodes spaces
      }
    }
  }

  return navigation
}
