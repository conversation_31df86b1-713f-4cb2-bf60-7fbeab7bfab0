import { ICONS } from './constants'

type IconKey = keyof typeof ICONS

export interface SideNavItem {
  title: string
  link: string
  icon: IconKey
  subItems?: {
    title: string
    link: string
  }[]
}

export interface PriceDetails {
  userId: string
  entityId: string
  entityType: string
  amount: number
  walletBalance: number
  iat: number
  discountedAmount: number
  paymentType: string
  isWalletEnabled: boolean
}

export interface FormField {
  type: string
  typeLabel: string
  label: string
  apiName: string
  required: boolean
  placeholder?: string | null
  isShortText: boolean
  isEmail: boolean
  isPhone: boolean
  isDate: boolean
  isLongText: boolean
  isPicklist: boolean
  options: any[]
  optionsText?: string | null
  order: number
}

export interface FormSchema {
  version: number
  savedAt: string
  fields: FormField[]
}
