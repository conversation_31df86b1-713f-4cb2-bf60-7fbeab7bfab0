import { createHash } from 'crypto'
import { format, parse, parseISO } from 'date-fns'
import { differenceInYears } from 'date-fns/fp/differenceInYears'
import { DateTime } from 'luxon'
import {
  NON_LOGGED_IN_PROTECTED_ROUTES,
  URL_PATHS_NO_HEADER_FOOTER_SIDEBAR,
} from './constants'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import timezone from 'dayjs/plugin/timezone'
import { RoutePattern } from '@/types/types'
import { getPresignedPutUrl, getPublicUrl } from '@/lib/aws'
import { toast } from 'react-toastify'

dayjs.extend(utc)
dayjs.extend(timezone)

export function capitalizeFirstLetter(str: string | null) {
  if (!str) return ''
  return str.charAt(0).toUpperCase() + str.slice(1)
}

type DebouncedFunction<T extends (...args: any[]) => void> = (
  ...args: Parameters<T>
) => void

export function debounce<T extends (...args: any[]) => void>(
  func: T,
  delay: number,
): DebouncedFunction<T> {
  let timeoutId: ReturnType<typeof setTimeout> | null = null

  return function (...args: Parameters<T>) {
    if (timeoutId) {
      clearTimeout(timeoutId)
    }

    timeoutId = setTimeout(() => {
      func(...args)
    }, delay)
  }
}

export function throttle<T extends (...args: any[]) => void>(
  func: T,
  limit: number,
): (...args: Parameters<T>) => void {
  let lastFunc: ReturnType<typeof setTimeout> | null
  let lastRan: number | null = null

  return function (...args: Parameters<T>) {
    if (!lastRan) {
      func(...args)
      lastRan = Date.now()
    } else {
      if (lastFunc) {
        clearTimeout(lastFunc)
      }
      lastFunc = setTimeout(
        () => {
          if (Date.now() - (lastRan as number) >= limit) {
            func(...args)
            lastRan = Date.now()
          }
        },
        limit - (Date.now() - (lastRan as number)),
      )
    }
  }
}

export function responseGenerator(
  success: boolean,
  data?: any,
  tokenExpired?: boolean,
) {
  return {
    success,
    data,
    tokenExpired,
  }
}

export function generateDummyEmailFromPhone(phoneNumber: string): string {
  // Normalize the phone number: remove non-digit characters
  const normalizedPhone = phoneNumber.replace(/\D/g, '')

  // Ensure it's a valid phone number (at least 10 digits)
  if (normalizedPhone.length < 10) {
    throw new Error('Invalid phone number: must contain at least 10 digits.')
  }

  // Use last 10 digits to keep it consistent
  const phoneSuffix = normalizedPhone.slice(-10)

  // Create a dummy email format
  const dummyEmail = `user${phoneSuffix}@example.com`

  return dummyEmail.toLowerCase()
}

export function isDummyEmail(email: string): boolean {
  const dummyEmailRegex = /^user\d{10}@example\.com$/i
  return dummyEmailRegex.test(email)
}

export function getPhoneFromDummyEmail(email: string): string | undefined {
  if (!isDummyEmail(email)) return undefined

  const match = email.match(/^user(\d{10})@example\.com$/i)
  return match ? match[1] : undefined
}

export function hashString(str: string): string {
  return createHash('sha512').update(str).digest('hex')
}

export function verifyHash(input: string, hash: string): boolean {
  const hashedInput = hashString(input)
  return hashedInput === hash
}

export function getNameLetter(name: string): string {
  if (!name || typeof name !== 'string') {
    return 'N/A'
  }

  const nameParts = name.trim().split(/\s+/)

  if (nameParts.length === 0) {
    return 'N/A'
  }

  if (nameParts.length === 1) {
    return nameParts[0].substring(0, 2).toUpperCase()
  }

  const firstNameInitial = nameParts[0][0]
  const lastNameInitial = nameParts[nameParts.length - 1][0]

  return (firstNameInitial + lastNameInitial).toUpperCase()
}

export const getBackgroundColor = (type: string): string => {
  switch (type) {
    case 'Introductory':
      return '#FFE4BA'
    case 'Open to Adults':
      return '#DDFFAD'
    case 'Advanced/Professional':
      return '#9BB4FF'
    case 'games':
      return '#FFB5B6'
    default:
      return '#FFE4BA'
  }
}
export const startOfDay = (date: string): string => {
  if (!date) return ''
  const parsedDate = parseISO(date)
  return dayjs
    .tz(parsedDate, 'America/New_York')
    .add(1, 'day')
    .startOf('day')
    .utc()
    .format()
}

export const convertTimestampToET = (date: Date): string => {
  return dayjs.tz(date, 'America/New_York').utc().format()
}

export const endOfDay = (date: string): string => {
  if (!date) return ''
  const parsedDate = parseISO(date)
  return dayjs
    .tz(parsedDate, 'America/New_York')
    .add(1, 'day')
    .endOf('day')
    .utc()
    .format()
}

export const arrayToSqlInQuery = (arr: string[]): string => {
  const stringArray = arr.map((item) => `'${item}'`)

  const sqlString = `(${stringArray.join(', ')})`

  return sqlString
}

const normalizeDate = (input: string): string => {
  try {
    const date = parseISO(input)
    return format(date, 'dd-MM-yyyy')
  } catch {
    return input
  }
}

export const isAbove18 = (dob: string): boolean => {
  const normalizedDob = normalizeDate(dob)
  const today = new Date()
  const birth = parse(normalizedDob, 'dd-MM-yyyy', new Date())
  const age = differenceInYears(birth, today)
  return age >= 18
}

export const formatDateToAmerican = (dateString: string): string => {
  if (!dateString) return ''

  const dateTime = DateTime.fromISO(dateString, { zone: 'America/New_York' })
  return dateTime.toFormat('MM/dd/yyyy')
}

export const formatTime = (timeString: string): string => {
  if (!timeString) return ''
  const isoTime = `${new Date().toISOString().split('T')[0]}T${timeString}:00`
  const dateTime = DateTime.fromISO(isoTime, { zone: 'America/New_York' })
  return `${dateTime.toFormat('h:mm a')} (ET)`
}

export const formatDateTime = (
  dateTimeString: string,
): { date: string; time: string } => {
  if (!dateTimeString) return { date: '', time: '' }

  const dateTime = DateTime.fromISO(dateTimeString, {
    zone: 'America/New_York',
  })
  const formattedDate = dateTime.toFormat('MM/dd/yyyy')
  const formattedTime = formatSalesforceTime(dateTimeString)

  return {
    date: formattedDate,
    time: formattedTime,
  }
}

export const formatSalesforceTime = (
  startTime: string | null,
  endTime?: string | null,
) => {
  if (!startTime) return ''

  const start = DateTime.fromISO(startTime, { zone: 'America/New_York' })
  if (!endTime) {
    return `${start.toFormat('h:mm a')} (ET)`
  }

  const end = DateTime.fromISO(endTime, { zone: 'America/New_York' })
  return `${start.toFormat('h:mm a')}-${end.toFormat('h:mm a')} (ET)`
}

export const getTimeAgo = (createdDate: string) => {
  const now = new Date()
  const created = new Date(createdDate)
  const diffInMs = now.getTime() - created.getTime()
  const diffInMinutes = Math.floor(diffInMs / (1000 * 60))

  if (diffInMinutes < 60) return `${diffInMinutes} m ago`
  const diffInHours = Math.floor(diffInMinutes / 60)
  if (diffInHours < 24) return `${diffInHours} h ago`
  const diffInDays = Math.floor(diffInHours / 24)
  return `${diffInDays} d ago`
}

export const isHeaderFooterSidebarPath = (path: string): boolean => {
  return URL_PATHS_NO_HEADER_FOOTER_SIDEBAR.some((item) => path.includes(item))
}

export function formatETTimeRange(
  time: string,
  duration: number,
  date: string,
): string {
  try {
    const timeOnly = time.replace('Z', '').trim()
    const combined = `${date}T${timeOnly}`
    const utcTime = dayjs.utc(combined)

    if (!utcTime.isValid()) {
      console.error('Invalid datetime:', combined)
      return 'Invalid datetime'
    }

    const startET = utcTime.tz('America/New_York')
    const endET = startET.add(duration, 'minute')

    return `${startET.format('h:mm A')} – ${endET.format('h:mm A')}`
  } catch (error) {
    console.error('Error formatting time:', error)
    return 'Error'
  }
}
export function isNonLoginProtectedRoute(path: string): boolean {
  return NON_LOGGED_IN_PROTECTED_ROUTES.some((route) => {
    if (route === path) return true
    return false
  })
}

export function formatDateTimeRange(
  startDateTime: string,
  endDateTime?: string,
): string {
  if (!startDateTime) return ''
  try {
    const startET = DateTime.fromISO(startDateTime, {
      zone: 'America/New_York',
    })
    const dateFormatted = startET.toFormat('MMMM d')
    const startTimeFormatted = startET.toFormat('h a')

    if (endDateTime) {
      const endET = DateTime.fromISO(endDateTime, { zone: 'America/New_York' })
      const endTimeFormatted = endET.toFormat('h a')
      return `${dateFormatted}, ${startTimeFormatted} - ${endTimeFormatted} ET`
    }

    return `${dateFormatted}, ${startTimeFormatted} ET`
  } catch (error) {
    console.error('Error formatting datetime range:', error)
    return 'Error formatting date'
  }
}

export function isRouteMatched(
  path: string,
  routes: RoutePattern[],
  isUserLoggedIn: boolean = true,
): boolean {
  return routes
    .concat(isUserLoggedIn ? NON_LOGGED_IN_PROTECTED_ROUTES : [])
    .some((route) => {
      if (route.includes(':')) {
        const regexPattern = route.replace(':id', '[^/]+')
        const regex = new RegExp(`^${regexPattern}$`)
        return regex.test(path)
      }
      return route === path
    })
}

export function runOnce<T extends (...args: any[]) => void>(func: T): T {
  let hasRun = false
  return ((...args: Parameters<T>) => {
    if (!hasRun) {
      hasRun = true
      return func(...args)
    }
  }) as T
}

// 2010-03-31 will become "March 31, 2010"
export function formatDate(dateString?: string): string {
  if (!dateString) return 'NA'

  const date = new Date(dateString)
  if (isNaN(date.getTime())) return 'NA'

  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  })
}

export const addPhotoToS3Bucket = async (file: any) => {
  const timeStamp = new Date().getTime()
  if (file) {
    const fileName = file?.name + timeStamp
    try {
      const presignedUrl = await getPresignedPutUrl(fileName, file.type)
      const uploadResponse = await fetch(presignedUrl, {
        method: 'PUT',
        body: file,
        headers: {
          'Content-Type': file.type,
        },
      })
      if (uploadResponse?.ok) {
        const imageLink = await getPublicUrl(fileName)
        return imageLink
      }
    } catch (error) {
      toast.error('Something went wrong!' + error, { autoClose: 1500 })
    } finally {
    }
  }
}

export const calculateProcessingFee = (amount: number) => {
  const feePercentage = 3
  const feeAmount = (amount * feePercentage) / 100
  const roundedTo2Decimal = Math.round(feeAmount * 100) / 100
  return roundedTo2Decimal
}
