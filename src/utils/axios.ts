import axios from 'axios'

export const AuthoriseDotNetAxios = axios.create({
  baseURL: process.env.AUTHORISE_NET_API_URL,
  headers: {
    'Content-Type': 'application/json',
    Accept: 'application/json',
  },
})

export const StackAuthServerAxios = axios.create({
  baseURL: process.env.STACK_API_URL,
  headers: {
    'X-Stack-Project-Id': process.env.STACK_PROJECT_ID,
    'X-Stack-Secret-Server-Key': process.env.STACK_SECRET_SERVER_KEY,
    'x-stack-access-type': 'server',
  },
})
