import { getToken, handleToken, logout } from '@/lib/actions/login.actions'
import { toast } from 'react-toastify'

export async function callApiWithToken<T>(
  apiFunction: (...args: any[]) => Promise<T>,
  isTokenRequired: boolean = true,
  ...args: any[]
): Promise<T> {
  try {
    let accessToken = ''
    if (isTokenRequired) {
      accessToken = await getToken('access')
      if (!accessToken) {
        toast.error('Session expired, logging out...')
        await logout()
        throw new Error('Session expired, logging out...')
      }
    } else {
      accessToken = ''
    }

    // First call with token (or ' ')
    let res: any = await apiFunction(accessToken, ...args)

    // If tokenExpired, refresh token and retry
    if ((res as any)?.tokenExpired) {
      const newAccessToken = await handleToken()
      if (!newAccessToken) {
        toast.error('Unauthorized: Token expired. Logging out...')
        await logout()
        throw new Error('Unauthorized: Token expired')
      }
      // Retry call with new token or '' if token not required
      const retryToken = isTokenRequired ? newAccessToken : ''
      res = await apiFunction(retryToken, ...args)
    }
    if (!res.success) {
      toast.error(res?.data?.errors[0] || 'An error occurred')
    }
    return res
  } catch (error: any) {
    toast.error(error.message || 'Something went wrong')
    throw error
  }
}
