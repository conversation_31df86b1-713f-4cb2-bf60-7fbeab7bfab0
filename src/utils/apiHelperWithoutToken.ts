import { toast } from 'react-toastify'

export async function callApiWithoutToken<T>(
  apiFunction: (...args: any[]) => Promise<T>,
  ...args: any[]
): Promise<T> {
  try {
    let res: any = await apiFunction(...args)
    if (!res.success) {
      toast.error(res?.data?.errors[0] || 'An error occurred')
    }
    return res
  } catch (error: any) {
    toast.error(error.message || 'Something went wrong')
    throw error
  }
}
