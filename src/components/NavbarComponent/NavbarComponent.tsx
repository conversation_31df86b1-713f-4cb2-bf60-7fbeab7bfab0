'use client'
// NavbarComponent.tsx
import React, { useState } from 'react'
import './NavbarComponent.css'
import Link from 'next/link'

const NavbarComponent = ({ navData }: { navData: any }) => {
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null)

  const handleLinkClick = () => {
    setActiveDropdown(null) // Close dropdown on any link click
  }

  const handleMouseEnter = (itemId: string) => {
    setActiveDropdown(itemId)
  }

  const handleMouseLeave = () => {
    setActiveDropdown(null)
  }

  const renderMenu = (items: any) => {
    return (
      <ul>
        {items.map((item: any) => (
          <li
            key={item?.Id}
            className={`${item?.children.length > 0 ? 'dropDown-menu' : ''} cursor-pointer relative`}
            onMouseEnter={() =>
              item?.children.length > 0 && handleMouseEnter(item.Id)
            }
          >
            <Link
              href={item?.Slug__c || '/#'}
              className={`w-max text-wrap text-lg font-medium navbar-text ${item.Id === activeDropdown ? 'font-semibold text-black' : 'text-color-grey-6'}`}
              onClick={handleLinkClick}
            >
              {item?.Name}
            </Link>

            {item?.children.length > 0 && (
              <ul
                className={`dropdown-container ${activeDropdown === item.Id ? 'active' : ''}`}
                style={{ width: '100%' }}
                onMouseLeave={() =>
                  item?.children.length > 0 && handleMouseLeave()
                }
              >
                {item?.children.map((child: any) => (
                  <li key={child.Id} className="dropdown-column">
                    <Link
                      href={child.Slug__c || '/#'}
                      className="dropdown-category"
                      onClick={handleLinkClick}
                    >
                      {child.Name}
                    </Link>
                    {child.children && child.children.length > 0 && (
                      <ul>
                        {child.children.map((grandChild: any) => (
                          <li key={grandChild.Id}>
                            <Link
                              href={grandChild.Slug__c || '/#'}
                              className="normal-font"
                              onClick={handleLinkClick}
                            >
                              <span className="text-color-grey-6">
                                {grandChild.Name}
                              </span>
                            </Link>
                          </li>
                        ))}
                      </ul>
                    )}
                  </li>
                ))}
              </ul>
            )}
          </li>
        ))}
      </ul>
    )
  }

  return <nav onMouseLeave={handleMouseLeave}>{renderMenu(navData)}</nav>
}

export default NavbarComponent
