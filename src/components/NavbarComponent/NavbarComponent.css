/* NavbarComponent.css */
/* Base navigation styling */
nav > ul {
    list-style: none;
    margin: 0px;
    padding: 0px;
    display: flex;
}

nav > ul > li {
    position: relative;
    padding: 25px 15px;
}

.dropDown-menu {
    position: relative;
}

/* Arrow indicators for dropdown menus */
nav > ul > .dropDown-menu:after {
    content: '';
    width: 0;
    height: 0;
    visibility: hidden;
    border-style: solid;
    border-width: 5px 5px 0 5px;
    border-color: #666 transparent transparent transparent;
    position: absolute;
    top: 38px;
    right: 0px;
    transition: all 0.3s ease;
}

/* Style the <a> to include padding and ensure it fills the space */
nav > ul > .dropDown-menu > a {
    display: block;
    padding: 25px 15px;
    position: relative;
    text-decoration: none;
}

/* Full-width dropdown container for top-level items */
nav > ul > .dropDown-menu > ul {
    list-style: none;
    margin: 0;
    padding: 20px;
    position: absolute;
    left: 0;
    top: 100%;
    width: 100vw;
    background-color: white;
    border-top: 2px solid #E1E1E1;
    box-shadow: 0px 6px 6px 0px rgba(0, 0, 0, 0.3);
    opacity: 0;
    visibility: hidden;
    z-index: 1000;
    animation: dropdownFade 0.3s ease;
    transform: translateX(-50%);
    left: 50%;
    transition: opacity 0.3s, visibility 0s linear 0.3s;
    pointer-events: none;
    padding-top: 30px;
}

/* Create an invisible hover bridge between nav item and dropdown */
.navbar-text::before {
    content: "";
    position: absolute;
    height: 20px;
    width: 100%;
    bottom: -20px;
    left: 0;
    z-index: 999;
}

/* Adjust for proper positioning - full width relative to the viewport */
nav > ul > .dropDown-menu {
    position: static;
}

/* Remove padding from li when it has a dropdown to avoid double padding */
nav > ul > .dropDown-menu {
    padding: 0;
}

/* Show dropdown when active class is present */
nav > ul > .dropDown-menu > ul.active {
    opacity: 1;
    visibility: visible;
    pointer-events: auto;
    transition-delay: 0s;
}

/* Keep parent link highlighted when dropdown is active */
nav > ul > .dropDown-menu > ul.active ~ a {
    font-weight: bold;
    color: black;
}

/* Grid layout for dropdown content */
nav > ul > .dropDown-menu > ul {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
}

/* Styling for dropdown menu items */
nav .dropDown-menu li a {
    display: block;
    padding: 12px 12px;
    transition: all 0.2s ease;
    font-weight: normal;
}

/* Make the first column title have hover effect */
.dropdown-column:first-child .dropdown-category:hover {
    font-weight: bold !important;
}

/* For other dropdown links - only make bold on hover */
nav .dropDown-menu li a:hover {
    font-weight: bold;
    color: #000;
}

/* Animation for dropdown appearance */
@keyframes dropdownFade {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

/* Column headers within dropdown - always bold */
.dropdown-category {
    font-weight: bold !important;
    color: #000;
    padding-bottom: 5px;
}

/* Normal font weight for all other items by default */
.dropdown-column ul li a {
    font-weight: normal;
}

/* Dropdown container structure */
.dropdown-column {
    display: flex;
    flex-direction: column;
}