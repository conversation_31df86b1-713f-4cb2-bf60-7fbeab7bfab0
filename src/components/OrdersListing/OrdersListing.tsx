'use client'
import React, { useEffect, useState, useRef } from 'react'
import CustomDropDown from '../CustomComponents/CustomDropDown/CustomDropDown'
import FilterIcon from '../Icons/FilterIcon'
import OrderCard from '../OrderCard/OrderCard'
import NoResult from '../NoResult/NoResult'
import InkPot from '../Icons/InkPot'
import { useApi } from '@/hooks/useApi'
import { getOrders } from '@/lib/actions/order.actions'
import { format, lastDayOfYear, startOfYear } from 'date-fns'
import InfiniteScroll from 'react-infinite-scroll-component'
import { PAGINATION_LIMIT } from '@/utils/constants'
import OrdersSkeleton from '../Skeleton/OrdersSkeleton'

const options = [
  { label: '2025', value: '2025' },
  { label: '2024', value: '2024' },
  { label: '2023', value: '2023' },
  { label: '2022', value: '2022' },
]

const OrdersListing = () => {
  const [orderData, setOrderData] = useState<any>([])
  const [year, setYear] = useState<number>(2025)
  const offset = useRef<number>(0)
  const [hasMore, setHasMore] = useState<boolean>(true)
  const [response, getAllOrders] = useApi(
    (access: string, start, end, limit, offset) =>
      getOrders(access, start, end, limit, offset),
  )

  useEffect(() => {
    if (response.isSuccess) {
      const moreOrders = response?.data?.data?.data || []
      if (moreOrders.length > 0) {
        setOrderData((prev: any) => [...prev, ...moreOrders])
        offset.current += PAGINATION_LIMIT
        if (
          response?.data?.data?.total <=
          orderData.length + moreOrders.length
        ) {
          setHasMore(false)
        }
      } else {
        setHasMore(false)
      }
    }
  }, [response])

  useEffect(() => {
    setOrderData([])
    offset.current = 0
    setHasMore(true)
    fetchMoreData()
  }, [year])

  const fetchMoreData = () => {
    const startDate = format(startOfYear(new Date(year, 0, 1)), 'yyyy-MM-dd')
    const endDate = format(lastDayOfYear(new Date(year, 0, 1)), 'yyyy-MM-dd')
    getAllOrders(startDate, endDate, PAGINATION_LIMIT, offset.current)
  }

  const handleFilter = (value: string) => {
    setYear(parseInt(value))
  }

  return (
    <div>
      <div className="bg-white px-3 md:px-7 pb-7 flex items-end md:items-center justify-end flex-col gap-3 md:flex-row w-full">
        <div className="w-max">
          <CustomDropDown
            icon={<FilterIcon />}
            title={`${year}`}
            options={options}
            handleOptionClick={handleFilter}
            width={'w-full'}
          />
        </div>
      </div>
      {response.isFetching && orderData.length === 0 && (
        <div className="w-full flex items-center justify-center mt-5">
          <div className="w-11/12">
            <OrdersSkeleton count={3} />
          </div>
        </div>
      )}
      <div className="w-full flex items-center justify-center">
        <div className="w-11/12 mt-5 flex flex-col gap-5 mb-5 items-center justify-center">
          <InfiniteScroll
            dataLength={orderData.length}
            next={fetchMoreData}
            hasMore={hasMore}
            loader={orderData.length !== 0 && <OrdersSkeleton />}
            endMessage={
              <>
                {orderData.length == 0 && !response.isFetching ? (
                  <NoResult
                    icon={<InkPot />}
                    title={'Nothing Here... Yet!'}
                    desc={
                      ' You haven’t placed any orders yet. Start your journey toward becoming the writer you’ve always dreamed of with our classes.'
                    }
                    btnText={'Explore Classes'}
                    btnLink={'/classes'}
                  />
                ) : (
                  <div className="flex items-center font-bold justify-center mb-10 text-color-grey-1">
                    No more orders to show
                  </div>
                )}
              </>
            }
            style={{
              overflow: 'hidden',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
            }}
            scrollableTarget="scrollableDiv"
            pullDownToRefreshThreshold={50}
          >
            <div className="flex flex-col w-full">
              {orderData?.length > 0 && <OrderCard data={orderData} />}
            </div>
          </InfiniteScroll>
        </div>
      </div>
    </div>
  )
}

export default OrdersListing
