'use client'

import { useGetAllFollowedTeachersQuery } from '@/lib/redux/slices/apiSlice/apiSlice'
import Link from 'next/link'
import { useState } from 'react'

const FavouriteTeachers = () => {
  const [showAll, setShowAll] = useState(false)

  const { data, isFetching } = useGetAllFollowedTeachersQuery()
  const teachers = data?.data || []

  // Remove duplicate teachers based on .id
  const uniqueTeachers = teachers.filter(
    (teacher: any, index: number, self: any[]) =>
      index === self.findIndex((t) => t.id === teacher.id),
  )

  const visibleTeachers = showAll ? uniqueTeachers : uniqueTeachers?.slice(0, 5)

  const handleViewAll = () => {
    setShowAll((prev) => !prev)
  }

  return (
    <div>
      {/* Header */}
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-2xl font-medium text-color-black">
          Favorite Teachers
        </h2>
        {teachers?.length > 5 && (
          <button
            onClick={handleViewAll}
            className="text-sm font-normal leading-[2] tracking-normal text-right text-color-grey-1"
          >
            {showAll ? 'View Less' : 'View All'}
          </button>
        )}
      </div>

      {/* Loader */}
      {isFetching ? (
        <div className="flex flex-wrap gap-6">
          {[...Array(5)].map((_, i) => (
            <div
              key={i}
              className="flex flex-col items-center w-24 animate-pulse"
            >
              <div className="w-24 h-24 rounded-full bg-gray-200" />
              <div className="w-16 h-3 mt-2 bg-gray-200 rounded" />
            </div>
          ))}
        </div>
      ) : teachers?.length === 0 ? (
        <div className="flex items-center font-bold justify-center mb-10 text-color-grey-1">
          You haven&rsquo;t followed any teachers yet
        </div>
      ) : (
        <div
          className={`flex flex-wrap gap-6 ${
            !showAll ? 'overflow-hidden' : ''
          }`}
        >
          {visibleTeachers?.map((teacher: any, index: number) => (
            <Link
              href={`/teachers/${teacher?.id}`}
              key={index}
              className="flex flex-col items-center w-24"
            >
              <div className="w-24 h-24 rounded-full overflow-hidden border-4 border-gray-200 shrink-0">
                <img
                  src={teacher?.photo}
                  alt={teacher?.name}
                  className="w-full h-full object-cover shrink-0"
                />
              </div>
              <p className="mt-2 text-center text-sm font-semibold">
                {teacher?.name}
              </p>
            </Link>
          ))}
        </div>
      )}
    </div>
  )
}

export default FavouriteTeachers
