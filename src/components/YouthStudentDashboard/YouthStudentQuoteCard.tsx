'use client'

import { useGetAccountQuotesQuery } from '@/lib/redux/slices/apiSlice/apiSlice'
import { useState } from 'react'

const YouthStudentQuoteCard = () => {
  const { data, isFetching } = useGetAccountQuotesQuery({
    type: 'Youth Student',
  })

  const [index, setIndex] = useState(0)

  const quotes = data?.success ? data.data : []

  const handleCardClick = () => {
    if (quotes.length > 1) {
      setIndex((prev) => (prev + 1) % quotes.length)
    }
  }

  if (isFetching) {
    return (
      <div className="w-full h-28 bg-gray-200 rounded-xl animate-pulse relative overflow-hidden">
        <div className="absolute top-6 left-6 space-y-3">
          <div className="h-5 w-3/4 bg-gray-300 rounded-md" />
          <div className="h-5 w-1/2 bg-gray-300 rounded-md" />
        </div>
        <div className="absolute bottom-10 left-6">
          <div className="h-4 w-1/4 bg-gray-300 rounded-md" />
        </div>
      </div>
    )
  }

  if (!quotes.length) {
    return (
      <div className="w-full flex items-center justify-center p-6 rounded-2xl bg-gradient-to-r from-color-blue-5 to-color-blue-5 text-white">
        <p className="text-sm italic">No quotes available</p>
      </div>
    )
  }

  const current = quotes[index]
  const baseColor = `#${current?.Background_Color__c}`
  const gradientStyle = {
    backgroundImage: `linear-gradient(to right, ${baseColor}, ${baseColor})`,
  }

  return (
    <div
      className="relative w-full text-white p-6 rounded-2xl overflow-hidden cursor-pointer"
      style={gradientStyle}
      onClick={handleCardClick}
    >
      <p className="font-semibold italic text-sm leading-6 tracking-normal text-neutral-white/60">
        {current?.Title__c}
      </p>
      <p className="text-base font-medium leading-6 mt-2">
        {current?.Content__c || 'No Content Available'}
      </p>
      <p className="font-semibold italic text-sm leading-6 tracking-normal text-neutral-white/60 mt-2">
        {current?.Author__c}
      </p>

      <div className="absolute top-0 right-0 w-40 h-40 bg-blue-200 opacity-30 rounded-full translate-x-1/3 -translate-y-1/3 pointer-events-none" />
      <div className="absolute bottom-0 right-12 w-32 h-32 bg-blue-100 opacity-30 rounded-full translate-y-1/3 pointer-events-none" />
    </div>
  )
}

export default YouthStudentQuoteCard
