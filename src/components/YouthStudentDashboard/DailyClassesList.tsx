'use client'

import { ChevronLeft, ChevronRight } from 'lucide-react'
import dayjs, { Dayjs } from 'dayjs'
import { useEffect, useState } from 'react'
import { toast } from 'react-toastify'
import { useApi } from '@/hooks/useApi'
import ClassEventCard from '../ClassEventCard/ClassEventCard'
import { getAllMeetings } from '@/lib/actions/class.actions'
import ClassEventCardSkeleton from '../Skeletons/ClassEventCardSkeleton'

type DailyClassesListProps = {
  selectedDate: Dayjs
  setSelectedDate: React.Dispatch<React.SetStateAction<Dayjs>>
}

const DailyClassesList = ({
  selectedDate,
  setSelectedDate,
}: DailyClassesListProps) => {
  const [isLoading, setIsLoading] = useState<boolean>(false)

  const [classResponse, fetchClassDataDay] = useApi(
    (accessToken: string, startDate: string, endDate: string) =>
      getAllMeetings(accessToken, startDate, endDate),
  )

  const getClassDataDayWise = async (start: string, end: string) => {
    setIsLoading(true)
    try {
      await fetchClassDataDay(start, end)
    } catch (error) {
      toast.error('Something went wrong!' + error, { autoClose: 1500 })
    } finally {
      setIsLoading(false)
    }
  }

  const onPrevDate = () => {
    setSelectedDate((prev) => prev.subtract(1, 'day'))
  }

  const onNextDate = () => {
    setSelectedDate((prev) => prev.add(1, 'day'))
  }

  useEffect(() => {
    const formattedDate = selectedDate.format('YYYY-MM-DD')
    getClassDataDayWise(formattedDate, formattedDate)
  }, [selectedDate])

  const classes = classResponse?.data?.data || []

  return (
    <div className="p-4 bg-white rounded-xl border border-gray-200 shadow-sm">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-sm font-bold leading-[1.5] text-neutral-black">
          Classes & Events on {selectedDate.format('MMM D')}
        </h2>
        <div className="flex gap-2">
          <ChevronLeft
            className="w-4 h-4 text-gray-500 cursor-pointer"
            onClick={onPrevDate}
          />
          <ChevronRight
            className="w-4 h-4 text-gray-500 cursor-pointer"
            onClick={onNextDate}
          />
        </div>
      </div>

      {classResponse?.isFetching ? (
        <div className="w-full">
          {[...Array(2)].map((_, index) => (
            <ClassEventCardSkeleton key={index} />
          ))}
        </div>
      ) : classes.length > 0 ? (
        <div className="w-full">
          {classes.map((event: any, index: number) => (
            <ClassEventCard
              key={event?.Id || `event-${index}`}
              event={event}
              index={index}
            />
          ))}
        </div>
      ) : (
        <div className="rounded-2xl flex flex-col w-full items-center justify-center">
          <h2 className="font-medium text-4 text-color-grey-1">No Classes</h2>
        </div>
      )}
    </div>
  )
}

export default DailyClassesList
