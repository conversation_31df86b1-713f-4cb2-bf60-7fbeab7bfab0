'use client'
import React, { useEffect, useRef, useState } from 'react'
import DatePickerMonth from '../DatePickerMonth/DatePickerMonth'
import dayjs from 'dayjs'
import { useApi } from '@/hooks/useApi'
import { getAllMeetings } from '@/lib/actions/class.actions'
import DailyClassesList from './DailyClassesList'
import {
  selectYouthEventsByMonth,
  setYouthMonthlyEvents,
  useDispatch,
  useSelector,
} from '@/lib/redux'
import UpcomingClassesDashboard from '../UpcomingClassesDashboard/UpcomingClassesDashboard'
import { useAuth } from '@/contexts/AuthContext'

const YouthStudentEvents = () => {
  const [selectedDate, setSelectedDate] = useState(dayjs())
  const [currentMonth, setCurrentMonth] = useState(dayjs())
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const dispatch = useDispatch()
  const debounceRef = useRef<NodeJS.Timeout | null>(null)
  const [classResponseMonth, fetchClassDataMonth] = useApi(
    (accessToken: string, startDate: string, endDate: string) =>
      getAllMeetings(accessToken, startDate, endDate),
  )
  const user = useAuth()

  const cachedMonthlyEvents = useSelector(selectYouthEventsByMonth)

  useEffect(() => {
    if (classResponseMonth.isSuccess) {
      const data = classResponseMonth?.data?.data || []
      if (data.length > 0) {
        const responseMonthKey = dayjs(data[0].startDate).format('YYYY-MM')
        const dates = data.map((event: any) => dayjs(event.startDate).date())
        dispatch(
          setYouthMonthlyEvents({ month: responseMonthKey, events: dates }),
        )
      }

      setIsLoading(false)
    }
  }, [classResponseMonth])

  useEffect(() => {
    if (debounceRef.current) {
      clearTimeout(debounceRef.current)
    }

    const currentKey = currentMonth.format('YYYY-MM')

    if (!cachedMonthlyEvents[currentKey]) {
      setIsLoading(true)
    }

    debounceRef.current = setTimeout(() => {
      const nextMonth = currentMonth.add(1, 'month')
      const nextKey = nextMonth.format('YYYY-MM')

      const currentStart = currentMonth.startOf('month').format('YYYY-MM-DD')
      const currentEnd = currentMonth.endOf('month').format('YYYY-MM-DD')

      const nextStart = nextMonth.startOf('month').format('YYYY-MM-DD')
      const nextEnd = nextMonth.endOf('month').format('YYYY-MM-DD')

      if (!cachedMonthlyEvents[currentKey]) {
        setIsLoading(true)
        fetchClassDataMonth(currentStart, currentEnd, 'All')
      } else {
        setIsLoading(false)
      }

      if (!cachedMonthlyEvents[nextKey]) {
        fetchClassDataMonth(nextStart, nextEnd, 'All')
      }
    }, 300)

    return () => {
      if (debounceRef.current) clearTimeout(debounceRef.current)
    }
  }, [currentMonth])

  return (
    <div className="flex flex-col gap-5">
      <UpcomingClassesDashboard role={user?.user?.role} />
      <DatePickerMonth
        currentMonth={currentMonth}
        setCurrentMonth={setCurrentMonth}
        selectedDate={selectedDate}
        setSelectedDate={setSelectedDate}
        highlightedDates={cachedMonthlyEvents[currentMonth.format('YYYY-MM')]}
        isLoading={isLoading}
      />
      <DailyClassesList
        selectedDate={selectedDate}
        setSelectedDate={setSelectedDate}
      />
    </div>
  )
}

export default YouthStudentEvents
