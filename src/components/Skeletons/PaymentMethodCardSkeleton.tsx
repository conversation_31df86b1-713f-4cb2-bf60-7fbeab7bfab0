import React from 'react'

const PaymentMethodCardSkeleton = () => {
  return (
    <div className="w-full animate-pulse rounded-lg border border-gray-200 bg-white p-6 shadow-md">
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <div className="flex items-start justify-between">
            <div className="w-1/2">
              <div className="mb-1 h-4 w-12 rounded bg-gray-200"></div>
              <div className="h-5 w-4/5 rounded bg-gray-300"></div>
            </div>
            <div className="w-1/4">
              <div className="mb-1 h-4 w-10 rounded bg-gray-200"></div>
              <div className="h-5 w-full rounded bg-gray-300"></div>
            </div>
          </div>
        </div>
        <div className="ml-6 h-8 w-16 rounded-md bg-gray-300"></div>
      </div>
    </div>
  )
}

export default PaymentMethodCardSkeleton
