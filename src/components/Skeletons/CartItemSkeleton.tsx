import React from 'react'

const CartItemSkeleton = ({ showBorder }: { showBorder?: boolean }) => {
  return (
    <div
      className={`grid grid-cols-[auto,1fr,auto,auto,auto] items-center gap-x-6 py-5 px-4 md:px-10 ${showBorder ? 'border-b-2 border-color-grey-2' : ''} animate-pulse`}
    >
      {/* Image Skeleton */}
      <div className="w-28 h-20 bg-gray-300 rounded-md"></div>

      {/* Title Skeleton */}
      <div className="space-y-2">
        <div className="h-6 bg-gray-300 rounded w-3/5"></div>
      </div>

      {/* Unit Price Skeleton */}
      <div className="hidden md:block w-20 h-6 bg-gray-300 rounded"></div>

      {/* Sub Total Skeleton */}
      <div className="hidden md:block w-20 h-6 bg-gray-300 rounded"></div>

      {/* Delete Icon Skeleton */}
      <div className="w-8 h-8 bg-gray-300 rounded-full"></div>
    </div>
  )
}

export default CartItemSkeleton
