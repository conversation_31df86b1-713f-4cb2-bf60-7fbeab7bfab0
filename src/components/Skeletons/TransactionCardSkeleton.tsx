import React from 'react'

const TransactionCardSkeleton = ({
  type = 'Credit',
  index = 0,
}: {
  type?: 'All' | 'Credit' | 'Debit'
  index?: number
}) => {
  const getAmountColor = () => {
    if (type === 'All') {
      return index % 2 === 0 ? 'bg-green-300' : 'bg-red-300'
    }
    return type === 'Credit' ? 'bg-green-300' : 'bg-red-300'
  }

  return (
    <div className="grid grid-cols-4 gap-4 px-5 py-3 border-b last:border-b-0 items-center w-full animate-pulse">
      <div className="flex items-center gap-2">
        <div className="h-6 w-6 rounded-full bg-gray-300 shrink-0"></div>
        <div className="h-4 w-16 bg-gray-200 rounded"></div>
      </div>
      <div className="h-4 w-12 bg-gray-200 rounded"></div>
      <div className="h-4 w-20 bg-gray-200 rounded"></div>
      <div className="h-4 w-24 bg-gray-200 rounded"></div>
    </div>
  )
}

export default TransactionCardSkeleton
