import React from 'react'

const UpcomingClassEventSkeleton = () => {
  return (
    <div className="border-l-4 border-l-gray-300 grid grid-cols-10 border-2 border-gray-200 p-6 animate-pulse">
      {/* Date section - matches col-span-3 */}
      <div className="col-span-3 text-center space-y-2">
        <div className="h-4 bg-gray-200 rounded w-8 mx-auto"></div>
        <div className="h-12 bg-gray-200 rounded w-12 mx-auto"></div>
      </div>

      {/* Content section - matches col-span-7 */}
      <div className="col-span-7 space-y-3">
        {/* Time */}
        <div className="h-4 bg-gray-200 rounded w-24"></div>

        {/* Class title */}
        <div className="h-6 bg-gray-200 rounded w-48"></div>

        {/* Location */}
        <div className="flex items-center space-x-2">
          <div className="w-4 h-4 bg-gray-200 rounded"></div>
          <div className="h-4 bg-gray-200 rounded w-36"></div>
        </div>
      </div>
    </div>
  )
}

export default UpcomingClassEventSkeleton
