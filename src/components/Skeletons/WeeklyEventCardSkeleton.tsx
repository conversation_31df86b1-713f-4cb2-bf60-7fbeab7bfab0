import React from 'react'

const WeeklyEventCardSkeleton = () => {
  return (
    <div className="bg-white rounded-xl p-6 shadow-sm border animate-pulse">
      {/* Header with image placeholder */}
      <div className="w-full h-40 bg-gray-200 rounded-lg mb-4"></div>

      {/* Event title */}
      <div className="space-y-2 mb-4">
        <div className="h-6 bg-gray-200 rounded w-3/4"></div>
        <div className="h-4 bg-gray-200 rounded w-1/2"></div>
      </div>

      {/* Date and time */}
      <div className="flex items-center space-x-4 mb-4">
        <div className="w-8 h-8 bg-gray-200 rounded"></div>
        <div className="space-y-1">
          <div className="h-4 bg-gray-200 rounded w-24"></div>
          <div className="h-3 bg-gray-200 rounded w-20"></div>
        </div>
      </div>

      {/* Location */}
      <div className="flex items-center space-x-4 mb-4">
        <div className="w-8 h-8 bg-gray-200 rounded"></div>
        <div className="h-4 bg-gray-200 rounded w-32"></div>
      </div>

      {/* Price and button */}
      <div className="flex items-center justify-between">
        <div className="h-6 bg-gray-200 rounded w-16"></div>
        <div className="h-10 bg-gray-200 rounded w-24"></div>
      </div>
    </div>
  )
}

export default WeeklyEventCardSkeleton
