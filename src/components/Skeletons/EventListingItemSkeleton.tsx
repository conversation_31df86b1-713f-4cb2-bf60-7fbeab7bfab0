import React from 'react'

const EventListingItemSkeleton = () => {
  return (
    <div className="flex items-start gap-x-6 py-6 border-b border-gray-200 animate-pulse">
      {/* Date Skeleton */}
      <div className="flex flex-col items-center w-16 shrink-0">
        <div className="h-4 bg-gray-300 rounded w-10 mb-1"></div>
        <div className="h-7 bg-gray-300 rounded w-14"></div>
      </div>

      {/* Image Skeleton */}
      <div className="w-56 h-40 bg-gray-300 rounded-lg shrink-0"></div>

      {/* Content Skeleton */}
      <div className="flex-1 space-y-3 pt-2">
        <div className="h-4 bg-gray-300 rounded w-1/3"></div>
        <div className="h-7 bg-gray-300 rounded w-1/2"></div>
        <div className="space-y-2 mt-4">
          <div className="h-4 bg-gray-300 rounded w-full"></div>
          <div className="h-4 bg-gray-300 rounded w-5/6"></div>
        </div>
        <div className="h-5 bg-gray-300 rounded w-1/4 mt-4"></div>
      </div>
    </div>
  )
}

export default EventListingItemSkeleton
