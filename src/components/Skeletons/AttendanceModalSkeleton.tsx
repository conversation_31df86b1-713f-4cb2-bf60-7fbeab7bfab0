import React from 'react'

const AttendanceModalSkeleton = () => {
  return (
    <div className="space-y-4 animate-pulse">
      {/* Table skeleton */}
      <table className="w-full border-collapse">
        <thead>
          <tr>
            <th className="border p-2 text-left">
              <div className="h-5 bg-gray-200 rounded w-16"></div>
            </th>
            <th className="border p-2 text-left">
              <div className="h-5 bg-gray-200 rounded w-20"></div>
            </th>
            <th className="border p-2 text-left">
              <div className="h-5 bg-gray-200 rounded w-16"></div>
            </th>
          </tr>
        </thead>
        <tbody>
          {/* Adults row */}
          <tr>
            <td className="border p-2">
              <div className="flex items-center gap-2">
                <div className="h-4 bg-gray-200 rounded w-12"></div>
                <div className="w-4 h-4 bg-gray-200 rounded-full"></div>
              </div>
            </td>
            <td className="border p-2">
              <div className="h-9 bg-gray-200 rounded w-full"></div>
            </td>
            <td className="border p-2">
              <div className="h-9 bg-gray-200 rounded w-full"></div>
            </td>
          </tr>

          {/* Youth row */}
          <tr>
            <td className="border p-2">
              <div className="flex items-center gap-2">
                <div className="h-4 bg-gray-200 rounded w-10"></div>
                <div className="w-4 h-4 bg-gray-200 rounded-full"></div>
              </div>
            </td>
            <td className="border p-2">
              <div className="h-9 bg-gray-200 rounded w-full"></div>
            </td>
            <td className="border p-2">
              <div className="h-9 bg-gray-200 rounded w-full"></div>
            </td>
          </tr>

          {/* Paid Teachers row */}
          <tr>
            <td className="border p-2">
              <div className="flex items-center gap-2">
                <div className="h-4 bg-gray-200 rounded w-44"></div>
                <div className="w-4 h-4 bg-gray-200 rounded-full"></div>
              </div>
            </td>
            <td className="border p-2">
              <div className="h-9 bg-gray-200 rounded w-full"></div>
            </td>
            <td className="border p-2">
              <div className="h-9 bg-gray-200 rounded w-full"></div>
            </td>
          </tr>

          {/* Unpaid Teachers row */}
          <tr>
            <td className="border p-2">
              <div className="flex items-center gap-2">
                <div className="h-4 bg-gray-200 rounded w-48"></div>
                <div className="w-4 h-4 bg-gray-200 rounded-full"></div>
              </div>
            </td>
            <td className="border p-2">
              <div className="h-9 bg-gray-200 rounded w-full"></div>
            </td>
            <td className="border p-2">
              <div className="h-9 bg-gray-200 rounded w-full"></div>
            </td>
          </tr>
        </tbody>
      </table>

      {/* Notes section skeleton */}
      <div className="space-y-2">
        <div className="h-4 bg-gray-200 rounded w-12"></div>
        <div className="h-20 bg-gray-200 rounded w-full"></div>
      </div>
    </div>
  )
}

export default AttendanceModalSkeleton
