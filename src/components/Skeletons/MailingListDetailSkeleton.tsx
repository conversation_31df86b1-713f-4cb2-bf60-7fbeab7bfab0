import React from 'react'

const MailingListDetailSkeleton = () => {
  return (
    <div className="space-y-4 animate-pulse">
      {/* Header with green background */}
      <div className="bg-green-50 border border-green-200 rounded-xl p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gray-200 rounded-lg"></div>
            <div className="space-y-2">
              <div className="h-5 bg-gray-200 rounded w-24"></div>
              <div className="h-4 bg-gray-200 rounded w-32"></div>
            </div>
          </div>
          <div className="h-8 bg-gray-200 rounded w-20"></div>
        </div>
      </div>

      {/* Email list */}
      <div className="space-y-3">
        {[...Array(5)].map((_, index) => (
          <div
            key={index}
            className="flex items-center justify-between p-3 border-b border-gray-100"
          >
            <div className="h-4 bg-gray-200 rounded w-40"></div>
            <div className="flex items-center space-x-3">
              <div className="h-8 bg-blue-200 rounded w-16"></div>
              <div className="w-5 h-5 bg-gray-200 rounded"></div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

export default MailingListDetailSkeleton
