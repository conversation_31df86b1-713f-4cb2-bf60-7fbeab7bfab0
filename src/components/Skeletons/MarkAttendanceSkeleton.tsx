import React from 'react'

const MarkAttendanceSkeleton = () => {
  return (
    <div className="animate-pulse">
      {/* Student rows */}
      {[...Array(5)].map((_, index) => (
        <div
          key={index}
          className="grid grid-cols-10 gap-x-3 border-b border-color-grey-2 py-3"
        >
          {/* Checkbox column */}
          <div className="flex items-center col-span-1 space-x-3">
            <div className="w-4 h-4 bg-gray-200 rounded"></div>
          </div>

          {/* Name column */}
          <div className="col-span-3">
            <div className="h-6 bg-gray-200 rounded w-32"></div>
          </div>

          {/* Attendance buttons column */}
          <div className="flex space-x-3 col-span-6">
            {/* Online button */}
            <div className="px-4 py-2 border h-8 flex items-center justify-center gap-1 rounded border-gray-200">
              <div className="w-3 h-3 bg-gray-200 rounded-full"></div>
              <div className="h-3 bg-gray-200 rounded w-10"></div>
            </div>
            {/* In Person button */}
            <div className="px-4 py-2 border h-8 flex items-center justify-center gap-1 rounded border-gray-200">
              <div className="w-3 h-3 bg-gray-200 rounded-full"></div>
              <div className="h-3 bg-gray-200 rounded w-14"></div>
            </div>
            {/* Absent button */}
            <div className="px-4 py-2 border h-8 flex items-center justify-center gap-1 rounded border-gray-200">
              <div className="w-3 h-3 bg-gray-200 rounded-full"></div>
              <div className="h-3 bg-gray-200 rounded w-10"></div>
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}

export default MarkAttendanceSkeleton
