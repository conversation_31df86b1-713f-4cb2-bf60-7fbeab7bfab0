'use client'
import useModal from '@/hooks/useModal'
import Image from 'next/image'
import React, { useEffect, useState, memo } from 'react'
import AddRecipientModal from './AddRecipientModal'
import { CartItemProps } from '@/interfaces/cart'
import Link from 'next/link'

const CartItem = ({
  item,
  price,
  setSubTotal,
  setTotalAmount,
}: CartItemProps) => {
  const [selectedQty, setSelectedQty] = useState(1)
  const [modal, showModal] = useModal()
  const handleModal = () => {
    showModal({
      title: '',
      contentFn: (onClose) => <AddRecipientModal onClose={onClose} />,
      closeOnClickOutside: true,
      size: 'md',
      showCloseButton: false,
    })
  }
  const handleQuantity = (type: string) => {
    if (type == 'minus' && selectedQty == 1) return
    setTotalAmount((prev: number) => prev - selectedQty * item?.Unit_Price__c)
    if (type === 'minus' && selectedQty > 1) {
      setSelectedQty(selectedQty - 1)
    } else if (type === 'plus') {
      setSelectedQty(selectedQty + 1)
    }
  }

  useEffect(() => {
    setSubTotal(selectedQty * price)
  }, [selectedQty])
  return (
    <div className="flex items-center gap-5">
      {item?.Image__c && (
        <Link href={`/classes/${item?.Class__c}`}>
          <Image
            width={45}
            height={45}
            src={item?.Image__c}
            alt="User avatar"
            className="w-28 h-20 object-cover rounded-lg"
          />
        </Link>
      )}
      <div className="flex flex-col gap-2">
        <Link href={`/classes/${item?.Class__c}`}>
          <h1 className="text-base font-bold">{item?.Name}</h1>
        </Link>
        <h2 className="block text-sm md:hidden">Unit Price: ${price}</h2>
        <div className="flex items-center gap-1 flex-col md:flex-row">
          {item?.Family == 'Donation' && (
            <div className="w-full md:w-max">
              <div className="flex items-center w-max border border-color-grey-2 rounded-md">
                <div
                  className="px-2 py-1 h-8 w-9 flex items-center cursor-pointer justify-between border-r-2 border-color-grey-2"
                  onClick={() => handleQuantity('minus')}
                >
                  -
                </div>
                <input
                  type="text"
                  className="w-9 h-8 flex items-center justify-between px-2 text-sm font-semibold"
                  value={selectedQty}
                ></input>
                <div
                  className="px-2 py-1 h-8 w-9 flex items-center cursor-pointer justify-between border-l-2 border-color-grey-2"
                  onClick={() => handleQuantity('plus')}
                >
                  +
                </div>
              </div>
            </div>
          )}
          <h2 className="text-sm text-left w-full block md:hidden">
            Sub Total: <strong>${price}</strong>
          </h2>
          {item?.recipients && (
            <span className="text-xs w-full md:w-max font-medium">
              1 to each recipient
            </span>
          )}
        </div>
        {item?.recipients && (
          <div className="text-xs">
            <button
              className="text-color-blue-1 font-semibold"
              onClick={handleModal}
            >
              Edit recipientsp
            </button>
          </div>
        )}
      </div>
      {modal}
    </div>
  )
}

export default memo(CartItem)
