import Image from 'next/image'
import React from 'react'

const AddRecipientModal = ({ onClose }: { onClose: any }) => {
  return (
    <div className="w-full md:w-max">
      <div className="flex items-center justify-between border-b-2 border-color-grey-2 pb-5">
        <div className="flex items-center gap-3">
          <Image
            width={330}
            height={120}
            src="/gift.jpg"
            alt="User avatar"
            className="w-10 h-7"
          />
          <h1 className="text-sm font-bold">The Muse Writers – Gift Card</h1>
        </div>
        <svg
          width="20"
          height="21"
          viewBox="0 0 20 21"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          className="cursor-pointer"
          onClick={() => onClose()}
        >
          <path
            d="M4.16797 4.66699L15.8339 16.3329"
            stroke="#141522"
            strokeWidth="1.25"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M4.1681 16.3329L15.834 4.66699"
            stroke="#141522"
            strokeWidth="1.25"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      </div>
      <div className="flex flex-col mt-3">
        <h2 className="text-sm font-semibold">Recipient Email Address</h2>
        <span className="font-medium text-xs text-color-grey-1">
          Add up to 10 people, and each will receive their own unique gift card
        </span>
      </div>
      <div className="flex items-center mt-3">
        <input
          type="text"
          placeholder="Enter recipient’s email address"
          className="rounded-tl-md rounded-bl-rounded-tl-md w-full h-10 p-xs text-xs border border-color-grey-2"
        ></input>
        <button className="text-white flex items-center justify-center bg-color-black p-xs w-16 h-10 rounded-tr-md rounded-br-md">
          Add
        </button>
      </div>
      <div className="flex items-center mt-3 gap-2 flex-wrap w-full">
        <div className="px-2 py-1 rounded-md flex gap-3 bg-color-grey items-center">
          <h2 className="text-xs font-medium"><EMAIL></h2>
          <span>x</span>
        </div>
        <div className="px-2 py-1 rounded-md flex gap-3 bg-color-grey items-center">
          <h2 className="text-xs font-medium"><EMAIL></h2>
          <span>x</span>
        </div>
        <div className="px-2 py-1 rounded-md flex gap-3 bg-color-grey items-center">
          <h2 className="text-xs font-medium"><EMAIL></h2>
          <span>x</span>
        </div>
      </div>
      <button className="w-full h-10 bg-color-yellow px-6 py-4 rounded-xl text-sm font-semibold flex items-center justify-center mt-3">
        Save
      </button>
    </div>
  )
}

export default AddRecipientModal
