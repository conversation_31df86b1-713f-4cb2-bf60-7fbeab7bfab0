'use client'
import React, { useEffect, useRef, useState, memo } from 'react'
import CartItem from './CartItem'
import EditCartIcon from '../Icons/EditCartIcon'
import DeleteIcon from '../Icons/DeleteIcon'
import { removeFromCart } from '@/lib/actions/cart.actions'
import { useApi } from '@/hooks/useApi'
import { useDispatch } from 'react-redux'
import { decreaseCartCount } from '@/lib/redux'

const CartProduct = ({
  item,
  setTotalAmount,
  setCartData,
}: {
  item: any
  setTotalAmount: any
  setCartData: any
}) => {
  const [isEdit, setIsEdit] = useState<boolean>(false)
  const inputRef = useRef<HTMLDivElement>(null)
  const [subTotal, setSubTotal] = useState<number>(item?.Unit_Price__c)
  const dispatch = useDispatch()
  const [removeCartResponse, removeCart] = useApi((access: string) =>
    removeFromCart(access, item?.Id),
  )

  useEffect(() => {
    setTotalAmount((prev: number) => {
      return prev + subTotal
    })
  }, [subTotal])

  useEffect(() => {
    if (isEdit) {
      document.addEventListener('mousedown', handleOutsideClick)
    }
    return () => {
      document.removeEventListener('mousedown', handleOutsideClick)
    }
  }, [isEdit])

  useEffect(() => {
    if (removeCartResponse.isSuccess) {
      setTotalAmount((prev: number) => prev - subTotal)
      setCartData((prev: any) =>
        prev.filter((cart: any) => cart?.Id != item?.Id),
      )
      dispatch(decreaseCartCount())
    }
  }, [removeCartResponse])

  const handleOutsideClick = (event: MouseEvent) => {
    if (inputRef.current && !inputRef.current.contains(event.target as Node)) {
      setIsEdit(false)
    }
  }

  return (
    <div className="grid grid-cols-[90%,10%] md:grid-cols-[70%,15%,15%,5%] py-2 md:py-5 px-4 md:px-10 w-full border-b-1 border-color-grey pb-6">
      <CartItem
        item={item?.Product__r}
        price={item?.Unit_Price__c}
        setSubTotal={setSubTotal}
        setTotalAmount={setTotalAmount}
      />
      <div className="gap-2 hidden md:flex">
        {isEdit ? (
          <>
            <input
              type="number"
              value={item?.Unit_Price__c}
              className="w-16 h-9 text-right text-sm font-semibold border border-color-grey-2 rounded-md"
              onBlur={() => setIsEdit(false)}
            />
          </>
        ) : (
          <span className="text-sm font-medium leading-5 w-full flex items-center justify-center">
            ${item?.Unit_Price__c}
          </span>
        )}
        {item?.Product__r?.Family == 'Donation' && !isEdit && (
          <div onClick={() => setIsEdit(true)}>
            <EditCartIcon />
          </div>
        )}
      </div>
      <span className="text-sm font-medium leading-5 hidden w-full md:flex items-center justify-center">
        ${subTotal}
      </span>
      <div className="w-full flex items-center">
        {removeCartResponse.isFetching ? (
          <div className="delete-icon-loader"></div>
        ) : (
          <DeleteIcon handleClick={removeCart} />
        )}
      </div>
    </div>
  )
}

export default memo(CartProduct)
