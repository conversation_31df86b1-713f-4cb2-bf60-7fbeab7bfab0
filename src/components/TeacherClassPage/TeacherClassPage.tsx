'use client'
import Link from 'next/link'
import React, { useEffect } from 'react'
import ClassCard from '../ClassCard/ClassCard'
import { useApi } from '@/hooks/useApi'
import { getTeacherClasses } from '@/lib/actions/teacher.actions'
import InfiniteScroll from 'react-infinite-scroll-component'
import { PAGINATION_LIMIT } from '@/utils/constants'
import ClassGridSkeleton from '../Skeleton/ClassGridSkeleton'

type ClassType = 'Attendance Pending' | 'Active' | 'Previous'

const TeacherClassPage = ({ classType }: { classType: string }) => {
  const [hasMore, setHasMore] = React.useState(true)
  const [classesData, setClassesData] = React.useState<any[]>([])
  const offset = React.useRef(0)
  const [classResponse, fetchTeacherClasses] = useApi(
    (access: string, type: ClassType, limit: number, offset: number) =>
      getTeacherClasses(access, type, limit, offset),
  )

  useEffect(() => {
    setClassesData([])
    offset.current = 0
    setHasMore(true)
    fetchClasses()
  }, [classType])

  useEffect(() => {
    if (classResponse.isSuccess) {
      const moreOrders = classResponse?.data?.data?.data || []
      if (moreOrders.length > 0) {
        setClassesData((prev: any) => [...prev, ...moreOrders])
        offset.current += PAGINATION_LIMIT
        if (
          classResponse?.data?.data?.total <=
          classesData.length + moreOrders.length
        ) {
          setHasMore(false)
        }
      } else {
        setHasMore(false)
      }
    }
  }, [classResponse])

  const fetchClasses = () => {
    if (!hasMore) return
    fetchTeacherClasses(classType, PAGINATION_LIMIT, offset.current)
  }

  if (classResponse.isFetching && classesData.length === 0) {
    return <ClassGridSkeleton />
  }

  return (
    <div className="w-full flex flex-col justify-center gap-5">
      <InfiniteScroll
        dataLength={classesData.length}
        next={fetchClasses}
        hasMore={hasMore}
        loader={<ClassGridSkeleton />}
        endMessage={
          <div className="flex items-center font-bold justify-center my-10 text-color-grey-1">
            No more classes to show
          </div>
        }
        scrollableTarget="scrollableDiv"
        pullDownToRefreshThreshold={50}
      >
        <div className="p-3 md:p-7 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
          {classesData.map((item: any, index: number) => (
            <Link href={`/classes/${item?.Id}`} key={index}>
              <ClassCard
                data={item}
                showRating={false}
                showTeachers={false}
                showDetails={true}
                showProgress={index === 0 || index === 8}
                border={true}
              />
            </Link>
          ))}
        </div>
      </InfiniteScroll>
    </div>
  )
}

export default TeacherClassPage
