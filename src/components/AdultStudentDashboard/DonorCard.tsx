'use client'

import React from 'react'
import Link from 'next/link'

const DonorBanne<PERSON> = ({ isDonor }: { isDonor: boolean }) => {
  return (
    <div className="w-full bg-neutral-white rounded-xl p-5 shadow-sm">
      {isDonor ? (
        <p className="text-center text-xl font-semibold">
          Thanks for being a{' '}
          <Link href="/donations">
            {' '}
            <span className="text-color-blue-6 mr-1">donor!</span>
          </Link>{' '}
          🎉
        </p>
      ) : (
        <p className="text-center text-xl font-medium">
          ✍️ Your support writes the next chapter —{' '}
          <Link href="/donations">
            <span className="text-color-blue-6 mr-1">become a donor</span>
          </Link>{' '}
          today.
        </p>
      )}
    </div>
  )
}

export default DonorBanner
