import { ROLES } from '@/types/enum'
import AdultStudentEvents from '../AdultStudentEvents/AdultStudentEvents'
import PageHeader from '../PageHeader/PageHeader'
import QuotesCard from '../QuotesCard/QuotesCard'
import RightColumnHoc from '../RightColumnHoc/RightColumnHoc'
import QuoteCard from './AdultStudentQuoteCard'
import DonorBanner from './DonorCard'
import FavouriteTeachers from './FavouriteTeachersSection'
import AdultStudentJourney from './JourneyCard'
import OngoingClassesSection from './OngoingClassesSection'
import TryNewSection from './TryNewSection'

const AdultStudentDashboard = ({ userData }: { userData: any }) => {
  return (
    <div className="flex flex-col sm:flex-row w-full">
      <div className="w-full flex-1">
        {userData && (
          <PageHeader
            heading={`Hey, ${userData?.name}!`}
            backgroundColor="#F5F5F7"
            showMessage={false}
            showSearch={false}
            showCart={userData?.role != ROLES.Teacher}
            userData={userData}
          />
        )}
        <div className="flex flex-col gap-5 p-3 md:pr-0">
          <QuotesCard type="Adult Student" />
          <AdultStudentJourney />
          <DonorBanner isDonor={userData?.roles?.includes('Donor')} />
          <div className="mt-5">
            <OngoingClassesSection />
          </div>
          <div className="mt-5">
            <FavouriteTeachers />
          </div>
          <div className="mt-5">
            <TryNewSection />
          </div>
        </div>
      </div>
      <div className="md:w-96 p-3 shrink-0">
        <RightColumnHoc>
          <AdultStudentEvents />
        </RightColumnHoc>
      </div>
    </div>
  )
}

export default AdultStudentDashboard
