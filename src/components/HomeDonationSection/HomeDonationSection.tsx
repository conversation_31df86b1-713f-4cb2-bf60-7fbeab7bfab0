'use client'
import Image from 'next/image'
import { useEffect, useState } from 'react'

export default function HomeDonationSection() {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })
  const [show, setShow] = useState(false)

  useEffect(() => {
    const handleScroll = () => setShow(false)
    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const handleMouseMove = (e: any) => {
    setShow(true)
    const rect = e.currentTarget.getBoundingClientRect()
    const x = e.clientX - rect.left
    const y = e.clientY - rect.top
    setMousePosition({ x, y })
  }

  return (
    <section
      className="relative px-6 sm:px-10 md:px-16 lg:px-24 xl:px-40 py-12 md:py-16 lg:py-20 bg-donation-gradient"
      onMouseMove={handleMouseMove}
      onMouseLeave={() => setShow(false)}
      onMouseEnter={() => setShow(true)}
    >
      {/* Circle animation */}
      {show && (
        <div
          className="absolute pointer-events-none transition-opacity duration-500 opacity-30"
          style={{
            top: mousePosition.y - 50,
            left: mousePosition.x - 50,
            width: '100px',
            height: '100px',
            background:
              'radial-gradient(circle, rgba(202,229,255,0.7) 0%, rgba(252,214,198,0.7) 70%, rgba(255,255,255,0) 100%)',
            borderRadius: '50%',
          }}
        />
      )}

      <div className="gradient-bg w-full p-6 sm:p-8 md:p-12 lg:p-16 flex flex-col xl:flex-row items-center justify-between relative z-10 gap-10">
        {/* Text Section */}
        <div className="max-w-xl text-center md:text-left">
          <h3 className="text-brown-800 uppercase text-xs sm:text-sm md:text-base tracking-[6px] sm:tracking-[8px] md:tracking-[9px] mb-3 sm:mb-4 font-bold">
            MAKE A DONATION
          </h3>

          <h2 className="font-bold text-3xl sm:text-4xl md:text-5xl lg:text-6xl mb-4">
            Your Gift Makes Stories Come to Life
          </h2>

          <p className="text-gray-700 text-base sm:text-lg md:text-xl lg:text-2xl mb-8">
            With your contributions, more teen and adult writers can join our
            creative writing classes. We are the only center in the nation to
            offer tuition assistance to anyone who wants to take a class but
            cannot afford one.
          </p>

          <a
            href="#"
            className="inline-flex items-center bg-black text-base sm:text-lg md:text-xl lg:text-2xl text-white px-5 sm:px-6 py-3 rounded-full font-bold hover:bg-gray-800 transition duration-200"
          >
            Donate Now
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5 ml-2"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                clipRule="evenodd"
              />
            </svg>
          </a>
        </div>

        {/* Image Section */}
        <div className="relative w-56 h-56 sm:w-72 sm:h-72 md:w-80 md:h-80 lg:w-96 lg:h-96">
          <div className="absolute inset-0 rounded-full overflow-hidden border border-gray-100 shadow-lg">
            <Image
              src={'/typewritter.png'}
              alt="Donation"
              width={400}
              height={400}
              className="object-cover w-full h-full floating-pulse transform transition-all duration-500 ease-in-out hover:scale-110 hover:rotate-6"
            />
          </div>
        </div>
      </div>
    </section>
  )
}
