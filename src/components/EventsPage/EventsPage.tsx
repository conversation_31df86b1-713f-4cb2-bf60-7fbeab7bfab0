'use client'
import React, { useCallback, useEffect, useState } from 'react'
import Search from '../Icons/Search'
import ListIcon from '../Icons/ListIcon'
import CalendarIcon from '../Icons/CalendarIcon'
import { useIsMobile } from '@/hooks/useIsMobile'
import EventCalendarView from '../EventCalendarView/EventCalendarView'
import { usePathname, useSearchParams } from 'next/navigation'
import { getEvents } from '@/lib/actions/class.actions'
import CustomDropDown from '../CustomComponents/CustomDropDown/CustomDropDown'
import Sort from '../Icons/Sort'
import { capitalizeFirstLetter, debounce } from '@/utils/utils'
import EventListingItemSkeleton from '../Skeletons/EventListingItemSkeleton'
import EventListing1 from '../EventListing/EventListing1'

const EventsPage = ({
  userData,
  eventType,
  categoryDropdownValues = [],
}: {
  userData: any
  eventType: any
  categoryDropdownValues: any
}) => {
  const pathname = usePathname()
  const pagename = capitalizeFirstLetter(pathname.split('/')[1])
  const searchParams = useSearchParams()
  const paramsType: string | null = searchParams.get('type')
  const categoryParamsType: string | null = searchParams.get('EventType')
  const subCategoryParamsType: string | null = searchParams.get('SubCategory')
  const recordTypeParamsType: string | null = searchParams.get('recordType')
  const eventClubCategoryParamsType: string | null =
    searchParams.get('Category')
  const isMobile = useIsMobile()
  const [eventData, setEventData] = useState<any>(null)
  const [loading, setLoading] = useState<boolean>(true)
  const getView = searchParams.get('view')
  const [showEventListing, setShowEventListing] = useState<boolean>(
    getView != 'calender',
  )
  const [selectedType, setSelectedType] = useState<string>(
    paramsType ? paramsType : '',
  )

  const [selectedEventCategoryType, setSelectedEventCategoryType] = useState<
    string[]
  >(categoryParamsType ? categoryParamsType?.split(',') : [])
  const [selectedEventClubType, setSelectedEventClubType] = useState<any>(
    recordTypeParamsType ? recordTypeParamsType : null,
  )
  const [selectedSubCategoryType, setSelectedSubCategoryType] = useState<
    string[]
  >(subCategoryParamsType ? subCategoryParamsType?.split(',') : [])

  const [selectedEventClubCategory, setSelectedEventClubCategory] = useState<
    string[]
  >(eventClubCategoryParamsType ? eventClubCategoryParamsType?.split(',') : [])

  const [search, setSearch] = useState<string>('')
  const classType = [
    {
      label: 'Hybrid',
      value: 'Hybrid',
    },
    {
      label: 'In-Person',
      value: 'In-Person',
    },
    {
      label: 'Online',
      value: 'Online',
    },
  ]

  // const eventOutReachType = [
  //   {
  //     label: 'All',
  //     value: 'All',
  //   },
  //   {
  //     label: 'Event',
  //     value: 'Event',
  //   },
  //   {
  //     label: 'Outreach',
  //     value: 'Outreach',
  //   },
  //   {
  //     label: 'Club',
  //     value: 'Club',
  //   },
  // ]

  useEffect(() => {
    fetchData()
  }, [selectedType, selectedEventCategoryType, search])

  useEffect(() => {
    if (isMobile) {
      setShowEventListing(true)
    } else {
      setShowEventListing(getView != 'calender')
    }
  }, [isMobile, getView])

  const updateView = useCallback(
    (view: string, showListing: boolean) => {
      setShowEventListing(showListing)
      const params = new URLSearchParams(searchParams.toString())
      params.set('view', view)
      const newUrl = `${window.location.pathname}?${params.toString()}`
      window.history.replaceState(null, '', newUrl)
    },
    [searchParams, setShowEventListing],
  )

  const handleListViewClick = () => updateView('list', true)
  const handleCalendarViewClick = () => updateView('calender', false)

  const fetchData = async () => {
    setLoading(true)
    try {
      const data = (
        await getEvents(
          'Current',
          selectedEventClubType
            ? [selectedEventClubType, ...selectedEventClubCategory]
            : selectedEventClubCategory,
          selectedEventCategoryType,
          selectedSubCategoryType,
          search,
          selectedType,
        )
      )?.data?.data
      setEventData(data)
    } catch (error) {
      console.error('Error fetching events:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleFilter = useCallback(
    (value: string, filterType: string) => {
      const url = new URL(window.location.href)
      switch (filterType) {
        case 'Record type':
          const selectedEventClubType = value
          setSelectedEventClubType(selectedEventClubType)
          if (selectedEventClubType)
            url.searchParams.set('recordType', selectedEventClubType)
          else url.searchParams.delete('recordType')
          break
        case 'Meeting type':
          const selectedEventType = value
          setSelectedType(selectedEventType)
          if (selectedEventType) url.searchParams.set('type', selectedEventType)
          else url.searchParams.delete('type')
          break
        case 'Category':
          const newEventClubCategories = selectedEventClubCategory.includes(
            value,
          )
            ? selectedEventClubCategory.filter((item) => item !== value)
            : [...selectedEventClubCategory, value]
          setSelectedEventClubCategory(newEventClubCategories)
          if (newEventClubCategories.length)
            url.searchParams.set('Category', newEventClubCategories.join(','))
          else url.searchParams.delete('Category')
          break
        case 'Event type':
          const newCategories = selectedEventCategoryType.includes(value)
            ? selectedEventCategoryType.filter((item) => item !== value)
            : [...selectedEventCategoryType, value]
          setSelectedEventCategoryType(newCategories)
          if (newCategories.length)
            url.searchParams.set('EventType', newCategories.join(','))
          else url.searchParams.delete('EventType')
          break
        case 'Sub-Category type':
          const newSubCategories = selectedSubCategoryType.includes(value)
            ? selectedSubCategoryType.filter((item) => item !== value)
            : [...selectedSubCategoryType, value]
          setSelectedSubCategoryType(newSubCategories)
          if (newSubCategories.length)
            url.searchParams.set('SubCategory', newSubCategories.join(','))
          else url.searchParams.delete('SubCategory')
          break
        default:
          break
      }

      window.history.pushState({}, '', url.toString())
    },
    [selectedType, selectedEventCategoryType, selectedEventClubType],
  )

  const handleSearch = (e: any) => {
    setSearch(e.target.value)
  }

  const debounceSearch = debounce(handleSearch, 1000)

  useEffect(() => {
    const paramsType = searchParams.get('type')
    const categoryParamsType = searchParams.get('EventType')
    setSelectedType(paramsType ? paramsType : '')
    setSelectedEventCategoryType(
      categoryParamsType ? categoryParamsType.split(',') : [],
    )
  }, [searchParams])

  return (
    <div className="w-full flex flex-col items-center">
      {!userData && (
        <div className="w-full flex justify-center">
          <div className="font-bold text-3xl px-6 bg-white md:px-7 py-7 text-left sm:w-3/4 w-full">
            {pagename == 'Outreach' ? 'Community & Outreach' : 'Events'}
          </div>
        </div>
      )}
      <div
        className={`${userData ? 'w-full' : 'sm:w-3/4 w-full rounded-b-xl'} bg-white px-6 md:px-7 pb-7 flex flex-wrap items-center md:gap-3 md:flex-row ${!showEventListing ? 'justify-end' : 'justify-between'}`}
      >
        <div className="flex flex-col gap-2 w-full sm:w-auto">
          {userData && (
            <h1 className="text-2xl font-semibold md:hidden mb-4">
              {pagename}
            </h1>
          )}
          {showEventListing && (
            <div className="relative block mb-2">
              <input
                type="text"
                placeholder={`Search for ${pagename.toLowerCase()}`}
                className="h-12 p-5 rounded-xl border text-xs text-color-grey-1 w-full md:w-96"
                onChange={debounceSearch}
              />
              <div
                className="absolute right-5"
                style={{ top: 'calc(50% - 7px)' }}
              >
                <Search width={14.25} height={14.25} color={'#9C9CA4'} />
              </div>
            </div>
          )}
        </div>
        {/* {!isMobile && ( */}
        <div className="flex flex-wrap lg:flex-nowrap items-center justify-between gap-2 md:gap-4 flex-row">
          <CustomDropDown
            icon={<Sort />}
            title={`Meeting type`}
            options={classType}
            column={1}
            handleOptionClick={handleFilter}
            selectedList={selectedType}
            width="w-max"
            isTitleType={true}
          />
          <CustomDropDown
            icon={<Sort />}
            title={'Event type'}
            options={eventType}
            column={2}
            handleOptionClick={handleFilter}
            selectedList={selectedEventCategoryType}
            width="w-max"
            isTitleType={true}
          />
          <CustomDropDown
            icon={<Sort />}
            title={'Category'}
            options={categoryDropdownValues}
            column={2}
            handleOptionClick={handleFilter}
            selectedList={selectedEventClubCategory}
            width="w-max"
            isTitleType={true}
          />
          {/* <CustomDropDown
              icon={<Sort />}
              title={'Sub-Category type'}
              options={eventSubcategories}
              column={2}
              handleOptionClick={handleFilter}
              selectedList={selectedSubCategoryType}
              width="w-max"
              isTitleType={true}
            /> */}
          <button onClick={handleListViewClick} className="w-full">
            <div
              className={`flex items-center gap-2 h-12 p-5 rounded-xl ${showEventListing ? 'bg-color-yellow' : ''} border cursor-pointer`}
            >
              <ListIcon />
              <span className="w-max block text-xs font-semibold">
                List view
              </span>
            </div>
          </button>
          <button onClick={handleCalendarViewClick} className="w-full">
            <div
              className={`flex items-center gap-2 h-12 p-5 w-full rounded-xl ${!showEventListing ? 'bg-color-yellow' : ''} border cursor-pointer`}
            >
              <CalendarIcon />
              <span className="w-max block text-xs font-semibold">
                Month view
              </span>
            </div>
          </button>
        </div>
        {/* )} */}
      </div>

      <div
        className={`flex justify-center px-6 sm:px-0 ${userData ? 'w-full' : 'sm:w-3/4 w-full'}`}
      >
        {showEventListing ? (
          <div className={`w-full mt-5 mb-5 ${userData ? 'sm:mx-5' : ''}`}>
            {loading ? (
              <div className="my-10">
                {[...Array(3)].map((_, index) => (
                  <EventListingItemSkeleton key={index} />
                ))}
              </div>
            ) : (
              <>
                {eventData?.length === 0 ? (
                  <div className="w-full flex justify-center items-center">
                    <h1 className="text-2xl font-semibold text-color-grey-3">
                      No events found
                    </h1>
                  </div>
                ) : (
                  <EventListing1
                    eventData={eventData}
                    type={pagename.toLocaleLowerCase()}
                  />
                )}
              </>
            )}
          </div>
        ) : (
          <EventCalendarView selectedType={selectedType} />
        )}
      </div>
    </div>
  )
}

export default EventsPage
