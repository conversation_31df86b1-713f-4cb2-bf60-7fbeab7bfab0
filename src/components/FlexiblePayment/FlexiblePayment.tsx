import dayjs from 'dayjs'
import CustomButton from '../CustomComponents/CustomButton/CustomButton'
import { X } from 'lucide-react'
import { useApi } from '@/hooks/useApi'
import { checkoutCart, youthCheckoutCart } from '@/lib/actions/cart.actions'
import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useDispatch } from 'react-redux'
import { setCartCount, setPaymentToken } from '@/lib/redux'
import { useAuth } from '@/contexts/AuthContext'
import { COUPON_TYPES, ROLES } from '@/types/enum'
import { toast } from 'react-toastify'
const FlexiblePayment = ({
  totalAmount,
  onClose,
  couponId,
  couponType,
  setCartData,
  setTotalAmount,
}: {
  totalAmount: number
  onClose: () => void
  couponId: string
  couponType: string
  setCartData: (data: any) => void
  setTotalAmount?: (amount: number) => void
}) => {
  const router = useRouter()
  const dispatch = useDispatch()
  const { user } = useAuth()
  const [couponResponse, handleCheckoutCart] = useApi(
    (access: string, isFlexible: boolean, coupon: string, type) =>
      checkoutCart(access, isFlexible, coupon, type),
  )
  const [parentApprovalResponse, sendParentApproval] = useApi(
    (
      access: string,
      isFlexible: boolean,
      couponId: string,
      couponType: COUPON_TYPES,
    ) => youthCheckoutCart(access, isFlexible, couponId, couponType),
  )
  const paymentDates = [
    dayjs(),
    dayjs().add(1, 'month'),
    dayjs().add(2, 'month'),
  ]

  useEffect(() => {
    if (parentApprovalResponse?.isSuccess) {
      toast.success('Parent approval sent successfully', { autoClose: 2000 })
      if (setCartData) {
        setCartData([])
      }
      if (setTotalAmount) {
        setTotalAmount(0)
      }
      dispatch(setCartCount(0))
      onClose()
    }
  }, [parentApprovalResponse])

  useEffect(() => {
    if (couponResponse?.isSuccess) {
      dispatch(setPaymentToken(couponResponse.data?.data?.token))
      if (setCartData) {
        setCartData([])
      }
      if (setTotalAmount) {
        setTotalAmount(0)
      }
      dispatch(setCartCount(0))
      router.push(`/checkout?token=${couponResponse.data?.data?.token}`)
      onClose()
    }
  }, [couponResponse])
  const handleCheckout = () => {
    if (user && user?.role === ROLES.Youth_Student) {
      sendParentApproval(true, couponId, couponType)
    } else {
      handleCheckoutCart(true, couponId, couponType)
    }
  }
  return (
    <div className="max-w-md w-full bg-white rounded-2xl p-6 shadow-md">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-lg text-color-black font-bold leading-7 align-middle mt-1">
          Flexible Payment
        </h2>
        <button
          onClick={onClose}
          className="p-1 hover:bg-color-grey-5 rounded-full transition-colors"
        >
          <X size={20} className="text-color-grey-1" />
        </button>
      </div>

      <p className="text-sm font-normal text-color-black-4 mb-4">
        Muse makes it easy to pay for your classes with flexible options.
        Instead of paying the full amount upfront, you can split your total into
        three equal monthly payments.
      </p>
      <p className="text-sm font-normal text-color-black-4 mb-4">
        Your card will be charged automatically on the following dates:
      </p>

      <div className="space-y-3 mb-4">
        {paymentDates.map((date, index) => (
          <div key={index} className="flex justify-between mb-2">
            <span className="font-semibold">
              ${(totalAmount / 3).toFixed(2)}
            </span>
            <span className="text-sm text-gray-400">
              {date.format('MMM D, YYYY')}
            </span>
          </div>
        ))}
      </div>

      <p className="text-sm text-gray-600 mb-6">
        Payments will be automatically billed.
      </p>

      <CustomButton
        title={'Continue'}
        isLoading={
          couponResponse?.isFetching || parentApprovalResponse?.isFetching
        }
        onClick={handleCheckout}
        height={12}
        isDisabled={false}
      />
    </div>
  )
}

export default FlexiblePayment
