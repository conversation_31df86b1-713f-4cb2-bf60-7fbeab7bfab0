import React, { useEffect, useState } from 'react'
import { X } from 'lucide-react'
import { useApi } from '@/hooks/useApi'
import {
  sendNotification,
  sendNotificationIndividual,
} from '@/lib/actions/teacher.actions'
import { toast } from 'react-toastify'
import { CLASS_TYPES, MAILING_LIST_TYPES } from '@/types/enum'
import CustomButton from '../CustomComponents/CustomButton/CustomButton'

interface NotifyStudentModalProps {
  mailingListId: string
  onClose?: () => void
  classType?: CLASS_TYPES | null
  notifySingle?: boolean
  studentCount?: number
  parentCount?: number
  mailingLists?: Array<any>
  mail?: string
}

const NotifyStudentModal = ({
  mailingListId,
  onClose,
  classType = null,
  notifySingle = false,
  studentCount = 0,
  parentCount = 0,
  mailingLists,
  mail,
}: NotifyStudentModalProps) => {
  const [step, setStep] = useState(1) // Step 1: Selection, Step 2: Notification Form
  const [notificationTarget, setNotificationTarget] = useState<
    'students' | 'parents' | 'both'
  >('students')
  const [emailSubject, setEmailSubject] = useState('')
  const [emailContent, setEmailContent] = useState('')
  const [response, notifyStudent] = useApi(
    (
      accessToken: string,
      mailingListId: string,
      subject: string,
      content: string,
    ) => sendNotification(accessToken, mailingListId, subject, content),
  )
  const [singleNotificationResponse, singleNotification] = useApi(
    (access: string, email: string, subject: string, content: string) =>
      sendNotificationIndividual(access, email, subject, content),
  )

  useEffect(() => {
    if (singleNotificationResponse.isSuccess) {
      toast.success('Notification sent successfully', { autoClose: 2000 })
      setEmailSubject('')
      setEmailContent('')
      if (onClose) {
        onClose()
      }
    }
  }, [singleNotificationResponse])

  useEffect(() => {
    if (response.isSuccess) {
      toast.success('Notification sent successfully', { autoClose: 2000 })
      setEmailSubject('')
      setEmailContent('')
      if (onClose) {
        onClose()
      }
    }
  }, [response, onClose])

  const handleContinue = () => {
    setStep(2)
  }

  const getModalTitle = () => {
    if (notifySingle) {
      return 'Notify Student'
    }
    if (classType !== CLASS_TYPES.YOUTH) {
      return 'Notify Students'
    }

    if (step === 1) {
      return 'Notify Students/Parents'
    }

    switch (notificationTarget) {
      case 'students':
        return 'Notify Students'
      case 'parents':
        return 'Notify Parents'
      case 'both':
        return 'Notify Students/Parents'
      default:
        return 'Notify Students'
    }
  }

  const getTargetCount = () => {
    if (classType !== CLASS_TYPES.YOUTH) {
      return `${studentCount} Students will be notified`
    }

    switch (notificationTarget) {
      case 'students':
        return `${studentCount} Students will be notified`
      case 'parents':
        return `${parentCount} Parents will be notified`
      case 'both':
        return `${studentCount + parentCount} Students and Parents will be notified`
      default:
        return `${studentCount} Students will be notified`
    }
  }

  const handleSendNotification = () => {
    if (notifySingle) {
      singleNotification(mail, emailSubject, emailContent)
    } else {
      let targetMailingListId = mailingListId
      if (mailingLists && classType === CLASS_TYPES.YOUTH) {
        if (notificationTarget === 'students') {
          const studentList = mailingLists.find(
            (list) => list.type === MAILING_LIST_TYPES.STUDENTS,
          )
          targetMailingListId = studentList?.id || mailingListId
        } else if (notificationTarget === 'parents') {
          const parentList = mailingLists.find(
            (list) => list.type === MAILING_LIST_TYPES.PARENTS,
          )
          targetMailingListId = parentList?.id || mailingListId
        }
        if (notificationTarget === 'both') {
          const studentList = mailingLists.find(
            (list) => list.type === MAILING_LIST_TYPES.STUDENTS,
          )
          const parentList = mailingLists.find(
            (list) => list.type === MAILING_LIST_TYPES.PARENTS,
          )

          if (studentList) {
            notifyStudent(studentList.id, 'Email', emailSubject, emailContent)
          }
          if (parentList) {
            notifyStudent(parentList.id, 'Email', emailSubject, emailContent)
          }
          return
        }
      }
      notifyStudent(mailingListId, 'Email', emailSubject, emailContent)
    }
  }

  return (
    <div
      className={`bg-white rounded-lg ${
        classType === CLASS_TYPES.YOUTH && step === 2
          ? 'w-full md:min-w-[32rem]'
          : 'w-full md:min-w-[28rem]'
      } mx-auto`}
    >
      {/* Header with close button */}
      <div className="flex justify-between items-center p-6 pb-4">
        <h2 className="text-xl font-semibold text-color-black">
          {getModalTitle()}
        </h2>
        <button
          onClick={onClose}
          className="p-1 hover:bg-color-grey-5 rounded-full transition-colors"
        >
          <X size={20} className="text-color-grey-1" />
        </button>
      </div>

      {/* Step 1: Selection Screen (Only for Youth Classes) */}
      {classType === CLASS_TYPES.YOUTH && step === 1 && (
        <div className="px-6 pb-6 space-y-6">
          <div className="space-y-4">
            <p className="text-sm text-color-grey-6 leading-relaxed">
              Notify students and parents about any class updates or schedule
              changes.
            </p>
            <p className="text-sm text-color-grey-6">
              You can notify only students, only parents or both.
            </p>

            <div className="space-y-4 pt-2">
              <label className="flex items-center space-x-3 cursor-pointer">
                <div className="relative">
                  <input
                    type="radio"
                    name="notificationTarget"
                    value="students"
                    checked={notificationTarget === 'students'}
                    onChange={(e) =>
                      setNotificationTarget(
                        e.target.value as 'students' | 'parents' | 'both',
                      )
                    }
                    className="sr-only"
                  />
                  <div
                    className={`w-5 h-5 rounded-full border-2 transition-colors ${
                      notificationTarget === 'students'
                        ? 'border-color-black bg-color-black'
                        : 'border-color-grey-2 bg-white'
                    }`}
                  >
                    {notificationTarget === 'students' && (
                      <div className="w-2 h-2 bg-white rounded-full absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"></div>
                    )}
                  </div>
                </div>
                <span className="text-sm font-medium text-color-black">
                  Notify students
                </span>
              </label>

              <label className="flex items-center space-x-3 cursor-pointer">
                <div className="relative">
                  <input
                    type="radio"
                    name="notificationTarget"
                    value="parents"
                    checked={notificationTarget === 'parents'}
                    onChange={(e) =>
                      setNotificationTarget(
                        e.target.value as 'students' | 'parents' | 'both',
                      )
                    }
                    className="sr-only"
                  />
                  <div
                    className={`w-5 h-5 rounded-full border-2 transition-colors ${
                      notificationTarget === 'parents'
                        ? 'border-color-black bg-color-black'
                        : 'border-color-grey-2 bg-white'
                    }`}
                  >
                    {notificationTarget === 'parents' && (
                      <div className="w-2 h-2 bg-white rounded-full absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"></div>
                    )}
                  </div>
                </div>
                <span className="text-sm font-medium text-color-black">
                  Notify parents
                </span>
              </label>

              <label className="flex items-center space-x-3 cursor-pointer">
                <div className="relative">
                  <input
                    type="radio"
                    name="notificationTarget"
                    value="both"
                    checked={notificationTarget === 'both'}
                    onChange={(e) =>
                      setNotificationTarget(
                        e.target.value as 'students' | 'parents' | 'both',
                      )
                    }
                    className="sr-only"
                  />
                  <div
                    className={`w-5 h-5 rounded-full border-2 transition-colors ${
                      notificationTarget === 'both'
                        ? 'border-color-black bg-color-black'
                        : 'border-color-grey-2 bg-white'
                    }`}
                  >
                    {notificationTarget === 'both' && (
                      <div className="w-2 h-2 bg-white rounded-full absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"></div>
                    )}
                  </div>
                </div>
                <span className="text-sm font-medium text-color-black">
                  Notify both students & parents
                </span>
              </label>
            </div>
          </div>

          <div className="pt-4">
            <CustomButton
              title="Continue"
              isLoading={false}
              onClick={handleContinue}
              width="w-full"
              backgroundColor="bg-color-yellow"
              height={12}
            />
          </div>
        </div>
      )}

      {/* Step 2: Notification Form (For Adult Classes or Youth Classes after selection) */}
      {(classType !== CLASS_TYPES.YOUTH || step === 2) && (
        <div className="px-6 pb-6">
          {/* Show target count for youth classes */}
          {classType === CLASS_TYPES.YOUTH && (
            <div className="pb-2">
              <p className="text-sm text-color-grey-6 mb-2">
                {getTargetCount()}
              </p>
            </div>
          )}

          {/* Notification Form */}
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-label-color mb-2">
                Subject
              </label>
              <input
                type="text"
                value={emailSubject}
                onChange={(e) => setEmailSubject(e.target.value)}
                className="w-full px-3 py-2 border border-color-grey-2 rounded-md focus:outline-none focus:ring-2 focus:ring-color-blue-2 focus:border-transparent"
                placeholder="Enter the title/subject for your notification"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-label-color mb-2">
                Content
              </label>
              <textarea
                value={emailContent}
                onChange={(e) => setEmailContent(e.target.value)}
                rows={6}
                className="w-full px-3 py-2 border border-color-grey-2 rounded-md focus:outline-none focus:ring-2 focus:ring-color-blue-2 focus:border-transparent resize-none"
                placeholder="Enter email content"
              />
            </div>
            <div className="pt-4">
              <CustomButton
                title={response.isFetching ? 'Sending...' : 'Send Notification'}
                isLoading={
                  response.isFetching || singleNotificationResponse.isFetching
                }
                onClick={handleSendNotification}
                isDisabled={!emailSubject.trim() || !emailContent.trim()}
                width="w-full"
                height={12}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default NotifyStudentModal
