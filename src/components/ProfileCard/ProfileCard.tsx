import { getNameLetter } from '@/utils/utils'
import Image from 'next/image'
import React from 'react'
interface ProfileCardProps {
  student: any
  height: number
  width: number
  showColumnView?: boolean
}
const ProfileCard = ({
  student,
  height,
  width,
  showColumnView,
}: ProfileCardProps) => {
  return (
    <div
      key={student.id}
      className={`flex items-center gap-3 ${showColumnView ? 'flex-row' : 'flex-col'}`}
    >
      {student?.photo ? (
        <Image
          src={student?.photo || '/profile1.png'}
          alt={student?.name}
          width={width}
          height={height}
          className="rounded-full object-cover"
          style={{ width: `${width}px`, height: `${height}px` }}
        />
      ) : (
        <div
          className="rounded-full bg-black cursor-pointer text-white flex items-center justify-center font-semibold text-base"
          style={{ height: `${height}px`, width: `${width}px` }}
        >
          <h3>{getNameLetter(student?.name)}</h3>
        </div>
      )}
      <h3 className="font-bold text-[10px]">{student.name}</h3>
    </div>
  )
}

export default ProfileCard
