import React from 'react'
import <PERSON>Header from '../PageHeader/PageHeader'
import { handleLoggedIn } from '@/lib/actions/login.actions'
import RightColumnHoc from '../RightColumnHoc/RightColumnHoc'
import DashboardEvents from '../DashboardEvents/DashboardEvents'
import UserStatsCard from '../UserStatsCard/UserStatsCard'
import DonationsChart from '../DonationChart/DonationChart'
import TopTeacher from '../TopTeacher/TopTeacher'
import UpCommingClasses from '../UpCommingClasses/UpCommingClasses'
import { ROLES } from '@/types/enum'

const StudentDashboardPage = ({ userData }: { userData: any }) => {
  // const userData: any = await handleLoggedIn()
  return (
    <div className="flex flex-col md:flex-row w-screen md:w-full">
      <div className="w-full">
        {userData && (
          <PageHeader
            heading={`Hey, ${userData?.name}!`}
            backgroundColor="#F5F5F7"
            showMessage={false}
            showSearch={false}
            showCart={userData?.role != ROLES.Teacher}
            userData={userData}
          />
        )}
        <div className="flex flex-col gap-5 p-3 md:pr-0">
          <div className="flex flex-col md:flex-row items-center gap-3 ">
            <UserStatsCard />
            <DonationsChart />
          </div>
          <div className="mt-5">
            <TopTeacher />
          </div>
          <div className="mt-5">
            <UpCommingClasses />
          </div>
        </div>
      </div>
      <div className="col-span-1 md:col-span-3">
        <RightColumnHoc>
          <DashboardEvents />
        </RightColumnHoc>
      </div>
    </div>
  )
}

export default StudentDashboardPage
