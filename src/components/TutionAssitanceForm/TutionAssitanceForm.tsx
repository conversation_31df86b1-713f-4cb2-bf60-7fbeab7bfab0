'use client'

import React, { useEffect, useState } from 'react'
import CustomButton from '../CustomComponents/CustomButton/CustomButton'
import MobileLogin from '../MobileLogin/MobileLogin'
import { useApi } from '@/hooks/useApi'
import { createTuitionAssistanceApplication } from '@/lib/actions/account.actions'
import { toast } from 'react-toastify'
import EmailVerify from '../EmailVerify/EmailVerify'
import GreenTick from '../Icons/GreenTick'
import SuccessfullyFormSubmit from '../SuccessfullyFormSubmit/SuccessfullyFormSubmit'

interface TuitionAssistanceFormData {
  firstName: string
  lastName: string
  reasonForClass: string
  paymentPercentage: string
}

interface TuitionAssistanceFormProps {
  payOptions: Array<{ value: string; label: string }>
  userData: any
}

const TuitionAssistanceForm = ({
  payOptions,
  userData,
}: TuitionAssistanceFormProps) => {
  const userNameArray = userData ? userData.name.split(' ') : ['', '']
  const [formData, setFormData] = useState<TuitionAssistanceFormData>({
    firstName: userData ? userNameArray[0] : '',
    lastName: userData ? userNameArray[userNameArray.length - 1] : '',
    reasonForClass: '',
    paymentPercentage: '',
  })
  const [isFormSubmitted, setIsFormSubmitted] = useState<boolean>(false)
  const [isPhoneVerified, setIsPhoneVerified] = useState<boolean>(
    !!userData?.phoneNumber,
  )
  const [isEmailVerified, setIsEmailVerified] = useState(!!userData?.email)
  const [email, setEmail] = useState(userData ? userData.email : '')
  const [phoneNumber, setPhoneNumber] = useState<string>(
    userData ? '+' + userData.phoneNumber : '',
  )
  const [formResponse, submitForm] = useApi(
    (first, last, email, phoneNumber, reason, percenttage) =>
      createTuitionAssistanceApplication(
        first,
        last,
        email,
        phoneNumber,
        reason,
        percenttage,
      ),
    false,
  )

  useEffect(() => {
    if (formResponse.isSuccess) {
      setIsFormSubmitted(true)
      setFormData({
        firstName: '',
        lastName: '',
        reasonForClass: '',
        paymentPercentage: '',
      })
      setEmail('')
      setPhoneNumber('')
      setIsPhoneVerified(false)
      setIsEmailVerified(false)
    } else if (!formResponse.isSuccess) {
      toast.error(formResponse.error, {
        autoClose: 3000,
      })
    }
  }, [formResponse])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    // Basic validation for required fields
    if (
      !formData.firstName ||
      !formData.lastName ||
      !formData.reasonForClass ||
      !formData.paymentPercentage
    ) {
      toast.error('Please fill out all required fields marked with *.', {
        autoClose: 3000,
      })
      return
    }
    if (!isEmailVerified) {
      toast.error('Please verify your email address.', { autoClose: 3000 })
      return
    }
    if (!isPhoneVerified) {
      toast.error('Please verify your phone number.', { autoClose: 3000 })
      return
    }
    await submitForm(
      formData.firstName,
      formData.lastName,
      email,
      phoneNumber,
      formData.reasonForClass,
      formData.paymentPercentage,
    )
  }

  return (
    <>
      {isFormSubmitted ? (
        <SuccessfullyFormSubmit formTitle={'Tution Assitance Form'} />
      ) : (
        <div className="container mx-auto max-w-4xl mt-10">
          <h2 className="px-8 font-semibold text-3xl">Tution Assitance Form</h2>
          <form onSubmit={handleSubmit} className="rounded px-8 pt-6 pb-8 mb-4">
            <div className="mb-6">
              <p className="text-lg mb-4">
                The Muse Writers Center is honored and grateful to announce the
                establishment of the Dakota Anne Chacon Tuition Assistance Fund.
                The Tuition Assistance Fund allows The Muse to offer our
                creative writing classes to students regardless of their ability
                to pay tuition. To date, the fund has provided tuition
                assistance to hundreds of students and has received{' '}
                <a href="#" className="text-blue-600 hover:underline">
                  donations
                </a>{' '}
                from hundreds of additional generous persons.
              </p>
              <p className="text-lg mb-4">
                To apply, please complete the form below. We will contact you to
                discuss your options. Those on tuition assistance are asked to
                be on our volunteer list.
              </p>
            </div>

            {/* Name */}
            <div className="mb-4 flex flex-col justify-between sm:flex-row gap-4">
              <div className="flex flex-col w-full">
                <label className="block text-gray-700 font-bold mb-2">
                  First Name <span className="text-red-500">*</span>
                </label>
                <div className="flex-1">
                  <input
                    type="text"
                    className="w-full rounded-lg border border-gray-300 p-2"
                    placeholder="First name"
                    value={formData.firstName}
                    onChange={(e) =>
                      setFormData({ ...formData, firstName: e.target.value })
                    }
                    required
                  />
                </div>
              </div>
              <div className="flex flex-col w-full">
                <label className="block text-gray-700 font-bold mb-2">
                  Last Name <span className="text-red-500">*</span>
                </label>
                <div className="flex-1">
                  <input
                    type="text"
                    className="w-full rounded-lg border border-gray-300 p-2"
                    placeholder="Last name"
                    value={formData.lastName}
                    onChange={(e) =>
                      setFormData({ ...formData, lastName: e.target.value })
                    }
                    required
                  />
                </div>
              </div>
            </div>

            {/* Email */}
            <EmailVerify
              user={null}
              email={email}
              setEmail={setEmail}
              phoneNumber={phoneNumber}
              studentOnBoarding={false}
              setIsEmailVerified={setIsEmailVerified}
              isEmailVerified={isEmailVerified}
              classNames="block text-gray-700 font-bold mb-2"
            />

            <div className="mt-4">
              {isPhoneVerified ? (
                <div className="space-y-1">
                  <label
                    htmlFor="fullName"
                    className="block text-sm font-medium"
                  >
                    Phone
                  </label>
                  <div className="relative">
                    <input
                      type="text"
                      value={phoneNumber}
                      readOnly={true}
                      className="text-sm w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-300"
                      placeholder="Enter your number"
                      required
                    />
                    <span className="absolute right-3 top-3 text-sm font-semibold text-color-green-4 flex items-center gap-2 cursor-pointer">
                      <GreenTick />
                      Verified
                    </span>
                  </div>
                </div>
              ) : (
                <div className="teacher-onboarding">
                  <MobileLogin
                    showOtpBtn={false}
                    setIsPhoneVerified={setIsPhoneVerified}
                    setUserPhone={setPhoneNumber}
                    classNames="block text-gray-700 font-bold mb-2"
                  />
                </div>
              )}
            </div>

            {/* Reason for Class */}
            <div className="my-4">
              <label className="block text-gray-700 font-bold mb-2">
                Why do you want to take a class, workshop, or seminar at The
                Muse? <span className="text-red-500">*</span>
              </label>
              <textarea
                className="w-full rounded-lg border border-gray-300 p-2"
                rows={4}
                value={formData.reasonForClass}
                onChange={(e) =>
                  setFormData({ ...formData, reasonForClass: e.target.value })
                }
                required
              />
            </div>

            {/* Payment Percentage */}
            <div className="mb-4">
              <label className="block text-gray-700 font-bold mb-2">
                What percentage of your tuition are you able to pay?{' '}
                <span className="text-red-500">*</span>
              </label>
              <div className="mt-2">
                {payOptions.map((option, index: number) => (
                  <div key={option.value} className="mb-2">
                    <label className="inline-flex items-center">
                      <input
                        type="radio"
                        className="form-radio"
                        name="paymentPercentage"
                        value={option.value}
                        checked={formData.paymentPercentage === option.value}
                        onChange={(e) =>
                          setFormData({
                            ...formData,
                            paymentPercentage: e.target.value,
                          })
                        }
                        required
                      />
                      <span className="ml-2">{option.label}</span>
                    </label>
                  </div>
                ))}
              </div>
              <p className="text-sm text-gray-700 mt-4">
                All choices are acceptable answers. The Muse refuses to allow
                finances to be a barrier between writers and access to becoming
                better writers. All choices above are valued and appreciated
                equally. If you require 100% tuition assistance, it is
                available. Please email{' '}
                <a
                  href="mailto:<EMAIL>"
                  className="text-blue-600 hover:underline"
                >
                  <EMAIL>
                </a>{' '}
                to inquire.
              </p>
            </div>

            {/* Submit Button */}
            <div className="flex items-center justify-start mt-6">
              <CustomButton
                title="Submit"
                onClick={handleSubmit}
                isLoading={formResponse.isFetching}
              />
            </div>
          </form>
        </div>
      )}
    </>
  )
}

export default TuitionAssistanceForm
