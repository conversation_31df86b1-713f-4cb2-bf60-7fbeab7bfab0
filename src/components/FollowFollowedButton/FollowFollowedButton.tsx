import { useApi } from '@/hooks/useApi'
import { followTeachers, unFollowTeachers } from '@/lib/actions/follow.actions'
import React, { useEffect } from 'react'
import { toast } from 'react-toastify'
import { useDispatch } from 'react-redux'
import {
  addFollowedTeacher,
  removeFollowedTeacher,
} from '@/lib/redux/slices/followSlice'

const FollowFollowedButton = ({
  teacherIds = [],
  isFollow = false,
  unFollowedText = 'Follow',
  showButton = false,
  height = '50px',
  backgroundColor = '#FFBB54',
}: {
  teacherIds?: string[]
  isFollow?: boolean
  unFollowedText?: string
  showButton?: boolean
  height?: string
  backgroundColor?: string
}) => {
  const dispatch = useDispatch()
  const [followResponse, followTeacher] = useApi(
    (access: string, teacherIds: string[]) =>
      followTeachers(access, teacherIds),
  )
  const [unfollowResponse, unfollowTeacher] = useApi(
    (access: string, teacherIds: string[]) =>
      unFollowTeachers(access, teacherIds),
  )

  useEffect(() => {
    if (followResponse.isSuccess) {
      toast.success('Successfully followed')
    } else if (followResponse.error) {
      // Revert the state if API call failed
      teacherIds.forEach((id) => {
        dispatch(removeFollowedTeacher(id))
      })
      toast.error('Failed to follow teacher')
    }
  }, [followResponse, dispatch, teacherIds])

  useEffect(() => {
    if (unfollowResponse.isSuccess) {
      toast.success('Successfully unfollowed')
    } else if (unfollowResponse.error) {
      // Revert the state if API call failed
      teacherIds.forEach((id) => {
        dispatch(addFollowedTeacher({ id, name: '', photo: '' }))
      })
      toast.error('Failed to unfollow teacher')
    }
  }, [unfollowResponse, dispatch, teacherIds])

  const handleFollow = () => {
    if (followResponse.isFetching || unfollowResponse.isFetching) {
      toast.info('Please wait, action is in progress')
      return
    }
    if (isFollow) {
      teacherIds.forEach((id) => {
        dispatch(removeFollowedTeacher(id))
      })
      unfollowTeacher(teacherIds)
    } else {
      teacherIds.forEach((id) => {
        dispatch(addFollowedTeacher({ id, name: '', photo: '' }))
      })
      followTeacher(teacherIds)
    }
  }
  return (
    <>
      {teacherIds && teacherIds.length > 0 && (
        <div className="cursor-pointer" onClick={handleFollow}>
          {showButton ? (
            <button
              className={`${isFollow ? 'bg-red-500 hover:bg-red-600' : 'bg-orange-400 hover:bg-orange-500'} px-6 py-4 w-full rounded-xl text-white font-semibold transition-colors`}
              style={{
                height,
                backgroundColor: isFollow ? undefined : backgroundColor,
              }}
            >
              {isFollow ? `Unfollow` : `+ ${unFollowedText}`}
            </button>
          ) : (
            <span
              className={`font-medium text-sm ${isFollow ? 'text-red-500' : 'text-color-blue-1'}`}
            >
              {isFollow ? `Unfollow` : `+ ${unFollowedText}`}
            </span>
          )}
        </div>
      )}
    </>
  )
}

export default FollowFollowedButton
