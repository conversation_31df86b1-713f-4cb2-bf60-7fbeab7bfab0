'use client'
import React, { useEffect, useMemo, useState } from 'react'
import CustomButton from '../CustomComponents/CustomButton/CustomButton'
import {
  CountrySelect,
  StateSelect,
  CitySelect,
} from 'react-country-state-city'
import 'react-country-state-city/dist/react-country-state-city.css'
import { useApi } from '@/hooks/useApi'
import { createCustomCheckoutRequest } from '@/lib/actions/payment.actions'
import { useRouter, useSearchParams } from 'next/navigation'
import { toast } from 'react-toastify'
import useModal from '@/hooks/useModal'
import { updateUserNestedField } from '@/lib/actions/login.actions'
const SaveCardModal = dynamic(() => import('../SaveCardModal/SaveCardModal'), {
  ssr: false,
})
import dynamic from 'next/dynamic'
import { paymentToken, setCartCount, setPaymentToken } from '@/lib/redux'
import { useDispatch } from 'react-redux'
import { ROLES } from '@/types/enum'
import CustomModalIcon from '../Icons/CustomModalIcon'
import NotifyModal from '../NotifyModal/NotifyModal'
import { getAllPaymentMethods } from '@/lib/actions/customerProfile.actions'
import { calculateProcessingFee } from '@/utils/utils'

interface PriceDetails {
  userId: string
  entityId: string
  entityType: string
  amount: number
  walletBalance: number
  iat: number
  discountedAmount: number
  isWalletEnabled: boolean
  paymentType: string
}

type CreditCardInfo = {
  ccNumber: string
  ccExpiry: string
  ccCode: string
  ccName: string
  saveCard: boolean
}

interface PaymentFormProps {
  data: PriceDetails
  userData?: any
  invoiceToken?: string
}

type SavedProfileInfo = {
  customerProfileId: string
  paymentProfileId: string
}

const PaymentForm: React.FC<PaymentFormProps> = ({
  data,
  userData,
  invoiceToken,
}) => {
  const router = useRouter()
  if (data?.entityId == '') router.push('/dashboard')
  const [addNewCard, setAddNewCard] = useState<boolean>(false)
  const [country, setCountry] = useState<any>(
    userData?.BillingAddress?.country
      ? { name: userData?.BillingAddress?.country }
      : '',
  )
  const [currentState, setCurrentState] = useState<any>(
    userData?.BillingAddress?.state
      ? { name: userData?.BillingAddress?.state }
      : '',
  )
  const [currentCity, setCurrentCity] = useState<any>(
    userData?.BillingAddress?.city
      ? { name: userData?.BillingAddress?.city }
      : '',
  )
  const [cardList, setCardList] = useState<any>([])
  const [cardNumber, setCardNumber] = useState('')
  const [expirationDate, setExpirationDate] = useState('')
  const [csc, setCsc] = useState('')
  const [nameOnCard, setNameOnCard] = useState('')
  const [addressLine1, setAddressLine1] = useState(
    userData?.BillingAddress?.street ?? '',
  )
  const [postalCode, setPostalCode] = useState(
    userData?.BillingAddress?.postalCode ?? '',
  )
  const [customerProfileId, setCustomerProfileId] = useState(null)
  const [coverProcessingFee, setCoverProcessingFee] = useState<boolean>(true)
  const [saveCard, setSaveCard] = useState<string>('')
  const [payementProfileId, setPayementProfileId] = useState<string>('')
  const [useWalletBalance, setUseWalletBalance] = useState<boolean>(
    data?.walletBalance > 0 && data?.isWalletEnabled,
  )
  const [shippingCountry, setShippingCountry] = useState<any>('')
  const [shippingAddressLine1, setShippingAddressLine1] = useState<string>('')
  const [shippingState, setShippingState] = useState<any>('')
  const [shippingCity, setShippingCity] = useState<any>('')
  const [shippingPostalCode, setShippingPostalCode] = useState('')
  const [sameAsBilling, setSameAsBilling] = useState(true)
  const [isPaymentSuccessful, setIsPaymentSuccessful] = useState(false)

  // Validation errors state
  const [errors, setErrors] = useState<{ [key: string]: string }>({})

  // Function to update user address information in cookie
  const updateUserAddresses = async () => {
    try {
      // Update billing address with the correct structure to match original data
      const billingAddressData = {
        street: addressLine1,
        city: currentCity?.name,
        state: currentState?.name,
        country: country?.name,
        postalCode: postalCode,
        // Keep existing fields from original structure if they exist
        countryCode: country?.iso2 || null,
        stateCode: currentState?.iso2 || null,
        geocodeAccuracy: null,
        latitude: null,
        longitude: null,
      }

      await updateUserNestedField('BillingAddress', billingAddressData)

      // Update shipping address if different from billing
      if (!sameAsBilling) {
        const shippingAddressData = {
          street: shippingAddressLine1,
          city: shippingCity?.name,
          state: shippingState?.name,
          country: shippingCountry?.name,
          postalCode: shippingPostalCode,
          // Keep existing fields from original structure if they exist
          countryCode: shippingCountry?.iso2 || null,
          stateCode: shippingState?.iso2 || null,
          geocodeAccuracy: null,
          latitude: null,
          longitude: null,
        }

        await updateUserNestedField('ShippingAddress', shippingAddressData)
      } else {
        // If same as billing, copy billing to shipping
        await updateUserNestedField('ShippingAddress', billingAddressData)
      }
    } catch (error) {
      console.error('Error updating user addresses:', error)
    }
  }

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {}

    // Billing address validation
    if (!country || !country.name) {
      newErrors.country = 'Country is required'
    }
    if (!addressLine1.trim()) {
      newErrors.addressLine1 = 'Address line 1 is required'
    }
    if (!currentState || !currentState.name) {
      newErrors.state = 'State is required'
    }
    if (!currentCity || !currentCity.name) {
      newErrors.city = 'City is required'
    }
    if (!postalCode.trim()) {
      newErrors.postalCode = 'Postal code is required'
    }

    // Card validation (only if adding new card and amount > 0)
    if (addNewCard && getNetAmount > 0) {
      if (!cardNumber.trim()) {
        newErrors.cardNumber = 'Card number is required'
      }
      if (!expirationDate.trim()) {
        newErrors.expirationDate = 'Expiration date is required'
      }
      if (!csc.trim()) {
        newErrors.csc = 'Security code is required'
      }
      if (!nameOnCard.trim()) {
        newErrors.nameOnCard = 'Name on card is required'
      }
    }

    // Shipping address validation (only if different from billing)
    if (!sameAsBilling) {
      if (!shippingCountry || !shippingCountry.name) {
        newErrors.shippingCountry = 'Shipping country is required'
      }
      if (!shippingAddressLine1.trim()) {
        newErrors.shippingAddressLine1 = 'Shipping address line 1 is required'
      }
      if (!shippingState || !shippingState.name) {
        newErrors.shippingState = 'Shipping state is required'
      }
      if (!shippingCity || !shippingCity.name) {
        newErrors.shippingCity = 'Shipping city is required'
      }
      if (!shippingPostalCode.trim()) {
        newErrors.shippingPostalCode = 'Shipping postal code is required'
      }
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }
  const searchParams = useSearchParams()
  const [modal, showModal] = useModal()
  const [youthModal, showYouthModal] = useModal()
  const token = searchParams.get('token')
  const dispatch = useDispatch()
  const [checkoutResponse, handleCheckout] = useApi(
    createCustomCheckoutRequest,
    false,
  )
  const [cardDataResponse, fetchCardData] = useApi((access: string) =>
    getAllPaymentMethods(access),
  )

  useEffect(() => {
    if (cardDataResponse.isSuccess) {
      setCardList(
        cardDataResponse?.data?.data?.paymentProfiles?.paymentProfiles || [],
      )
      setCustomerProfileId(
        cardDataResponse?.data?.data?.paymentProfiles?.customerProfileId,
      )
    }
  }, [cardDataResponse])

  useEffect(() => {
    if (userData?.role == ROLES.Youth_Student) {
      showParentModal()
    }
    userData && fetchCardData()
  }, [])

  useEffect(() => {
    if (checkoutResponse.isSuccess) {
      dispatch(setPaymentToken(invoiceToken || token || ''))
      toast.success('Payment successful', { autoClose: 1500 })
      setIsPaymentSuccessful(true)
      dispatch(setCartCount(0))
      setTimeout(() => {
        router.replace('/payment/success')
      }, 2500)
    }
    if (checkoutResponse.error) {
      setTimeout(() => {
        router.replace('/payment/failed')
      }, 2500)
    }
  }, [checkoutResponse])

  useEffect(() => {
    if (sameAsBilling) {
      setShippingCountry(country)
      setShippingAddressLine1(addressLine1)
      setShippingState(currentState)
      setShippingCity(currentCity)
      setShippingPostalCode(postalCode)
    } else {
      setShippingCountry(null)
      setShippingAddressLine1('')
      setShippingState(null)
      setShippingCity(null)
      setShippingPostalCode('')
    }
  }, [
    sameAsBilling,
    country,
    addressLine1,
    currentState,
    currentCity,
    postalCode,
  ])

  useEffect(() => {
    if (saveCard != '') {
      const syntheticEvent = { preventDefault: () => {} } as React.FormEvent
      handleSubmit(syntheticEvent)
    }
  }, [saveCard])

  useEffect(() => {
    if (addNewCard) {
      setPayementProfileId('')
    } else {
      setCardNumber('')
      setExpirationDate('')
      setCsc('')
      setNameOnCard('')
    }
  }, [addNewCard])

  // Monitor location state changes and clear validation errors when fields become empty
  useEffect(() => {
    if (!country || !country.name) {
      setErrors((prev) => ({ ...prev, country: '' }))
    }
  }, [country])

  useEffect(() => {
    if (!currentState || !currentState.name) {
      setErrors((prev) => ({ ...prev, state: '' }))
    }
  }, [currentState])

  useEffect(() => {
    if (!currentCity || !currentCity.name) {
      setErrors((prev) => ({ ...prev, city: '' }))
    }
  }, [currentCity])

  useEffect(() => {
    if (!shippingCountry || !shippingCountry.name) {
      setErrors((prev) => ({ ...prev, shippingCountry: '' }))
    }
  }, [shippingCountry])

  useEffect(() => {
    if (!shippingState || !shippingState.name) {
      setErrors((prev) => ({ ...prev, shippingState: '' }))
    }
  }, [shippingState])

  useEffect(() => {
    if (!shippingCity || !shippingCity.name) {
      setErrors((prev) => ({ ...prev, shippingCity: '' }))
    }
  }, [shippingCity])

  const getNetAmount = useMemo(() => {
    if (useWalletBalance && data.isWalletEnabled) {
      return data.amount - data.walletBalance < 0
        ? 0
        : Number(data.amount - data.walletBalance)
    } else {
      return Number(data.amount)
    }
  }, [useWalletBalance])

  useEffect(() => {
    if (getNetAmount === 0) {
      setCoverProcessingFee(false)
    } else {
      setCoverProcessingFee(true)
    }
  }, [getNetAmount])

  const convenienceFees = useMemo(
    () => (coverProcessingFee ? calculateProcessingFee(getNetAmount) : 0),
    [getNetAmount, coverProcessingFee],
  )

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Clear previous errors
    setErrors({})

    // Validate form
    if (!validateForm()) {
      toast.error('Please fill in all required fields', { autoClose: 1500 })
      return
    }

    if (
      !payementProfileId &&
      !cardNumber &&
      !expirationDate &&
      !csc &&
      getNetAmount > 0
    ) {
      toast.error('Please fill the card details', { autoClose: 1500 })
      return
    }

    if (addNewCard && saveCard == '' && userData) {
      handleSaveCard()
      return
    }
    try {
      if (token || invoiceToken) {
        const card: CreditCardInfo = {
          ccNumber: cardNumber.replaceAll('-', ''),
          ccExpiry: expirationDate,
          ccCode: csc,
          ccName: nameOnCard,
          saveCard: saveCard == 'yes' ? true : false,
        }
        let savedCard: SavedProfileInfo | undefined
        if (customerProfileId) {
          savedCard = {
            customerProfileId,
            paymentProfileId: payementProfileId,
          }
        }
        const shpAdd1 = sameAsBilling ? addressLine1 : shippingAddressLine1
        const shpCity = sameAsBilling ? currentCity : shippingCity
        const shpState = sameAsBilling ? currentState : shippingState
        const shpCountry = sameAsBilling ? country : shippingCountry
        const shpPostalCode = sameAsBilling ? postalCode : shippingPostalCode
        const checkoutCard =
          getNetAmount == 0 ? undefined : addNewCard ? card : savedCard
        await handleCheckout(
          token || invoiceToken,
          addressLine1,
          currentCity?.name,
          currentState?.name,
          country?.name,
          postalCode,
          shpAdd1,
          shpCity?.name,
          shpState?.name,
          shpCountry?.name,
          shpPostalCode,
          useWalletBalance,
          coverProcessingFee,
          checkoutCard,
        )

        // Update user addresses in cookie after successful payment processing
        await updateUserAddresses()
      }
    } catch (error) {
      console.error('Payment processing error:', error)
      toast.error('Payment processing failed. Please try again.', {
        autoClose: 3000,
      })
    }
  }

  const showParentModal = () => {
    showYouthModal({
      title: '',
      contentFn: (onClose: any) => (
        <NotifyModal
          onClose={onClose}
          icon={<CustomModalIcon />}
          title="Notify parent to pay"
          desc="Your parent will be notified to pay for the course"
          btnText="Notify parent"
          showCancel={true}
          showBtn={true}
        />
      ),
      closeOnClickOutside: true,
      size: 'md',
      showCloseButton: false,
    })
  }

  const handleSaveCard = () => {
    showModal({
      title: '',
      contentFn: (onClose: any) => (
        <SaveCardModal onClose={onClose} setSaveCard={setSaveCard} />
      ),
      closeOnClickOutside: true,
      size: 'md',
      showCloseButton: false,
    })
  }

  const formatCardNumber = (value: string) => {
    return value
      .replace(/\s/g, '')
      .replace(/(\d{4})(?=\d)/g, '$1-')
      .trim()
  }

  const handleCardNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/[^\d]/g, '')
    if (value.length <= 16) {
      setCardNumber(formatCardNumber(value))
      // Clear error when user types
      if (errors.cardNumber) {
        setErrors((prev) => ({ ...prev, cardNumber: '' }))
      }
    }
  }

  const handleExpirationChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let value = e.target.value.replace(/[^\d]/g, '')
    if (value.length > 2) {
      value = value.slice(0, 2) + '/' + value.slice(2, 4)
    }
    if (value.length <= 5) {
      setExpirationDate(value)
      // Clear error when user types
      if (errors.expirationDate) {
        setErrors((prev) => ({ ...prev, expirationDate: '' }))
      }
    }
  }

  const handleSecurityCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/[^\d]/g, '')
    if (value.length <= 4) {
      setCsc(value)
      // Clear error when user types
      if (errors.csc) {
        setErrors((prev) => ({ ...prev, csc: '' }))
      }
    }
  }

  useEffect(() => {
    if (cardList.length > 0) {
      setPayementProfileId(cardList[0].customerPaymentProfileId)
      setAddNewCard(false)
    } else {
      setAddNewCard(true)
    }
  }, [cardList])

  if (checkoutResponse?.isSuccess && getNetAmount > 0) {
    return (
      <div className="w-full flex justify-center items-center h-96 flex-col gap-10">
        <div className="success-loader relative">
          <svg
            className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
            width="90"
            height="64"
            viewBox="0 0 90 64"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <rect
              x="2.5426"
              y="2.25"
              width="85.2811"
              height="59.5"
              rx="8.99706"
              fill="#EAF8FF"
              stroke="#0474F4"
              strokeWidth="3.5"
            />
            <rect x="4" y="18" width="84" height="9.88235" fill="#0474F4" />
            <rect
              x="12.4"
              y="46"
              width="19.6"
              height="5.6"
              rx="2.8"
              fill="#0474F4"
            />
          </svg>
          <div className="rotating-background"></div>
        </div>
        <h1 className="font-semibold text-2xl">Processing payment…</h1>
      </div>
    )
  }

  if (checkoutResponse?.error && getNetAmount > 0) {
    return (
      <div className="w-full flex justify-center items-center h-96 flex-col gap-10">
        <div className="success-loader relative">
          <svg
            className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
            width="90"
            height="64"
            viewBox="0 0 90 64"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <rect
              x="2.5426"
              y="2.25"
              width="85.2811"
              height="59.5"
              rx="8.99706"
              fill="#EAF8FF"
              stroke="#0474F4"
              strokeWidth="3.5"
            />
            <rect x="4" y="18" width="84" height="9.88235" fill="#0474F4" />
            <rect
              x="12.4"
              y="46"
              width="19.6"
              height="5.6"
              rx="2.8"
              fill="#0474F4"
            />
          </svg>
          <div className="rotating-background"></div>
        </div>
        <h1 className="font-semibold text-2xl">Processing payment…</h1>
      </div>
    )
  }

  return (
    <>
      {checkoutResponse.isFetching && getNetAmount > 0 ? (
        <div className="w-full flex justify-center items-center h-96 flex-col gap-10">
          <div className="success-loader relative">
            <svg
              className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
              width="90"
              height="64"
              viewBox="0 0 90 64"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <rect
                x="2.5426"
                y="2.25"
                width="85.2811"
                height="59.5"
                rx="8.99706"
                fill="#EAF8FF"
                stroke="#0474F4"
                strokeWidth="3.5"
              />
              <rect x="4" y="18" width="84" height="9.88235" fill="#0474F4" />
              <rect
                x="12.4"
                y="46"
                width="19.6"
                height="5.6"
                rx="2.8"
                fill="#0474F4"
              />
            </svg>
            <div className="rotating-background"></div>
          </div>
          <h1 className="font-semibold text-2xl">Processing payment…</h1>
        </div>
      ) : (
        <div className="max-w-4xl mb-4 mx-auto flex gap-4 md:gap-8 flex-col-reverse md:flex-row">
          <div className="flex-1 bg-white p-6 rounded-2xl">
            <div className="space-y-6">
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  {getNetAmount > 0 && (
                    <h2 className="text-xl font-bold">Credit card</h2>
                  )}
                  {data?.entityType != 'Wallet' &&
                    data.isWalletEnabled &&
                    userData && (
                      <h2 className="text-sm font-bold flex items-center">
                        Wallet Balance: {data.walletBalance} $
                      </h2>
                    )}
                  {data?.entityType != 'Wallet' &&
                    data.isWalletEnabled &&
                    getNetAmount == 0 &&
                    data.walletBalance > 0 && (
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="useWalletBalance"
                          name="useWalletBalance"
                          checked={useWalletBalance}
                          onChange={(e) =>
                            setUseWalletBalance(e.target.checked)
                          }
                          className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                        <label
                          htmlFor="useWalletBalance"
                          className="ml-2 block text-sm md:text-base font-bold"
                        >
                          Use wallet balance
                        </label>
                      </div>
                    )}
                </div>
                {cardDataResponse.isFetching ? (
                  <span className="text-sm text-gray-400 mt-4 block animate-shimmer">
                    Fetching saved cards...
                  </span>
                ) : (
                  <>
                    {getNetAmount > 0 && cardList.length > 0 && (
                      <div className="space-y-1">
                        <div>
                          <label
                            htmlFor="savedCard"
                            className="block text-sm font-bold"
                          >
                            Saved Card
                          </label>
                          <select
                            id="savedCard"
                            name="savedCard"
                            value={payementProfileId}
                            onChange={(e) =>
                              setPayementProfileId(e.target.value)
                            }
                            onFocus={() => setAddNewCard(false)}
                            className="mt-1 block w-full px-3 py-2 border text-sm rounded-lg border-color-grey-2"
                          >
                            {[{ customerPaymentProfileId: '' }]
                              .concat(cardList)
                              .map((card: any, index) => (
                                <option
                                  key={card.customerPaymentProfileId}
                                  value={
                                    index === 0
                                      ? ''
                                      : card.customerPaymentProfileId
                                  }
                                  className="font-medium"
                                >
                                  {card?.payment?.creditCard?.cardType
                                    ? card?.payment?.creditCard?.cardType + ': '
                                    : ''}{' '}
                                  {index === 0
                                    ? 'Select saved card'
                                    : card?.payment?.creditCard?.cardNumber}
                                </option>
                              ))}
                          </select>
                        </div>
                        {!addNewCard && (
                          <h1
                            className="text-xs text-color-blue-1 font-bold cursor-pointer"
                            onClick={() => setAddNewCard(true)}
                          >
                            + Add new card
                          </h1>
                        )}
                      </div>
                    )}

                    {addNewCard && (
                      <div className="space-y-4">
                        <div>
                          <label
                            htmlFor="cardNumber"
                            className="block text-sm font-bold"
                          >
                            Card Number <span className="text-red-500">*</span>
                          </label>
                          <input
                            type="text"
                            id="cardNumber"
                            name="cardNumber"
                            value={cardNumber}
                            onChange={handleCardNumberChange}
                            placeholder="••••  ••••  ••••  ••••"
                            className={`mt-1 block w-full px-3 py-2 border text-sm rounded-lg ${
                              errors.cardNumber
                                ? 'border-red-500'
                                : 'border-color-grey-2'
                            }`}
                          />
                          {errors.cardNumber && (
                            <p className="text-red-500 text-xs mt-1">
                              {errors.cardNumber}
                            </p>
                          )}
                        </div>

                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <label
                              htmlFor="expirationDate"
                              className="block text-sm font-bold"
                            >
                              Expiration (MM/YY){' '}
                              <span className="text-red-500">*</span>
                            </label>
                            <input
                              type="text"
                              id="expirationDate"
                              name="expirationDate"
                              value={expirationDate}
                              onChange={handleExpirationChange}
                              placeholder="MM/YY"
                              className={`mt-1 block w-full px-3 py-2 border text-sm rounded-lg ${
                                errors.expirationDate
                                  ? 'border-red-500'
                                  : 'border-color-grey-2'
                              }`}
                            />
                            {errors.expirationDate && (
                              <p className="text-red-500 text-xs mt-1">
                                {errors.expirationDate}
                              </p>
                            )}
                          </div>
                          <div>
                            <label
                              htmlFor="csc"
                              className="block text-sm font-bold"
                            >
                              Card Security Code{' '}
                              <span className="text-red-500">*</span>
                            </label>
                            <input
                              type="text"
                              id="csc"
                              name="csc"
                              value={csc}
                              onChange={handleSecurityCodeChange}
                              placeholder="CSC"
                              className={`mt-1 block w-full px-3 py-2 border text-sm rounded-lg ${
                                errors.csc
                                  ? 'border-red-500'
                                  : 'border-color-grey-2'
                              }`}
                            />
                            {errors.csc && (
                              <p className="text-red-500 text-xs mt-1">
                                {errors.csc}
                              </p>
                            )}
                          </div>
                        </div>

                        <div>
                          <label
                            htmlFor="nameOnCard"
                            className="block text-sm font-bold"
                          >
                            Name on the card{' '}
                            <span className="text-red-500">*</span>
                          </label>
                          <input
                            type="text"
                            id="nameOnCard"
                            name="nameOnCard"
                            value={nameOnCard}
                            onChange={(e) => {
                              setNameOnCard(e.target.value)
                              // Clear error when user types
                              if (errors.nameOnCard) {
                                setErrors((prev) => ({
                                  ...prev,
                                  nameOnCard: '',
                                }))
                              }
                            }}
                            className={`mt-1 block w-full px-3 py-2 border text-sm rounded-lg ${
                              errors.nameOnCard
                                ? 'border-red-500'
                                : 'border-color-grey-2'
                            }`}
                          />
                          {errors.nameOnCard && (
                            <p className="text-red-500 text-xs mt-1">
                              {errors.nameOnCard}
                            </p>
                          )}
                        </div>
                      </div>
                    )}
                  </>
                )}
              </div>
              {data?.entityType != 'Wallet' &&
                data.isWalletEnabled &&
                getNetAmount > 0 &&
                data.walletBalance > 0 && (
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="useWalletBalance"
                      name="useWalletBalance"
                      checked={useWalletBalance}
                      onChange={(e) => setUseWalletBalance(e.target.checked)}
                      className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <label
                      htmlFor="useWalletBalance"
                      className="ml-2 block text-base font-bold"
                    >
                      Use wallet balance
                    </label>
                  </div>
                )}
              <div>
                <h2 className="text-xl font-bold mb-4">Billing address</h2>
                <div className="space-y-4">
                  <div>
                    <label
                      htmlFor="country"
                      className="block text-sm font-bold"
                    >
                      Country <span className="text-red-500">*</span>
                    </label>
                    <CountrySelect
                      containerClassName="mt-1"
                      inputClassName=""
                      onChange={(_country: any) => {
                        setCountry(_country)
                        // Reset state and city when country changes
                        setCurrentState('')
                        setCurrentCity('')
                        // Clear error when user selects a country
                        if (errors.country) {
                          setErrors((prev) => ({ ...prev, country: '' }))
                        }
                        if (errors.state) {
                          setErrors((prev) => ({ ...prev, state: '' }))
                        }
                        if (errors.city) {
                          setErrors((prev) => ({ ...prev, city: '' }))
                        }
                      }}
                      onTextChange={(value: any) => {
                        // Handle keyboard input and backspace
                        if (!value || value.trim() === '') {
                          setCountry('')
                          setCurrentState('')
                          setCurrentCity('')
                        }
                      }}
                      onFocus={(e) => e.target.click()}
                      placeHolder="Select Country"
                      defaultValue={country}
                    />
                    {errors.country && (
                      <p className="text-red-500 text-xs mt-1">
                        {errors.country}
                      </p>
                    )}
                  </div>

                  <div>
                    <label
                      htmlFor="addressLine1"
                      className="block text-sm font-bold"
                    >
                      Address line 1 <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      id="addressLine1"
                      name="addressLine1"
                      value={addressLine1}
                      onChange={(e) => {
                        setAddressLine1(e.target.value)
                        // Clear error when user types
                        if (errors.addressLine1) {
                          setErrors((prev) => ({ ...prev, addressLine1: '' }))
                        }
                      }}
                      className={`mt-1 block w-full px-3 py-2 border text-sm rounded-lg ${
                        errors.addressLine1
                          ? 'border-red-500'
                          : 'border-color-grey-2'
                      }`}
                    />
                    {errors.addressLine1 && (
                      <p className="text-red-500 text-xs mt-1">
                        {errors.addressLine1}
                      </p>
                    )}
                  </div>

                  <div>
                    <label htmlFor="state" className="block text-sm font-bold">
                      State <span className="text-red-500">*</span>
                    </label>
                    <StateSelect
                      countryid={country?.id}
                      containerClassName="mt-1"
                      inputClassName=""
                      onChange={(_state) => {
                        setCurrentState(_state)
                        // Reset city when state changes
                        setCurrentCity('')
                        // Clear error when user selects a state
                        if (errors.state) {
                          setErrors((prev) => ({ ...prev, state: '' }))
                        }
                        if (errors.city) {
                          setErrors((prev) => ({ ...prev, city: '' }))
                        }
                      }}
                      onTextChange={(value: any) => {
                        // Handle keyboard input and backspace
                        if (!value || value.trim() === '') {
                          setCurrentState('')
                          setCurrentCity('')
                        }
                      }}
                      onFocus={(e) => e.target.click()}
                      defaultValue={currentState}
                      placeHolder="Select State"
                    />
                    {errors.state && (
                      <p className="text-red-500 text-xs mt-1">
                        {errors.state}
                      </p>
                    )}
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label htmlFor="city" className="block text-sm font-bold">
                        City <span className="text-red-500">*</span>
                      </label>
                      <CitySelect
                        countryid={country?.id}
                        stateid={currentState?.id}
                        containerClassName="mt-1"
                        inputClassName=""
                        onChange={(_city) => {
                          setCurrentCity(_city)
                          // Clear error when user selects a city
                          if (errors.city) {
                            setErrors((prev) => ({ ...prev, city: '' }))
                          }
                        }}
                        onTextChange={(value: any) => {
                          // Handle keyboard input and backspace
                          if (!value || value.trim() === '') {
                            setCurrentCity('')
                          }
                        }}
                        onFocus={(e) => e.target.click()}
                        defaultValue={currentCity}
                        value={currentCity}
                        placeHolder="Select City"
                      />
                      {errors.city && (
                        <p className="text-red-500 text-xs mt-1">
                          {errors.city}
                        </p>
                      )}
                    </div>
                    <div>
                      <label
                        htmlFor="postalCode"
                        className="block text-sm font-bold"
                      >
                        Postal Code <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        id="postalCode"
                        name="postalCode"
                        value={postalCode}
                        onChange={(e) => {
                          setPostalCode(e.target.value)
                          // Clear error when user types
                          if (errors.postalCode) {
                            setErrors((prev) => ({ ...prev, postalCode: '' }))
                          }
                        }}
                        className={`mt-1 block w-full px-3 py-2 border text-sm rounded-lg ${
                          errors.postalCode
                            ? 'border-red-500'
                            : 'border-color-grey-2'
                        }`}
                      />
                      {errors.postalCode && (
                        <p className="text-red-500 text-xs mt-1">
                          {errors.postalCode}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              </div>
              {/* Checkbox for same as billing */}
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="sameAsBilling"
                  checked={sameAsBilling}
                  onChange={() => setSameAsBilling(!sameAsBilling)}
                  className="h-4 w-4 text-blue-600 rounded"
                />
                <label
                  htmlFor="sameAsBilling"
                  className="ml-2 text-sm font-bold"
                >
                  Shipping address same as billing address
                </label>
              </div>
              {!sameAsBilling && (
                <div>
                  <h2 className="text-xl font-bold mb-4">Shipping address</h2>
                  <div className="space-y-4">
                    <div>
                      <label
                        htmlFor="shippingCountry"
                        className="block text-sm font-bold"
                      >
                        Country <span className="text-red-500">*</span>
                      </label>
                      <CountrySelect
                        containerClassName="mt-1"
                        inputClassName=""
                        onChange={(_country: any) => {
                          setShippingCountry(_country)
                          // Reset state and city when country changes
                          setShippingState('')
                          setShippingCity('')
                          // Clear error when user selects a country
                          if (errors.shippingCountry) {
                            setErrors((prev) => ({
                              ...prev,
                              shippingCountry: '',
                            }))
                          }
                          if (errors.shippingState) {
                            setErrors((prev) => ({
                              ...prev,
                              shippingState: '',
                            }))
                          }
                          if (errors.shippingCity) {
                            setErrors((prev) => ({
                              ...prev,
                              shippingCity: '',
                            }))
                          }
                        }}
                        onTextChange={(value: any) => {
                          // Handle keyboard input and backspace
                          if (!value || value.trim() === '') {
                            setShippingCountry('')
                            setShippingState('')
                            setShippingCity('')
                          }
                        }}
                        onFocus={(e) => e.target.click()}
                        placeHolder="Select Country"
                      />
                      {errors.shippingCountry && (
                        <p className="text-red-500 text-xs mt-1">
                          {errors.shippingCountry}
                        </p>
                      )}
                    </div>

                    <div>
                      <label
                        htmlFor="shippingAddressLine1"
                        className="block text-sm font-bold"
                      >
                        Address line 1 <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        id="shippingAddressLine1"
                        name="shippingAddressLine1"
                        value={shippingAddressLine1}
                        onChange={(e) => {
                          setShippingAddressLine1(e.target.value)
                          // Clear error when user types
                          if (errors.shippingAddressLine1) {
                            setErrors((prev) => ({
                              ...prev,
                              shippingAddressLine1: '',
                            }))
                          }
                        }}
                        className={`mt-1 block w-full px-3 py-2 border text-sm rounded-lg ${
                          errors.shippingAddressLine1
                            ? 'border-red-500'
                            : 'border-color-grey-2'
                        }`}
                      />
                      {errors.shippingAddressLine1 && (
                        <p className="text-red-500 text-xs mt-1">
                          {errors.shippingAddressLine1}
                        </p>
                      )}
                    </div>

                    <div>
                      <label
                        htmlFor="shippingState"
                        className="block text-sm font-bold"
                      >
                        State <span className="text-red-500">*</span>
                      </label>
                      <StateSelect
                        countryid={shippingCountry?.id}
                        containerClassName="mt-1"
                        inputClassName=""
                        onChange={(_state) => {
                          setShippingState(_state)
                          // Reset city when state changes
                          setShippingCity('')
                          // Clear error when user selects a state
                          if (errors.shippingState) {
                            setErrors((prev) => ({
                              ...prev,
                              shippingState: '',
                            }))
                          }
                          if (errors.shippingCity) {
                            setErrors((prev) => ({
                              ...prev,
                              shippingCity: '',
                            }))
                          }
                        }}
                        onTextChange={(value: any) => {
                          // Handle keyboard input and backspace
                          if (!value || value.trim() === '') {
                            setShippingState('')
                            setShippingCity('')
                          }
                        }}
                        onFocus={(e) => e.target.click()}
                        defaultValue={shippingState}
                        placeHolder="Select State"
                      />
                      {errors.shippingState && (
                        <p className="text-red-500 text-xs mt-1">
                          {errors.shippingState}
                        </p>
                      )}
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label
                          htmlFor="shippingCity"
                          className="block text-sm font-bold"
                        >
                          City <span className="text-red-500">*</span>
                        </label>
                        <CitySelect
                          countryid={shippingCountry?.id}
                          stateid={shippingState?.id}
                          containerClassName="mt-1"
                          inputClassName=""
                          onChange={(_city) => {
                            setShippingCity(_city)
                            // Clear error when user selects a city
                            if (errors.shippingCity) {
                              setErrors((prev) => ({
                                ...prev,
                                shippingCity: '',
                              }))
                            }
                          }}
                          onTextChange={(value: any) => {
                            // Handle keyboard input and backspace
                            if (!value || value.trim() === '') {
                              setShippingCity('')
                            }
                          }}
                          onFocus={(e) => e.target.click()}
                          defaultValue={shippingCity}
                          placeHolder="Select City"
                        />
                        {errors.shippingCity && (
                          <p className="text-red-500 text-xs mt-1">
                            {errors.shippingCity}
                          </p>
                        )}
                      </div>
                      <div>
                        <label
                          htmlFor="shippingPostalCode"
                          className="block text-sm font-bold"
                        >
                          Postal Code <span className="text-red-500">*</span>
                        </label>
                        <input
                          type="text"
                          id="shippingPostalCode"
                          name="shippingPostalCode"
                          value={shippingPostalCode}
                          onChange={(e) => {
                            setShippingPostalCode(e.target.value)
                            // Clear error when user types
                            if (errors.shippingPostalCode) {
                              setErrors((prev) => ({
                                ...prev,
                                shippingPostalCode: '',
                              }))
                            }
                          }}
                          className={`mt-1 block w-full px-3 py-2 border text-sm rounded-lg ${
                            errors.shippingPostalCode
                              ? 'border-red-500'
                              : 'border-color-grey-2'
                          }`}
                        />
                        {errors.shippingPostalCode && (
                          <p className="text-red-500 text-xs mt-1">
                            {errors.shippingPostalCode}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              )}
              {getNetAmount > 0 && (
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="coverProcessingFee"
                    name="coverProcessingFee"
                    checked={coverProcessingFee}
                    onChange={(e) => setCoverProcessingFee(e.target.checked)}
                    className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <label
                    htmlFor="coverProcessingFee"
                    className="ml-2 block text-sm font-bold"
                  >
                    I will cover the processing fee (3%)
                  </label>
                </div>
              )}
              {!isPaymentSuccessful && (
                <CustomButton
                  title={getNetAmount > 0 ? 'Pay Now' : 'Proceed to purchase'}
                  isDisabled={cardDataResponse.isFetching}
                  isLoading={checkoutResponse.isFetching}
                  onClick={handleSubmit}
                />
              )}
              <p className="text-xs text-center text-gray-600">
                {`By clicking 'Pay now', you agree to the `}
                <a href="#" className="text-gray-900 underline">
                  {'Privacy Policy '}
                </a>
                {'and '}
                <a href="#" className="text-gray-900 underline">
                  {'Terms & Conditions'}
                </a>
                .
              </p>
            </div>
          </div>

          <div className="w-full md:w-80">
            <div className="bg-white p-6 rounded-2xl">
              <h3 className="text-lg font-semibold mb-4">Price details</h3>
              <div className="space-y-2">
                <div className="flex justify-between font-semibold text-xl">
                  <span>Subtotal</span>
                  <span>
                    ${(data.amount + (data?.discountedAmount ?? 0)).toFixed(2)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium">Discount</span>
                  <span className="text-sm font-medium">
                    -${(data?.discountedAmount ?? 0).toFixed(2)}
                  </span>
                </div>
                {useWalletBalance && data.isWalletEnabled && (
                  <div className="flex justify-between">
                    <span className="text-sm font-medium">Wallet balance</span>
                    <span className="text-sm font-medium">
                      -$
                      {(data.amount > data.walletBalance
                        ? data.walletBalance
                        : data.amount
                      ).toFixed(2)}
                    </span>
                  </div>
                )}
                {convenienceFees > 0 && coverProcessingFee && (
                  <div className="flex justify-between">
                    <span className="text-sm font-medium">
                      Processing Fee (3%)
                    </span>
                    <span className="text-sm font-medium">
                      +${convenienceFees.toFixed(2)}
                    </span>
                  </div>
                )}
                <div className="flex justify-between font-bold text-xl pt-2 border-t">
                  <span>Due amount</span>
                  <span>
                    $
                    {(Number(getNetAmount) + Number(convenienceFees)).toFixed(
                      2,
                    )}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
      {modal}
      {youthModal}
    </>
  )
}

export default PaymentForm
