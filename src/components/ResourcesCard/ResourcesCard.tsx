import Link from 'next/link'
import Right<PERSON>rrow from '../Icons/RightArrow'

interface ResourcesCardProps {
  title?: string
  description?: string
}

const ResourcesCard: React.FC<ResourcesCardProps> = ({
  title = 'Parent',
  description = 'Access Class Guidelines, Zoom Info, and Forms',
}) => {
  return (
    <Link
      className="bg-color-black text-white rounded-xl p-4 flex justify-between items-center w-full"
      href={'/pages/teacher-resources'}
    >
      <div>
        <h2 className="text-[24px] font-bold leading-[36px] tracking-normal">
          <span className="text-white">{title} </span>
          <span className="text-color-yellow">Resources</span>
        </h2>
        <p className="text-xs text-color-grey-1 font-normal leading-[18px] tracking-normal">
          {description}
        </p>
      </div>
      <div className="w-6 h-6 bg-neutral-white flex items-center justify-center text-black rounded-full">
        <RightArrow dark={true} />
      </div>
    </Link>
  )
}

export default ResourcesCard
