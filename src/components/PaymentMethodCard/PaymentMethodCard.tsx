'use client'
import React, { useEffect } from 'react'
import DeleteIcon from '../Icons/DeleteIcon'
import { useApi } from '@/hooks/useApi'
import { deletePaymentMethod } from '@/lib/actions/customerProfile.actions'
import VisaIcon from '../Icons/CardsIcon/VisaIcon'
import CardIcon from '../CardIcon/CardIcon'

const PaymentMethodCard = ({
  data,
  setPaymentMethods,
}: {
  data: any
  setPaymentMethods: any
}) => {
  const [showDelete, setShowDelete] = React.useState(false)
  const [response, removeCart] = useApi((access: string, id: string) =>
    deletePaymentMethod(access, id),
  )

  useEffect(() => {
    if (response.isSuccess) {
      setPaymentMethods((prev: any) =>
        prev.filter(
          (item: any) =>
            item.customerPaymentProfileId !== data.customerPaymentProfileId,
        ),
      )
    }
  }, [response])

  return (
    <div
      className="p-5 bg-white rounded-2xl w-full relative border-2"
      onMouseEnter={() => setShowDelete(true)}
      onMouseLeave={() => setShowDelete(false)}
      onTouchStart={() => setShowDelete(true)}
      onTouchEnd={() => setShowDelete(false)}
    >
      <div className="flex items-center justify-between mt-3">
        <div className="grid grid-cols-2 gap-x-4 gap-y-1">
          <h1 className="text-sm font-semibold"> Card</h1>
          <h1 className="text-sm font-semibold">Type</h1>
          <h2 className="text-base font-semibold">
            •••• •••• {data?.payment?.creditCard?.cardNumber}
          </h2>
          <h2 className="text-sm font-semibold">
            {data?.payment?.creditCard?.expirationDate}
          </h2>
        </div>
        {showDelete && (
          <div className="absolute -right-2 -top-2 rounded-full bg-color-grey-2 p-2">
            {response.isFetching ? (
              <div className="delete-icon-loader"></div>
            ) : (
              <DeleteIcon
                handleClick={() => removeCart(data?.customerPaymentProfileId)}
              />
            )}
          </div>
        )}
        <CardIcon type={data?.payment?.creditCard?.cardType} />
      </div>
    </div>
  )
}

export default PaymentMethodCard
