import Image from 'next/image'
import React from 'react'

const PurchasedItem = ({ item }: { item: any }) => {
  return (
    <div className="flex items-center gap-5 mb-4">
      {item?.Product2?.Image__c && (
        <Image
          width={45}
          height={45}
          src={item?.Product2?.Image__c}
          alt="User avatar"
          className="w-32 h-20 object-cover rounded-lg"
        />
      )}
      <div className="flex flex-col">
        <h1 className="font-bold text-base">{item?.Product2?.Name}</h1>
        {item?.Product_Family__c == 'Donation' && (
          <h3 className="font-medium text-sm">Qty: {item?.Quantity}</h3>
        )}
        <h2 className="font-medium text-sm">Total price: {item?.TotalPrice}</h2>
        {item?.Product_Family__c == 'Donation' && item?.recipients && (
          <div className="mt-1">
            <p className="font-bold text-xs">Send to:</p>
            <h2 className="text-xs text-color-grey-3">
              {item.recipients?.join(', ')}
            </h2>
          </div>
        )}
      </div>
    </div>
  )
}

export default PurchasedItem
