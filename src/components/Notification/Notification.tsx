'use client'
import <PERSON> from 'next/link'
import React, { useEffect } from 'react'
import Bell from '../Icons/Bell'
import { getNotificationsCount } from '@/lib/actions/notification.actions'
import { useAuth } from '@/contexts/AuthContext'

const Notification = () => {
  const { user } = useAuth()
  const [count, setCount] = React.useState<any>(null)
  useEffect(() => {
    const fetchNotification = async () => {
      try {
        if (user && user?.id) {
          const notificationCount = await getNotificationsCount(user?.id)
          setCount(notificationCount?.data?.data)
        }
      } catch (error) {
        console.error('Error fetching notification count:', error)
      }
    }
    fetchNotification()
  }, [user])
  return (
    <div className="relative">
      <Link href={'/notifications'}>
        <Bell />
      </Link>
      {count > 0 && (
        <div
          className={`w-4 h-4 flex items-center justify-center text-xs text-white bg-color-red-1 rounded-full absolute -top-1 right-0`}
        >
          {count > 9 ? '9+' : count}
        </div>
      )}
    </div>
  )
}

export default Notification
