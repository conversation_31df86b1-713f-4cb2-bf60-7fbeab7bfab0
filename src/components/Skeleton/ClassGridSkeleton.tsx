import React from 'react'

type ClassGridSkeletonProps = {
  count?: number
}

export default function ClassGridSkeleton({
  count = 6,
}: ClassGridSkeletonProps) {
  return (
    <div className="p-3 md:p-7 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
      {Array.from({ length: count }).map((_, idx) => (
        <div
          key={idx}
          className="bg-white rounded-2xl overflow-hidden animate-pulse"
        >
          {/* Image placeholder */}
          <div className="w-full h-0 pb-[56.25%] bg-gray-200" />
          <div className="p-4 space-y-3">
            {/* Title skeleton */}
            <div className="h-6 bg-gray-200 rounded w-3/4" />
            {/* Subtitle skeleton */}
            <div className="h-4 bg-gray-200 rounded w-1/2" />
            {/* Details row */}
            <div className="flex items-center gap-4">
              <div className="w-5 h-5 bg-gray-200 rounded" />
              <div className="h-4 bg-gray-200 rounded w-1/4" />
              <div className="w-5 h-5 bg-gray-200 rounded ml-4" />
              <div className="h-4 bg-gray-200 rounded w-1/4" />
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}
