import React from 'react'

const skeletonPlans = [1, 2, 3, 4]

const MembershipPlansSkeleton = () => {
  return (
    <div className="bg-white rounded-xl p-8">
      <div className="h-8 w-1/3 mb-6 bg-gray-200 rounded animate-pulse" />
      <div className="mb-8 h-10 w-40 bg-gray-200 rounded animate-pulse" />
      <div className="flex flex-col gap-6">
        {skeletonPlans.map((_, idx) => (
          <div
            key={idx}
            className="rounded-2xl p-8 bg-white border shadow-sm"
            style={{ marginBottom: '0.5rem' }}
          >
            <div className="grid grid-cols-1 md:grid-cols-12 items-center gap-4">
              {/* Info */}
              <div className="mb-4 md:mb-0 md:col-span-6">
                <div className="h-7 w-32 mb-2 bg-gray-200 rounded animate-pulse" />
                <div className="h-5 w-60 mb-2 bg-gray-200 rounded animate-pulse" />
                <div className="h-4 w-24 bg-gray-200 rounded animate-pulse" />
              </div>
              {/* Price */}
              <div className="flex justify-end items-center mb-4 md:mb-0 md:col-span-3">
                <div className="h-7 w-20 bg-gray-200 rounded animate-pulse" />
              </div>
              {/* Button */}
              <div className="flex flex-col items-center justify-center gap-2 md:col-span-3">
                <div className="h-12 w-40 bg-gray-200 rounded-lg animate-pulse" />
                <div className="h-4 w-20 bg-gray-200 rounded animate-pulse" />
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

export default MembershipPlansSkeleton
