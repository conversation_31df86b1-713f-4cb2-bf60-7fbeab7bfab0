import React from 'react'

type EventCardSkeletonProps = {
  count?: number
  weekView?: boolean
}

export default function EventCardSkeleton({
  count = 3,
  weekView = false,
}: EventCardSkeletonProps) {
  if (weekView) {
    return (
      <>
        {Array.from({ length: count }).map((_, idx) => (
          <div
            key={idx}
            className="p-4 rounded-lg mb-2 bg-gray-100 animate-pulse border border-gray-200"
          >
            {/* Image Skeleton */}
            <div className="h-16 bg-gray-200 rounded-md w-full mb-2" />

            {/* Title Skeleton - adjusted for potential two lines */}
            <div className="space-y-1.5">
              <div className="h-3 bg-gray-200 rounded w-3/4" />
              <div className="h-3 bg-gray-200 rounded w-1/2" />
            </div>

            {/* Date and Time Skeleton */}
            <div className="h-3 bg-gray-200 rounded w-1/2 mt-2" />

            {/* Seats Information Skeleton */}
            <div className="flex items-center mt-2">
              <div className="w-4 h-4 bg-gray-200 rounded-full mr-2" />
              <div className="h-3 bg-gray-200 rounded w-24" />
            </div>
          </div>
        ))}
      </>
    )
  }

  // Regular view skeleton (for calendar grid view)
  return (
    <>
      {Array.from({ length: count }).map((_, idx) => (
        <div
          key={idx}
          className="cursor-pointer relative bg-gray-100 border border-gray-200 rounded animate-pulse"
        >
          <div className="w-full p-1">
            <div className="h-3 bg-gray-200 rounded w-full" />
          </div>
        </div>
      ))}
    </>
  )
}
