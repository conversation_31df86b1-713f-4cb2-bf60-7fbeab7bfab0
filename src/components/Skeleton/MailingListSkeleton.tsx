import React from 'react'

type MailingListSkeletonProps = {
  count?: number
}

export default function MailingListSkeleton({
  count = 4,
}: MailingListSkeletonProps) {
  return (
    <div className="space-y-4 w-full">
      {Array.from({ length: count }).map((_, idx) => (
        <div
          key={idx}
          className="flex items-center justify-between p-4 bg-white rounded-xl shadow animate-pulse"
        >
          {/* Arrow + Title */}
          <div className="flex items-center gap-4 flex-1">
            <div className="w-4 h-4 bg-gray-200 rounded" />
            <div className="h-4 bg-gray-200 rounded w-1/2" />
          </div>
          {/* Actions: Notify, Edit, Delete */}
          <div className="flex items-center gap-3 ml-4">
            <div className="w-20 h-6 bg-gray-200 rounded" />
            <div className="w-6 h-6 bg-gray-200 rounded" />
            <div className="w-6 h-6 bg-gray-200 rounded" />
          </div>
        </div>
      ))}
    </div>
  )
}
