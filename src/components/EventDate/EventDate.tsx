'use client'
import React, { useState } from 'react'
import PrevNextButton from '../PrevNextButton/PrevNextButton'
import UpArrow from '../Icons/UpArrow'
import { CalenderDropDown } from '../CalenderDropDown/CalenderDropDown'
import { monthNames } from '@/utils/constants'
import MonthSelector from '../MonthSelector/MonthSelector'

const EventDate = ({
  currentDate,
  setCurrentDate,
  calendarView = true,
  handlePrevNextClick = () => {},
}: {
  currentDate: any
  setCurrentDate: any
  handlePrevNextClick?: any
  calendarView?: boolean
}) => {
  const [showCalender, setShowCalender] = useState<boolean>(false)
  const [todayMonth, setTodayMonth] = useState<any>(new Date())

  return (
    <div className="relative flex justify-end">
      {calendarView ? (
        <CalenderDropDown
          show={showCalender}
          setShowCalender={setShowCalender}
          currentDate={currentDate}
          setCurrentDate={setCurrentDate}
          todayMonth={todayMonth}
          setTodayMonth={setTodayMonth}
        />
      ) : (
        <div className="flex items-center gap-6">
          <MonthSelector
            currentDate={currentDate}
            setCurrentDate={setCurrentDate}
          />
          <PrevNextButton
            {...handlePrevNextClick()}
            notDisable={true}
            showBorder={true}
          />
        </div>
      )}
      <div className="flex gap-2 items-center">
        {calendarView && (
          <>
            <h2
              className="font-medium text-base cursor-pointer"
              onClick={() => setShowCalender(!showCalender)}
            >
              {`Now-${monthNames[todayMonth.getMonth()]}`}
            </h2>
            <UpArrow showDownArrow={!showCalender} />
          </>
        )}
      </div>
    </div>
  )
}

export default EventDate
