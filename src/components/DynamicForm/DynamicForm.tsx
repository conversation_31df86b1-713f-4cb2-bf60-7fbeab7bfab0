'use client'
import { createFormResponse } from '@/lib/actions/form.actions'
import { FormEvent, useState, useRef } from 'react'
import { toast } from 'react-toastify'
import { useFileUpload } from '@/hooks/useFileUpload'
import { FormField, FormSchema } from '@/utils/interface'

const DynamicForm = ({ id, data }: { id?: string; data: FormSchema }) => {
  const formRef = useRef<HTMLFormElement>(null)
  const [uploadedFiles, setUploadedFiles] = useState<Record<string, string>>({})
  const [isFileUploading, setIsFileUploading] = useState<
    Record<string, boolean>
  >({})
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Create a general file upload hook for all dynamic fields
  const { handleFileUpload: baseHandleFileUpload } = useFileUpload({
    filePrefix: 'DynamicForm',
    onSuccess: (fileUrl) => {
      // This will be handled in the custom handleFileChange
    },
    onError: (error) => {
      console.error('File upload error:', error)
    },
  })

  const handleFileChange = async (
    e: React.ChangeEvent<HTMLInputElement>,
    fieldName: string,
  ) => {
    setIsFileUploading({ ...isFileUploading, [fieldName]: true })

    const uploadedUrl = await baseHandleFileUpload(e)
    if (uploadedUrl) {
      setUploadedFiles({ ...uploadedFiles, [fieldName]: uploadedUrl })
    }

    setIsFileUploading({ ...isFileUploading, [fieldName]: false })
  }

  const clearForm = () => {
    // Reset the form
    if (formRef.current) {
      formRef.current.reset()
    }

    // Clear uploaded files state
    setUploadedFiles({})

    // Clear file upload states
    setIsFileUploading({})

    // Clear any errors
    setErrors({})

    // Clear file input values manually (in case form.reset() doesn't work for file inputs)
    const fileInputs = formRef.current?.querySelectorAll('input[type="file"]')
    fileInputs?.forEach((input: any) => {
      input.value = ''
    })
  }

  const validateForm = (
    formData: Record<string, any>,
  ): Record<string, string> => {
    const validationErrors: Record<string, string> = {}

    data.fields.forEach((field) => {
      const extendedField = field as any

      // Skip validation for display text fields
      if (extendedField.isDisplayText) return

      if (field.required) {
        const value = formData[field.apiName]

        // Check if field is empty
        if (!value || (typeof value === 'string' && value.trim() === '')) {
          validationErrors[field.apiName] = `${field.label} is required`
        }

        // Special validation for file uploads
        if (extendedField.isFileUpload && !uploadedFiles[field.apiName]) {
          validationErrors[field.apiName] = `${field.label} is required`
        }
      }

      // Additional validation for specific field types
      if (formData[field.apiName]) {
        const extendedField = field as any
        const value = formData[field.apiName]

        // Email validation
        if (extendedField.isEmail && value) {
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
          if (!emailRegex.test(value)) {
            validationErrors[field.apiName] =
              'Please enter a valid email address'
          }
        }

        // Phone validation
        if (extendedField.isPhone && value) {
          const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/
          if (!phoneRegex.test(value.replace(/[\s\-\(\)]/g, ''))) {
            validationErrors[field.apiName] =
              'Please enter a valid phone number'
          }
        }
      }
    })

    return validationErrors
  }

  const onSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setIsSubmitting(true)
    setErrors({}) // Clear previous errors

    const formData = new FormData(e.currentTarget)
    const formDataObject: Record<string, any> = {}

    formData.forEach((value, key) => {
      if (formDataObject[key]) {
        if (!Array.isArray(formDataObject[key])) {
          formDataObject[key] = [formDataObject[key].toString()]
        }
        formDataObject[key].push(value.toString())
      } else {
        formDataObject[key] = value.toString()
      }
    })

    // Add uploaded file URLs to form data
    Object.keys(uploadedFiles).forEach((fieldName) => {
      formDataObject[fieldName] = uploadedFiles[fieldName]
    })

    // Validate form
    const validationErrors = validateForm(formDataObject)

    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors)
      setIsSubmitting(false)
      toast.error('Please fix the errors in the form', { autoClose: 2000 })
      return
    }

    // Format data according to new schema structure
    const answers: Record<string, any> = {}

    // Build answers object using apiName as keys
    data.fields.forEach((field) => {
      const extendedField = field as any

      // Skip display text fields as they don't have user input
      if (extendedField.isDisplayText) return

      const value = formDataObject[field.apiName]
      if (value !== undefined && value !== '') {
        answers[field.apiName] = value
      }
    })

    // Create the submission schema
    const submissionData = {
      schemaVersion: 1,
      schemaSavedAt: new Date().toISOString(),
      submittedAt: new Date().toISOString(),
      answers: answers,
    }

    if (id) {
      try {
        const response = await createFormResponse(id, submissionData)
        toast.success('Form successfully submitted!', { autoClose: 1000 })
        clearForm() // Clear the form after successful submission
      } catch (error) {
        toast.error('Something went wrong!' + error, { autoClose: 1500 })
        console.error('Error saving form response:', error)
      }
    }

    setIsSubmitting(false)
  }

  const renderField = (field: FormField) => {
    // Type assertion for extended properties
    const extendedField = field as any

    // Handle display text - no input needed
    if (extendedField.isDisplayText) {
      return (
        <div className="bg-gray-50 p-4 rounded-md border">
          <p className="text-gray-700">{extendedField.displayText}</p>
        </div>
      )
    }

    // Handle file upload
    if (extendedField.isFileUpload) {
      const hasError = errors[field.apiName]
      return (
        <div>
          <input
            id={field.apiName}
            type="file"
            name={field.apiName}
            required={field.required}
            className={`w-full p-2 border rounded-md focus:outline-none focus:ring-2 ${
              hasError
                ? 'border-red-500 focus:ring-red-300'
                : 'border-gray-300 focus:ring-orange-300'
            }`}
            onChange={(e) => handleFileChange(e, field.apiName)}
            disabled={isFileUploading[field.apiName]}
          />
          {isFileUploading[field.apiName] && (
            <p className="text-sm text-blue-600 mt-1">Uploading...</p>
          )}
          {uploadedFiles[field.apiName] && (
            <p className="text-sm text-green-600 mt-1">
              File uploaded successfully!
            </p>
          )}
          {hasError && (
            <p className="text-sm text-red-600 mt-1">{errors[field.apiName]}</p>
          )}
        </div>
      )
    }

    // Handle radio buttons
    if (
      extendedField.isRadio &&
      extendedField.hasOptions &&
      field.options &&
      field.options.length > 0
    ) {
      const hasError = errors[field.apiName]
      return (
        <div>
          <div className="space-y-2">
            {field.options.map((option: any, j: number) => (
              <div key={j} className="flex items-center">
                <input
                  id={`${field.apiName}-${option.value}`}
                  type="radio"
                  name={field.apiName}
                  value={option.value}
                  required={field.required}
                  className="mr-2 h-4 w-4 text-orange-600 focus:ring-orange-500"
                />
                <label
                  htmlFor={`${field.apiName}-${option.value}`}
                  className="text-gray-700"
                >
                  {option.label}
                </label>
              </div>
            ))}
          </div>
          {hasError && (
            <p className="text-sm text-red-600 mt-1">{errors[field.apiName]}</p>
          )}
        </div>
      )
    }

    // Handle checkbox group
    if (
      extendedField.isCheckboxGroup &&
      extendedField.hasOptions &&
      field.options &&
      field.options.length > 0
    ) {
      return (
        <div className="space-y-2">
          {field.options.map((option: any, j: number) => (
            <div key={j} className="flex items-center">
              <input
                id={`${field.apiName}-${option.value}`}
                type="checkbox"
                name={field.apiName}
                value={option.value}
                className="mr-2 h-4 w-4 text-orange-600 focus:ring-orange-500 rounded"
              />
              <label
                htmlFor={`${field.apiName}-${option.value}`}
                className="text-gray-700"
              >
                {option.label}
              </label>
            </div>
          ))}
        </div>
      )
    }

    // Handle short text
    if (extendedField.isShortText) {
      const hasError = errors[field.apiName]
      return (
        <div>
          <input
            id={field.apiName}
            type="text"
            name={field.apiName}
            required={field.required}
            className={`w-full p-2 border rounded-md focus:outline-none focus:ring-2 ${
              hasError
                ? 'border-red-500 focus:ring-red-300'
                : 'border-gray-300 focus:ring-orange-300'
            }`}
            placeholder={field.placeholder || ''}
          />
          {hasError && (
            <p className="text-sm text-red-600 mt-1">{errors[field.apiName]}</p>
          )}
        </div>
      )
    }

    // Handle email
    if (extendedField.isEmail) {
      const hasError = errors[field.apiName]
      return (
        <div>
          <input
            id={field.apiName}
            type="email"
            name={field.apiName}
            required={field.required}
            className={`w-full p-2 border rounded-md focus:outline-none focus:ring-2 ${
              hasError
                ? 'border-red-500 focus:ring-red-300'
                : 'border-gray-300 focus:ring-orange-300'
            }`}
            placeholder={field.placeholder || ''}
          />
          {hasError && (
            <p className="text-sm text-red-600 mt-1">{errors[field.apiName]}</p>
          )}
        </div>
      )
    }

    // Handle phone
    if (extendedField.isPhone) {
      const hasError = errors[field.apiName]
      return (
        <div>
          <input
            id={field.apiName}
            type="tel"
            name={field.apiName}
            required={field.required}
            className={`w-full p-2 border rounded-md focus:outline-none focus:ring-2 ${
              hasError
                ? 'border-red-500 focus:ring-red-300'
                : 'border-gray-300 focus:ring-orange-300'
            }`}
            placeholder={field.placeholder || ''}
          />
          {hasError && (
            <p className="text-sm text-red-600 mt-1">{errors[field.apiName]}</p>
          )}
        </div>
      )
    }

    // Handle date
    if (extendedField.isDate) {
      const hasError = errors[field.apiName]
      return (
        <div>
          <input
            id={field.apiName}
            type="date"
            name={field.apiName}
            required={field.required}
            className={`w-full p-2 border rounded-md focus:outline-none focus:ring-2 ${
              hasError
                ? 'border-red-500 focus:ring-red-300'
                : 'border-gray-300 focus:ring-orange-300'
            }`}
          />
          {hasError && (
            <p className="text-sm text-red-600 mt-1">{errors[field.apiName]}</p>
          )}
        </div>
      )
    }

    // Handle long text
    if (extendedField.isLongText) {
      const hasError = errors[field.apiName]
      return (
        <div>
          <textarea
            id={field.apiName}
            name={field.apiName}
            required={field.required}
            className={`w-full p-2 border rounded-md focus:outline-none focus:ring-2 ${
              hasError
                ? 'border-red-500 focus:ring-red-300'
                : 'border-gray-300 focus:ring-orange-300'
            }`}
            placeholder={field.placeholder || ''}
            rows={4}
          />
          {hasError && (
            <p className="text-sm text-red-600 mt-1">{errors[field.apiName]}</p>
          )}
        </div>
      )
    }

    // Handle picklist/dropdown
    if (
      extendedField.isPicklist &&
      extendedField.hasOptions &&
      field.options &&
      field.options.length > 0
    ) {
      const hasError = errors[field.apiName]
      return (
        <div>
          <select
            id={field.apiName}
            name={field.apiName}
            required={field.required}
            className={`w-full p-2 border rounded-md focus:outline-none focus:ring-2 ${
              hasError
                ? 'border-red-500 focus:ring-red-300'
                : 'border-gray-300 focus:ring-orange-300'
            }`}
          >
            <option value="">Select an option</option>
            {field.options.map((option: any, j: number) => (
              <option key={j} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
          {hasError && (
            <p className="text-sm text-red-600 mt-1">{errors[field.apiName]}</p>
          )}
        </div>
      )
    }

    // Fallback for legacy types or unknown types
    const hasError = errors[field.apiName]

    switch (field.type) {
      case 'number':
        return (
          <div>
            <input
              id={field.apiName}
              type="number"
              name={field.apiName}
              required={field.required}
              className={`w-full p-2 border rounded-md focus:outline-none focus:ring-2 ${
                hasError
                  ? 'border-red-500 focus:ring-red-300'
                  : 'border-gray-300 focus:ring-orange-300'
              }`}
              placeholder={field.placeholder || ''}
            />
            {hasError && (
              <p className="text-sm text-red-600 mt-1">
                {errors[field.apiName]}
              </p>
            )}
          </div>
        )

      case 'header':
        return <h1 className="text-2xl font-bold mb-4">{field.label}</h1>

      case 'paragraph':
        return <p className="text-gray-700 mb-4">{field.label}</p>

      default:
        // Default to text input for unknown types
        return (
          <div>
            <input
              id={field.apiName}
              type="text"
              name={field.apiName}
              required={field.required}
              className={`w-full p-2 border rounded-md focus:outline-none focus:ring-2 ${
                hasError
                  ? 'border-red-500 focus:ring-red-300'
                  : 'border-gray-300 focus:ring-orange-300'
              }`}
              placeholder={field.placeholder || ''}
            />
            {hasError && (
              <p className="text-sm text-red-600 mt-1">
                {errors[field.apiName]}
              </p>
            )}
          </div>
        )
    }
  }

  return (
    <form ref={formRef} onSubmit={onSubmit} className="rounded-lg border-2 p-2">
      {data?.fields?.map((field: FormField, index: number) => (
        <div key={field.apiName || index} className="mb-4">
          {/* Don't show label for display text fields */}
          {!(field as any).isDisplayText && (
            <label
              htmlFor={field.apiName}
              className="block mb-2 font-semibold text-sm text-gray-700"
            >
              {field.label}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </label>
          )}
          {renderField(field)}
        </div>
      ))}

      {/* Submit button */}
      <div className="mt-6">
        <button
          type="submit"
          disabled={isSubmitting}
          className={`w-full font-semibold py-2 px-4 rounded-md transition-colors ${
            isSubmitting
              ? 'bg-gray-400 cursor-not-allowed'
              : 'bg-color-yellow hover:bg-yellow-600'
          }`}
        >
          {isSubmitting ? 'Submitting...' : 'Submit'}
        </button>
      </div>
    </form>
  )
}

export default DynamicForm
