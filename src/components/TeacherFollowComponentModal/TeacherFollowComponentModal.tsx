'use client'
import React from 'react'
import TeacherFollowCard from '../TeacherFollowCard/TeacherFollowCard'

interface TeacherFollowComponentModalProps {
  teacherData: Array<{
    teacher_id: string
    teacher_name: string
    teacher_photo?: string
    teacher_email?: string
    teacher_website?: string
  }>
}

const TeacherFollowComponentModal = ({
  teacherData,
}: TeacherFollowComponentModalProps) => {
  return (
    <div className="bg-white rounded-2xl max-w-md w-full">
      {/* Header with icon */}
      <div className="text-center mb-6">
        <h2 className="text-xl font-bold text-color-black mb-2">
          Teachers for this class
        </h2>
      </div>

      {/* Teachers list */}
      <div className="space-y-4 mb-6">
        {teacherData.map((teacher) => (
          <TeacherFollowCard key={teacher.teacher_id} teacher={teacher} />
        ))}
      </div>
      {teacherData.length === 0 && (
        <div className="text-center py-8">
          <p className="text-color-grey-1">No teachers found</p>
        </div>
      )}
    </div>
  )
}

export default TeacherFollowComponentModal
