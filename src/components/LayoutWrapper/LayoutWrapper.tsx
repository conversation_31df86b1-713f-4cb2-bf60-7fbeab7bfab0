'use client'
import { usePathname } from 'next/navigation'
import { ROLES } from '@/types/enum'

import {
  NON_LOGGED_IN_PROTECTED_ROUTES,
  URL_PATHS_NO_HEADER_FOOTER_SIDEBAR,
} from '@/utils/constants'

import { useRef } from 'react'

interface LayoutWrapperProps {
  children: React.ReactNode
  role?: string
}

export default function LayoutWrapper({ children, role }: LayoutWrapperProps) {
  const pathname = usePathname()
  const currentRoute = '/' + pathname.split('/')[1]
  const roleRef = useRef<any>(role)

  const isTwoColumnPath = NON_LOGGED_IN_PROTECTED_ROUTES.includes(currentRoute)

  if (isTwoColumnPath) roleRef.current = null
  else roleRef.current = role

  let layoutClass = ''
  if (URL_PATHS_NO_HEADER_FOOTER_SIDEBAR.includes(currentRoute)) {
    layoutClass = 'one-column' // Force one-column for these special paths
  } else {
    // Determine the layout class based on both role and URL for other paths
    layoutClass = [
      ROLES.Adult_Student,
      ROLES.Youth_Student,
      ROLES.Teacher,
      ROLES.Parent,
    ].includes(roleRef.current as ROLES)
      ? 'two-column'
      : 'one-column'
  }

  return (
    <div className={`layout-container bg-color-grey ${layoutClass}`}>
      {children}
    </div>
  )
}
