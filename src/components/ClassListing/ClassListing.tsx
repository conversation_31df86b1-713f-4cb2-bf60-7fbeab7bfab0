'use client'
import React, { useState, useCallback, useEffect, useRef, useMemo } from 'react'
import Search from '../Icons/Search'
import Genre from '../Icons/Genre'
import Level from '../Icons/Level'
import Hybrid from '../Icons/Hybrid'
import GridCalendarButton from '../GridCalendarButton/GridCalendarButton'
import CustomDropDown from '../CustomComponents/CustomDropDown/CustomDropDown'
import { capitalizeFirstLetter, debounce } from '@/utils/utils'
import { useSearchParams } from 'next/navigation'
import Sort from '../Icons/Sort'
import { CLASS_TYPE } from '@/utils/constants'
import ListClass from '../ListClass/ListClass'
import CalenderView from '../CalendarView/CalendarView'
import NoBorderCrossIcon from '../Icons/NoBorderCrossIcon'
import { useIsMobile } from '@/hooks/useIsMobile'
import Link from 'next/link'

const tabs = [
  { key: 'Upcoming', value: 'Upcoming' },
  { key: 'Completed', value: 'Completed' },
]

const ClassesPage = ({
  genres,
  types,
  levels,
  userData,
  selfClasses = false,
  categoryDropdownValues = [],
}: {
  genres: any
  types: any
  levels: any
  userData: any
  selfClasses?: boolean
  categoryDropdownValues?: any
}) => {
  const searchParams = useSearchParams()
  const paramGeners: string | null = searchParams.get('genre')
  const paramLevels: string | null = searchParams.get('level')
  const paramTypes: string | null = searchParams.get('type')
  const teacherId: string | null = searchParams.get('teacher')
  const classCategory: string | null = searchParams.get('category')
  const viewType: string | null = searchParams.get('view')
  const paramsClassType: string | null = searchParams.get('classType')
  const eventClubCategoryParamsType: string | null =
    searchParams.get('category')
  const isMobile = useIsMobile()
  const [activeTab, setActiveTab] = useState<string>('Upcoming')
  const [showCalenderView, setShowCalenderView] = useState<boolean>(false)
  const [selectedGenre, setSelectedGenre] = useState<string[]>(
    paramGeners ? paramGeners?.split(',') : [],
  )
  const [selectedLevel, setSelectedLevel] = useState<string[]>(
    paramLevels ? paramLevels?.split(',') : [],
  )
  const [selectedType, setSelectedType] = useState<string>(paramTypes || '')
  const [selectedTeacher, setSelectedTeacher] = useState<string>(
    teacherId || '',
  )
  const [search, setSearch] = useState<string>('')
  const [category, setCategory] = useState<
    'All' | 'Adult Class' | 'Youth Class'
  >(
    classCategory && ['adult', 'youth'].includes(classCategory)
      ? ((capitalizeFirstLetter(classCategory) + ' Class') as
          | 'Adult Class'
          | 'Youth Class')
      : 'All',
  )
  const [classType, setSelectedClassType] = useState<string>(
    paramsClassType || '',
  )
  const [selectedEventClubCategory, setSelectedEventClubCategory] = useState<
    string[]
  >(eventClubCategoryParamsType ? eventClubCategoryParamsType?.split(',') : [])
  useEffect(() => {
    setCategory((prev) => {
      if (classCategory && ['adult', 'youth'].includes(classCategory)) {
        return (capitalizeFirstLetter(classCategory) + ' Class') as
          | 'Adult Class'
          | 'Youth Class'
      }
      return prev
    })
  }, [classCategory])

  useEffect(() => {
    setShowCalenderView(viewType === 'calender' ? true : false)
  }, [viewType])

  useEffect(() => {
    setSelectedClassType(paramsClassType || '')
    if (paramsClassType) {
      setActiveTab(paramsClassType)
    }
  }, [paramsClassType])

  useEffect(() => {
    setSelectedTeacher(teacherId || '')
  }, [teacherId])

  const handleFilter = useCallback(
    (value: string, filterType: string) => {
      const url = new URL(window.location.href)
      url.searchParams.delete('classType')
      url.searchParams.delete('teacher')
      setSelectedTeacher('')
      setSelectedClassType('')

      switch (filterType) {
        case 'Genre':
          const newGenres = selectedGenre.includes(value)
            ? selectedGenre.filter((item) => item !== value)
            : [...selectedGenre, value]
          setSelectedGenre(newGenres)
          if (newGenres.length)
            url.searchParams.set('genre', newGenres.join(','))
          else url.searchParams.delete('genre')
          break
        case 'Level':
          const newLevels = selectedLevel.includes(value)
            ? selectedLevel.filter((item) => item !== value)
            : [...selectedLevel, value]
          setSelectedLevel(newLevels)
          if (newLevels.length)
            url.searchParams.set('level', newLevels.join(','))
          else url.searchParams.delete('level')
          break
        case 'Type':
          const newType = selectedType === value ? '' : value
          setSelectedType(newType)
          if (newType) url.searchParams.set('type', newType)
          else url.searchParams.delete('type')
          break
        case 'Instructor':
          const newTeacher = selectedTeacher === value ? '' : value
          setSelectedTeacher(newTeacher)
          if (newTeacher) url.searchParams.set('teacher', newTeacher)
          else url.searchParams.delete('teacher')
          break
        case 'Class type':
          setCategory(value as 'All' | 'Adult Class' | 'Youth Class')
          if (value !== 'All')
            url.searchParams.set('category', value.toLowerCase().split(' ')[0])
          else url.searchParams.delete('category')
          break
        case 'Category':
          const newEventClubCategories = selectedEventClubCategory.includes(
            value,
          )
            ? selectedEventClubCategory.filter((item) => item !== value)
            : [...selectedEventClubCategory, value]
          setSelectedEventClubCategory(newEventClubCategories)
          if (newEventClubCategories.length)
            url.searchParams.set('category', newEventClubCategories.join(','))
          else url.searchParams.delete('category')
          break
        default:
          break
      }

      window.history.pushState({}, '', url.toString())
    },
    [
      selectedGenre,
      selectedLevel,
      selectedType,
      selectedTeacher,
      selectedEventClubCategory,
    ],
  )

  // Function to remove a specific filter
  const removeFilter = (value: string, filterType: string) => {
    handleFilter(value, filterType)
  }

  // Function to clear all filters
  const clearAllFilters = () => {
    const url = new URL(window.location.href)
    setSelectedEventClubCategory([])
    setSelectedGenre([])
    setSelectedLevel([])
    setSelectedType('')
    setSelectedTeacher('')

    // Clear URL params
    url.searchParams.delete('genre')
    url.searchParams.delete('level')
    url.searchParams.delete('type')
    url.searchParams.delete('teacher')
    url.searchParams.delete('category')

    window.history.pushState({}, '', url.toString())
  }

  const handleSearch = (e: any) => {
    setSearch(e.target.value)
  }

  const debounceSearch = debounce(handleSearch, 1000)

  const hasActiveFilters = useMemo(
    () =>
      selectedEventClubCategory.length > 0 ||
      selectedGenre.length > 0 ||
      selectedLevel.length > 0 ||
      selectedType !== '' ||
      selectedTeacher !== '',
    [
      category,
      selectedGenre,
      selectedLevel,
      selectedType,
      selectedTeacher,
      selectedEventClubCategory,
    ],
  )

  const handleTabClick = (value: string) => {
    setActiveTab(value)
    setSelectedClassType(value)

    const url = new URL(window.location.href)
    url.searchParams.set('classType', value)
    window.history.pushState({}, '', url.toString())
  }

  return (
    <div className="w-full">
      <div className="flex flex-col items-center justify-center w-full">
        <div className={`${userData ? 'w-full' : 'w-full md:w-3/4'}`}>
          {!userData && (
            <div className="font-bold text-base md:text-3xl px-3 md:px-7 py-7">
              Search {capitalizeFirstLetter(classCategory)} Classes
            </div>
          )}
          <div
            className={`${!userData ? '' : 'bg-white'} px-3 md:px-7 pb-7 flex flex-wrap items-center justify-between flex-col gap-3 md:flex-row`}
          >
            {showCalenderView ? (
              <div></div>
            ) : (
              <div className="w-full md:w-max relative">
                <input
                  type="text"
                  placeholder="Search by Class Name/Genre/Teacher"
                  className="h-12 p-5 rounded-xl border text-xs text-color-grey-1 w-full md:w-80"
                  onChange={debounceSearch}
                />
                <div
                  className="absolute right-5"
                  style={{ top: 'calc(50% - 7px)' }}
                >
                  <Search width={14.25} height={14.25} color={'#9C9CA4'} />
                </div>
              </div>
            )}
            <div className="flex w-full md:w-auto items-center justify-between gap-4 flex-col md:flex-row">
              {/* <CustomDropDown
                icon={<Sort />}
                title={'Class type'}
                options={CLASS_TYPE}
                column={1}
                handleOptionClick={handleFilter}
                selectedList={category}
                isTitleType={true}
              /> */}
              <CustomDropDown
                icon={<Sort />}
                title={'Category'}
                options={categoryDropdownValues}
                column={isMobile ? 1 : 2}
                handleOptionClick={handleFilter}
                selectedList={selectedEventClubCategory}
                width="w-max"
                isTitleType={true}
              />
              <CustomDropDown
                icon={<Genre />}
                title={'Genre'}
                options={genres}
                column={isMobile ? 1 : 2}
                handleOptionClick={handleFilter}
                selectedList={selectedGenre}
                isTitleType={true}
              />
              <CustomDropDown
                icon={<Level />}
                title={'Level'}
                options={levels}
                column={isMobile ? 1 : 2}
                handleOptionClick={handleFilter}
                selectedList={selectedLevel}
                isTitleType={true}
              />
              <CustomDropDown
                icon={<Hybrid />}
                title={'Type'}
                options={types}
                handleOptionClick={handleFilter}
                selectedList={selectedType}
                isTitleType={true}
              />
              <GridCalendarButton
                calendarView={showCalenderView}
                setShowCalenderView={setShowCalenderView}
              />
            </div>
          </div>

          {/* Filter Pills Section */}
          {hasActiveFilters && (
            <div className="flex flex-wrap items-center gap-2 px-3 md:px-7 py-4 border-t border-b">
              {/* {category !== 'All' && (
                <div className="flex items-center px-4 py-2 text-sm font-medium bg-gray-100 rounded-full border border-color-grey-1">
                  <span className="text-sm">{category}</span>
                  <button
                    className="ml-2 text-gray-500 hover:text-gray-700"
                    onClick={() => handleFilter('All', 'Class type')}
                  >
                    <NoBorderCrossIcon />
                  </button>
                </div>
              )} */}

              {selectedEventClubCategory?.map((category) => (
                <div
                  key={`category-${category}`}
                  className="flex items-center px-4 py-2 text-sm font-medium bg-gray-100 rounded-full border border-color-grey-1"
                >
                  <span className="text-sm">Category: {category}</span>
                  <button
                    className="ml-2 text-gray-500 hover:text-gray-700"
                    onClick={() => removeFilter(category, 'Category')}
                  >
                    <NoBorderCrossIcon />
                  </button>
                </div>
              ))}

              {selectedGenre.map((genre) => (
                <div
                  key={`genre-${genre}`}
                  className="flex items-center px-4 py-2 text-sm font-medium bg-gray-100 rounded-full border border-color-grey-1"
                >
                  <span className="text-sm">Genre: {genre}</span>
                  <button
                    className="ml-2 text-gray-500 hover:text-gray-700"
                    onClick={() => removeFilter(genre, 'Genre')}
                  >
                    <NoBorderCrossIcon />
                  </button>
                </div>
              ))}

              {selectedLevel.map((level) => (
                <div
                  key={`level-${level}`}
                  className="flex items-center px-4 py-2 text-sm font-medium bg-gray-100 rounded-full border border-color-grey-1"
                >
                  <span className="text-sm">Level: {level}</span>
                  <button
                    className="ml-2 text-gray-500 hover:text-gray-700"
                    onClick={() => removeFilter(level, 'Level')}
                  >
                    <NoBorderCrossIcon />
                  </button>
                </div>
              ))}

              {selectedType && (
                <div className="flex items-center px-4 py-2 text-sm font-medium bg-gray-100 rounded-full border border-color-grey-1">
                  <span className="text-sm">{selectedType}</span>
                  <button
                    className="ml-2 text-gray-500 hover:text-gray-700"
                    onClick={() => removeFilter(selectedType, 'Type')}
                  >
                    <NoBorderCrossIcon />
                  </button>
                </div>
              )}

              <button
                className="ml-auto text-blue-500 hover:text-blue-700 text-sm font-medium"
                onClick={clearAllFilters}
              >
                Clear All
              </button>
            </div>
          )}
          {/* Classes at a Glance Link */}
          <div className="px-3 md:px-7 pt-4 pb-2">
            <Link
              href="/classes-at-a-glance"
              className="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium text-sm transition-colors duration-200"
            >
              View all classes at a glance
              <svg
                className="ml-1 w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </Link>
          </div>
          {selfClasses && (
            <div className="flex items-center w-max justify-start border-b border-color-grey-2 mx-7 mt-7">
              {tabs.map((tab: any) => (
                <div
                  key={tab?.key}
                  className={`flex items-center gap-1 py-3 px-10 ${activeTab == tab?.value ? 'border-b-4 border-color-yellow' : 'text-color-grey-1'} cursor-pointer`}
                  // onClick={() => setActiveTab(tab?.value)}
                  onClick={() => handleTabClick(tab?.value)}
                >
                  <span className="font-semibold text-base">{tab?.key}</span>
                </div>
              ))}
            </div>
          )}
          {showCalenderView ? (
            <CalenderView
              paramGeners={paramGeners}
              paramLevels={paramLevels}
              paramTypes={paramTypes}
              classCategory={classCategory}
              category={category}
              selectedType={selectedType}
              selectedGenre={selectedGenre}
              selectedLevel={selectedLevel}
              selfClasses={selfClasses}
              activeTab={activeTab}
            />
          ) : (
            <ListClass
              search={search}
              paramGeners={paramGeners}
              paramLevels={paramLevels}
              paramTypes={paramTypes}
              classCategory={classCategory}
              teacherId={teacherId}
              category={category}
              selectedType={selectedType}
              selectedGenre={selectedGenre}
              selectedLevel={selectedLevel}
              selectedTeacher={selectedTeacher}
              selfClasses={selfClasses}
              activeTab={activeTab}
              classType={classType}
              selectedEventClubCategory={selectedEventClubCategory}
            />
          )}
        </div>
      </div>
    </div>
  )
}

export default ClassesPage
