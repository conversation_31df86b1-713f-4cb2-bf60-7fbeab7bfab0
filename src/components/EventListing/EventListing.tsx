'use client'
import React, { useEffect, useState } from 'react'
import EventDate from '../EventDate/EventDate'
import Link from 'next/link'
import PurplePot from '../Icons/PurplePot'
import Image from 'next/image'
import NoResult from '../NoResult/NoResult'
import BigCalendar from '../Icons/BigCalendar'
import { formatDateTime } from '@/utils/utils'

const EventListing = ({ eventData }: { eventData: any }) => {
  const [data, setData] = useState<any>([])
  const [currentDate, setCurrentDate] = useState(new Date())

  useEffect(() => {
    const eventsByMonth: { [key: string]: any[] } = {}

    eventData.forEach((event: any) => {
      const createdDate = new Date(event?.Start_Date__c)
      const monthYear = createdDate.toLocaleString('default', {
        month: 'long',
        year: 'numeric',
      })
      if (!eventsByMonth[monthYear]) {
        eventsByMonth[monthYear] = []
      }
      eventsByMonth[monthYear].push(event)
    })
    setData(eventsByMonth)
  }, [])

  return (
    <>
      {data.length == 0 ? (
        <div className="w-full flex items-center justify-center">
          <NoResult
            icon={<BigCalendar />}
            title={'A quiet day on 10/12/2024'}
            desc={
              'It looks like we don’t have anything planned for this day. Try selecting a different date to see what’s on.'
            }
            btnText={'Browse all events'}
            btnLink={'/events'}
          />
        </div>
      ) : (
        <div>
          <EventDate
            currentDate={currentDate}
            setCurrentDate={setCurrentDate}
          />
          {Object.keys(data).map((monthYear, monthIndex) => (
            <div key={monthYear} className="space-y-2 mb-5">
              <div
                className={`flex items-center justify-between ${monthIndex != 0 ? 'mt-4' : ''}`}
              >
                <h2 className="text-color-grey-1 text-sm">{monthYear}</h2>
              </div>

              <div className="w-full bg-white rounded-2xl px-3 md:px-10 mt-4">
                {data[monthYear].map((event: any, eventIndex: number) => {
                  const formatedDate = formatDateTime(event?.Start_Date__c)
                  return (
                    <Link
                      href={`/events/${event?.Id}`}
                      key={monthYear + event?.Id}
                    >
                      <div
                        className={`grid md:grid-cols-[12%,30%,58%] ${
                          eventIndex !== data[monthYear].length - 1
                            ? 'border-b-1 border-color-grey-2'
                            : ''
                        } py-5`}
                        key={eventIndex}
                      >
                        <div className="flex justify-between">
                          <div className="flex items-center gap-4">
                            <div>
                              <span className="text-color-grey-1 font-medium text-sm">
                                {new Date(event?.Start_Date__c)
                                  .toLocaleString('default', {
                                    weekday: 'short',
                                  })
                                  .toUpperCase()}
                              </span>
                              <h2 className="font-bold text-lg">
                                {new Date(event?.Start_Date__c).toLocaleString(
                                  'default',
                                  {
                                    month: 'short',
                                    day: 'numeric',
                                  },
                                )}
                              </h2>
                              <h2 className="font-medium text-sm block md:hidden">
                                {formatedDate.date} - {formatedDate.time}
                              </h2>
                            </div>
                            {event?.Featured__c && (
                              <div className="flex items-center gap-2 md:hidden">
                                <PurplePot />
                                <span className="text-sm font-semibold text-color-purple">
                                  Featured
                                </span>
                              </div>
                            )}
                          </div>
                          {event?.Banner_Image__c && (
                            <Image
                              width={330}
                              height={120}
                              src={event?.Banner_Image__c}
                              alt="Event banner"
                              className="rounded-xl h-20 md:h-52 w-20 md:w-52 object-cover block md:hidden"
                            />
                          )}
                        </div>
                        <div>
                          {event?.Banner_Image__c && (
                            <Image
                              width={330}
                              height={120}
                              src={event?.Banner_Image__c}
                              alt="Event banner"
                              className="rounded-xl h-20 md:h-52 w-20 md:w-52 object-cover hidden md:flex"
                            />
                          )}
                        </div>
                        <div className="">
                          <div className="flex items-center gap-3">
                            {event?.Featured__c && (
                              <div className="items-center gap-2 hidden md:flex">
                                <PurplePot />
                                <span className="text-sm font-semibold text-color-purple">
                                  Featured
                                </span>
                              </div>
                            )}
                            <h2 className="font-medium text-sm hidden md:block">
                              {formatedDate.date} - {formatedDate.time}
                            </h2>
                          </div>
                          <div className="flex justify-between gap-2">
                            <h1 className="font-extrabold text-lg md:text-2xl">
                              {event?.Title__c}
                            </h1>
                          </div>
                          <h2 className="font-semibold text-sm">
                            {event?.Meeting_Location__c}
                          </h2>
                          <div
                            className="mt-4 line-clamp-3"
                            dangerouslySetInnerHTML={{
                              __html: event?.Description__c,
                            }}
                          />
                          <span className="text-color-blue-1 font-semibold text-sm mt-4 block">
                            View Event Details
                          </span>
                        </div>
                      </div>
                    </Link>
                  )
                })}
              </div>
            </div>
          ))}
        </div>
      )}
    </>
  )
}

export default EventListing
