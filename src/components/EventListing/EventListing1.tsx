import BigCalendar from '../Icons/BigCalendar'
import NoResult from '../NoResult/NoResult'
import Image from 'next/image'
import Link from 'next/link'
import { formatSalesforceTime } from '@/utils/utils'
import BlackCalendar from '../Icons/BlackCalendar'
import LocationIcon from '../Icons/LocationIcon'
import { TickIcon } from '../Icons/TickIcon'
import Person from '../Icons/Person'
import FilledVideoPlayerIcon from '../Icons/FilledVideoPlayerIcon'
import VideoPlayerIcon from '../Icons/VideoPlayerIcon'
import PriceIcon from '../Icons/PriceIcon'

const EventListing1 = ({
  eventData,
  type,
}: {
  eventData: any
  type: string
}) => {
  const getClassTypeIcon = (type: string) => {
    switch (type) {
      case 'In-Person':
        return <Person stroke="#000000" />
      case 'Hybrid':
        return <FilledVideoPlayerIcon stroke="#000000" />
      case 'Online':
        return <VideoPlayerIcon stroke="#000000" />
    }
  }

  const linkType = (catergory: string) => {
    switch (catergory) {
      case 'Event':
        return 'events'
      case 'Class':
        return 'classes'
      case 'Club':
        return 'clubs'
      case 'Outreach':
        return 'outreach'
      default:
        return 'events'
    }
  }

  return (
    <>
      {eventData?.length == 0 ? (
        <div className="w-full flex items-center justify-center">
          <NoResult
            icon={<BigCalendar />}
            title={'A quiet day on 10/12/2024'}
            desc={
              'It looks like we don’t have anything planned for this day. Try selecting a different date to see what’s on.'
            }
            btnText={'Browse all events'}
            btnLink={'/events'}
          />
        </div>
      ) : (
        <div className={`flex flex-col gap-4`}>
          {eventData?.map((event: any) => {
            const date = new Date(event.Starts_On__c)
            const formatted = date
              .toLocaleDateString('en-US', {
                weekday: 'short',
                day: 'numeric',
                month: 'long',
              })
              .replace(/,/g, '')

            return (
              <Link
                key={event?.Class_and_Event__c}
                href={`/${linkType(event?.Class_and_Event__r?.Category__c)}/${event?.Class_and_Event__c}`}
                className="flex flex-col lg:flex-row gap-10 p-4 bg-white rounded-2xl shadow-sm hover:shadow-md transition-shadow duration-200"
              >
                {/* Image */}
                <div className="relative w-full lg:w-[346px] h-[221px] flex-shrink-0">
                  {event?.Class_and_Event__r?.Banner_Image__c ? (
                    <Image
                      fill
                      src={event?.Class_and_Event__r?.Banner_Image__c}
                      alt="Event banner"
                      className="object-cover"
                    />
                  ) : (
                    <Image
                      fill
                      alt="Event banner"
                      src={'/banner-image-new.png'}
                      className="object-cover"
                    />
                  )}
                </div>

                {/* Content */}
                <div className="flex flex-1 flex-col gap-4">
                  <div className="flex flex-col gap-2 items-start">
                    {event?.Class_and_Event__r?.Garnish__c && (
                      <div className="inline-block bg-green-100 bg-color-green-7 text-color-green-6 text-base font-medium px-2 py-1 rounded-lg">
                        {event?.Class_and_Event__r?.Garnish__c}
                      </div>
                    )}
                    <div className="font-medium text-sm text-color-grey-19 flex gap-3">
                      <span>
                        <BlackCalendar />
                      </span>
                      {formatted} -{' '}
                      {formatSalesforceTime(
                        event?.Starts_On__c,
                        event?.Ends_On__c,
                      )}
                    </div>
                  </div>

                  <div className="font-bold text-2xl leading-[100%] tracking-normal text-color-black-2">
                    {event?.Class_and_Event__r?.Title__c}
                  </div>

                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <div className="flex items-center gap-2 font-medium text-sm leading-[100%] tracking-normal text-color-grey-19">
                      <span>
                        <LocationIcon />
                      </span>{' '}
                      {event?.Venue__r?.Name}
                    </div>
                  </div>

                  <div className="flex gap-5 font-medium text-sm leading-[100%] tracking-normal text-color-grey-19">
                    <div className="flex gap-1 items-center">
                      <span>
                        <TickIcon />
                      </span>{' '}
                      {event?.Class_and_Event__r?.Registration_Required__c ? (
                        <span className="w-max">
                          {event?.Class_and_Event__r?.Total_Seats__c -
                            event?.Class_and_Event__r?.Booked_Seats__c}{' '}
                          out of{' '}
                          {event?.Class_and_Event__r?.Total_Seats__c ?? ''}{' '}
                          seats
                        </span>
                      ) : (
                        <span>
                          {event?.Class_and_Event__r?.Booked_Seats__c} going
                        </span>
                      )}
                    </div>

                    <div className="flex gap-1 items-center">
                      <span>
                        {getClassTypeIcon(event?.Class_and_Event__r?.Type__c)}
                      </span>{' '}
                      {event?.Class_and_Event__r?.Type__c}
                    </div>
                    <div className="flex gap-1 items-center">
                      <span>
                        <PriceIcon />
                      </span>
                      <span
                        className={
                          event?.Class_and_Event__r?.Price__c > 0
                            ? 'text-color-grey-19'
                            : 'text-green-600'
                        }
                      >
                        {event?.Class_and_Event__r?.Price__c > 0
                          ? `$${event?.Class_and_Event__r?.Price__c}`
                          : 'Free'}
                      </span>
                    </div>
                  </div>

                  <div className="flex gap-2 items-center font-medium text-xs leading-[100%] tracking-normal">
                    <div className="relative w-7 h-7 flex-shrink-0">
                      {event?.teacher_data?.Photo__c ? (
                        <Image
                          fill
                          src={event?.teacher_data?.Photo__c}
                          alt="Teacher Image"
                          className="object-cover rounded-full"
                        />
                      ) : (
                        <Image
                          fill
                          alt="Teacher banner"
                          src={'/banner-image-new.png'}
                          className="object-cover rounded-full"
                        />
                      )}
                    </div>
                    <span className="text-color-grey-19">
                      {event?.teacher_data?.Name}
                    </span>
                  </div>
                </div>
              </Link>
            )
          })}
        </div>
      )}
    </>
  )
}

export default EventListing1
