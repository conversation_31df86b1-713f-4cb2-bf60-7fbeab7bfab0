import React, { useEffect, useMemo } from 'react'
import CustomButton from '../CustomComponents/CustomButton/CustomButton'
import { useApi } from '@/hooks/useApi'
import {
  checkoutCart,
  getCartCoupons,
  youthCheckoutCart,
} from '@/lib/actions/cart.actions'
import { useRouter } from 'next/navigation'
import { useDispatch } from 'react-redux'
import { setCartCount, setPaymentToken } from '@/lib/redux'
import { COUPON_TYPES, ROLES } from '@/types/enum'
import { toast } from 'react-toastify'
import { validateCoupon } from '@/lib/actions/coupon.actions'
import CouponSkeleton from '../Skeletons/CouponSkeleton'
import CustomTooltip from '../CustomComponents/CustomTooltip/CustomTooltip'
import useModal from '@/hooks/useModal'
import FlexiblePayment from '../FlexiblePayment/FlexiblePayment'
import IEIcon from '../Icons/IEIcon'
import { useAuth } from '@/contexts/AuthContext'
import AddParentDetails from '../AddParentDetails/AddParentDetails'
import { buyClassForYouth } from '@/lib/actions/parent.actions'

interface CartPurchaseProps {
  totalAmount: number
  cartData: any[]
  parentChildren?: any[]
  setCartData: React.Dispatch<React.SetStateAction<any[]>>
  setTotalAmount?: React.Dispatch<React.SetStateAction<number>>
}

const CartPurchase = ({
  totalAmount,
  cartData,
  parentChildren,
  setCartData,
  setTotalAmount,
}: CartPurchaseProps) => {
  const [userCoupons, setUserCoupons] = React.useState<any[]>([])
  const [couponId, setCouponId] = React.useState<string>('')
  const [couponCode, setCouponCode] = React.useState<string>('')
  const [couponType, setCouponType] = React.useState<string>(COUPON_TYPES.USER)
  const [discountAmount, setDiscountAmount] = React.useState<number>(0)
  const [fetchCouponResponse, fetchCoupon] = useApi((access: string) =>
    getCartCoupons(access),
  )
  const [parentApprovalResponse, sendParentApproval] = useApi(
    (
      access: string,
      isFlexible: boolean,
      couponId: string,
      couponType: COUPON_TYPES,
    ) => youthCheckoutCart(access, isFlexible, couponId, couponType),
  )

  const { user } = useAuth()
  const [parentAccountId, setParentAccountId] = React.useState<string>('')
  const [childId, setChildId] = React.useState<string>('')
  const [modal, showModal] = useModal()
  const [parentModal, showParentModal] = useModal()
  const router = useRouter()
  const dispatch = useDispatch()
  const tooltipMessage = 'Pay for your purchase in 3 easy installments'

  useEffect(() => {
    if (user) {
      setParentAccountId(user?.parent || '')
    }
  }, [user])

  const handleModal = (totalAmount: number) => {
    showModal({
      title: ``,
      contentFn: (onClose) => (
        <FlexiblePayment
          totalAmount={totalAmount}
          onClose={onClose}
          couponId={couponId}
          couponType={couponType}
          setCartData={setCartData}
          setTotalAmount={setTotalAmount}
        />
      ),
      closeOnClickOutside: true,
      size: 'lg',
      rounded: '2xl',
      padding: 0,
      showCloseButton: false,
    })
  }

  useEffect(() => {
    fetchCoupon()
    setCouponId('')
    setCouponCode('')
    setCouponType(COUPON_TYPES.USER)
    setDiscountAmount(0)
  }, [cartData])

  useEffect(() => {
    if (fetchCouponResponse.isSuccess) {
      setUserCoupons(fetchCouponResponse.data?.data || [])
    }
  }, [fetchCouponResponse])

  useEffect(() => {
    if (parentApprovalResponse.isSuccess) {
      toast.success('Parent approval request sent successfully', {
        autoClose: 1500,
      })
      setCartData([])
      dispatch(setCartCount(0))
      // Clear discount and coupon state
      setDiscountAmount(0)
      setCouponId('')
      setCouponCode('')
      setCouponType(COUPON_TYPES.USER)
      // Clear total amount
      if (setTotalAmount) {
        setTotalAmount(0)
      }
    }
  }, [parentApprovalResponse])

  const [couponResponse, handleCheckoutCart] = useApi(
    (access: string, coupon: string, type) =>
      checkoutCart(access, false, coupon, type),
  )
  const [validateResponse, handleValidateCoupon] = useApi((access, coupon) =>
    validateCoupon(access, coupon),
  )

  useEffect(() => {
    if (couponResponse.isSuccess) {
      dispatch(setPaymentToken(couponResponse.data?.data?.token))
      // Clear cart data, discount and coupon state
      setCartData([])
      dispatch(setCartCount(0))
      setDiscountAmount(0)
      setCouponId('')
      setCouponCode('')
      setCouponType(COUPON_TYPES.USER)
      // Clear total amount
      if (setTotalAmount) {
        setTotalAmount(0)
      }
      router.push(`/checkout?token=${couponResponse.data?.data?.token}`)
    }
  }, [couponResponse])

  useEffect(() => {
    if (validateResponse.isSuccess) {
      toast.success('Coupon validated successfully')
      const couponObj = {
        ...validateResponse?.data?.data,
        couponType: COUPON_TYPES.GENERAL,
        id: couponCode,
      }
      setUserCoupons((prev: any) => [couponObj, ...prev])
      setCouponId(couponCode)
      setDiscountAmount(validateResponse?.data?.data?.totalDiscount)
      setCouponCode('')
    }
  }, [validateResponse])

  const handleCouponAdd = async () => {
    if (couponCode.trim() === '') {
      toast.error('Please enter a coupon code')
      return
    }
    const duplicateCoupon = userCoupons.find(
      (coupon) => coupon.id === couponCode,
    )
    if (duplicateCoupon) {
      toast.error('Coupon already exists')
      return
    }
    try {
      setCouponId('')
      await handleValidateCoupon(couponCode)
      setCouponType(COUPON_TYPES.GENERAL)
    } catch (error) {
      toast.error('Error validating coupon')
    }
  }

  const handleCouponSelect = (coupon: any) => {
    setCouponId(coupon?.id)
    setCouponType(coupon?.couponType ? COUPON_TYPES.GENERAL : COUPON_TYPES.USER)
    setCouponCode('')
    setDiscountAmount(coupon?.totalDiscount)
  }

  const checkoutBtnText = useMemo(() => {
    if (user && user?.role === ROLES.Youth_Student) {
      return 'Request Parent Approval'
    }
    if (childId && childId !== '') {
      return 'Purchase for Child'
    }
    return 'Proceed to Checkout'
  }, [user])

  const handleRequestApproval = async (
    couponId: string,
    couponType: string,
  ) => {
    if (user && user?.parent && parentAccountId !== '') {
      sendParentApproval(false, couponId, couponType)
    } else {
      showParentModal({
        title: '',
        contentFn: (onClose) => (
          <AddParentDetails
            onClose={onClose}
            setParentAccountId={setParentAccountId}
          />
        ),
        size: 'md',
        rounded: '2xl',
        showCloseButton: false,
      })
    }
  }

  const totalCartAmount = useMemo(() => {
    return (totalAmount - discountAmount).toFixed(2)
  }, [totalAmount, discountAmount])

  return (
    <div className="bg-white h-max w-full p-5 rounded-2xl flex items-center flex-col justify-start md:sticky md:top-5 sticky bottom-0 left-0">
      <div className="flex items-center justify-between w-full text-lg font-semibold">
        <h2>Cart total</h2>
        <h1>${totalAmount}</h1>
      </div>
      <div className="items-center justify-between w-full mt-2 border-b border-color-grey-2 pb-5 hidden md:flex">
        <h2 className="text-sm">Discount</h2>
        <h1 className="text-sm text-color-grey-1 font-medium">
          -${discountAmount.toFixed(2)}
        </h1>
      </div>
      <div className="items-center justify-between w-full text-lg font-bold mt-5 hidden md:flex">
        <h2>Total amount</h2>
        <h1>${totalCartAmount}</h1>
      </div>
      <div className="mt-3 w-full">
        <CustomButton
          title={checkoutBtnText}
          isLoading={
            couponResponse.isFetching || parentApprovalResponse.isFetching
          }
          onClick={() => {
            if (user && user?.role === ROLES.Youth_Student) {
              handleRequestApproval(couponId, couponType)
            } else if (childId && childId !== '') {
              // youthPurchased(childId, clas)
            } else {
              handleCheckoutCart(couponId, couponType)
            }
          }}
          height={12}
          isDisabled={cartData.length == 0 || couponResponse.isFetching}
        />
      </div>
      {totalAmount - discountAmount > 10 &&
        childId === '' &&
        Number(totalCartAmount) > 0 && (
          <div className="mt-3 w-full border border-color-grey-2 rounded-lg">
            <CustomTooltip text={tooltipMessage} isVisible={true}>
              <CustomButton
                title={'Flexible Payment'}
                isLoading={false}
                onClick={() =>
                  handleModal && handleModal(totalAmount - discountAmount)
                }
                height={12}
                isDisabled={cartData.length == 0 || couponResponse.isFetching}
                backgroundColor="#FFFFFF"
                icon={<IEIcon stroke="#9C9CA4" />}
              />
            </CustomTooltip>
          </div>
        )}

      {userCoupons.length > 0 && (
        <div className="w-full mt-5">
          <h2 className="text-lg font-bold">Discount</h2>
          {cartData.length > 0 && (
            <>
              {fetchCouponResponse.isFetching ? (
                <div className="mt-5 space-y-2">
                  {[...Array(3)].map((_, index) => (
                    <CouponSkeleton key={index} />
                  ))}
                </div>
              ) : (
                <>
                  {userCoupons.map((coupon: any) => (
                    <label key={coupon?.id} className="text-sm font-bold">
                      <div
                        className={`flex items-center justify-between w-full mt-2 ${couponId == coupon.id ? 'border-2 border-color-blue-2' : 'border-2 border-color-grey-2'} p-5 rounded-md`}
                      >
                        <div className="flex items-center gap-2">
                          <input
                            type="radio"
                            name="coupon"
                            className="w-4 h-4 cursor-pointer"
                            onChange={() => handleCouponSelect(coupon)}
                            checked={couponId == coupon?.id}
                          />
                          <h2 className="text-sm font-bold">{coupon?.name}</h2>
                        </div>
                        <h1 className="text-sm text-color-grey-1 font-medium">
                          <span className="font-bold">$ </span>
                          {coupon?.totalDiscount}
                        </h1>
                      </div>
                    </label>
                  ))}
                </>
              )}
            </>
          )}
        </div>
      )}
      <div className="w-full gap-3 mt-5 text-sm hidden md:grid grid-cols-[70%,30%]">
        <input
          type="text"
          className="border border-color-grey-2 px-5 py-3 h-12 rounded-lg w-full"
          placeholder="Enter discount code"
          onChange={(e) => setCouponCode(e.target.value)}
          value={couponCode}
        />
        <CustomButton
          title={'Apply'}
          isLoading={validateResponse.isFetching}
          onClick={handleCouponAdd}
          height={12}
          backgroundColor="bg-color-black"
          textColor="text-white"
          isDisabled={
            cartData.length == 0 ||
            couponCode.trim() === '' ||
            validateResponse.isFetching ||
            couponResponse.isFetching
          }
        />
      </div>
      {modal}
      {parentModal}
    </div>
  )
}

export default CartPurchase
