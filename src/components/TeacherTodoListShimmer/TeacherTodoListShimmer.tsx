const TodoListShimmer = () => {
  return (
    <div className="space-y-4">
      {[...Array(3)].map((_, i) => (
        <div
          key={i}
          className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 sm:gap-4 py-2 border-b last:border-none animate-pulse"
        >
          {/* Left section */}
          <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 w-full">
            {/* Date */}
            <div className="w-24 sm:w-28 h-5 bg-gray-200 rounded-md" />
            {/* Title */}
            <div className="w-20 sm:w-32 h-5 bg-gray-200 rounded-md" />
            {/* Teacher */}
            <div className="flex-1 h-5 bg-gray-200 rounded-md" />
          </div>

          {/* Right button */}
          <div className="w-full sm:w-36 h-9 bg-gray-300 rounded-lg" />
        </div>
      ))}
    </div>
  )
}

export default TodoListShimmer
