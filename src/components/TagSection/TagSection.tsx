'use client'

interface TagSectionProps {
  title: string
  items: string[]
}

export const TagSection: React.FC<TagSectionProps> = ({ title, items }) => {
  if (!items?.length) return null

  return (
    <div className="flex flex-col gap-3">
      <h2 className="text-2xl font-semibold leading-9 tracking-normal text-color-black">
        {title}
      </h2>
      <div className="flex flex-wrap gap-2">
        {items?.map((item) => (
          <span
            key={item}
            className="px-4 py-2 text-sm bg-neutral-white text-color-black-2 font-medium rounded-full shadow-sm"
          >
            {item}
          </span>
        ))}
      </div>
    </div>
  )
}
