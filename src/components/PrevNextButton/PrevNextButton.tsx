import React from 'react'
import LeftArrow from '../Icons/LeftArrow'
import RightArrow from '../Icons/RightArrow'

const PrevNextButton = ({
  onPrev,
  onNext,
  showBorder = false,
  notDisable = false,
  arrorwColor = '',
}: {
  onPrev?: () => void
  onNext?: () => void
  showBorder?: boolean
  notDisable?: boolean
  arrorwColor?: string
}) => {
  return (
    <div className="flex items-center gap-3">
      <button
        onClick={onPrev}
        style={
          showBorder
            ? {
                padding: '5px',
                borderRadius: '6px',
                border: '1px solid #DFDFDF',
                backgroundColor: '#FFFFFF',
              }
            : {}
        }
      >
        <LeftArrow dark={notDisable} arrowColor={arrorwColor} />
      </button>
      <button
        onClick={onNext}
        style={
          showBorder
            ? {
                padding: '5px',
                borderRadius: '6px',
                border: '1px solid #DFDFDF',
                backgroundColor: '#FFFFFF',
              }
            : {}
        }
      >
        <RightArrow dark={notDisable} arrowColor={arrorwColor} />
      </button>
    </div>
  )
}

export default PrevNextButton
