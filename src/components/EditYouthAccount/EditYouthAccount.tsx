'use client'
import React, { FormEvent, useEffect, useState } from 'react'
import 'react-country-state-city/dist/react-country-state-city.css'
import CustomDatePicker from '../CustomComponents/CustomDatePicker/CustomDatePicker'
import { toast } from 'react-toastify'
import CustomButton from '../CustomComponents/CustomButton/CustomButton'
import { useAuth } from '@/contexts/AuthContext'
import PhoneInput from 'react-phone-input-2'
import 'react-phone-input-2/lib/style.css'
import { useUpdateYouthAccountMutation } from '@/lib/redux/slices/apiSlice/apiSlice'
import ProfileUploadYouth from '../ProfileUploadYouth/ProfileUploadYouth'
import { addPhotoToS3Bucket } from '@/utils/utils'
import { revalidateYouthDetail } from '@/lib/actions/revalidate.actions'

interface FormData {
  firstName: string
  lastName: string
  phone: string
  academicLevel: string
  allergy: boolean
  allergyInfo: string | null
  disablityStatus: string | null
  highSchool: string
  photoUrl: string
}

const EditYouthAccount = ({
  userData,
  id = '',
}: {
  userData: any
  id?: string
}) => {
  const { user } = useAuth()
  const [formData, setFormData] = useState<FormData>({
    firstName: '',
    lastName: '',
    phone: '',
    academicLevel: '',
    allergy: false,
    allergyInfo: '',
    disablityStatus: null,
    highSchool: '',
    photoUrl: '',
  })

  const [dateOfBirth, setDateOfBirth] = useState<string>(user?.birthday)
  const [file, setFile] = useState<File | null>(null)
  const [isUploadingImage, setIsUploadingImage] = useState<boolean>(false)

  const [updateYouthAccount, { isLoading, isSuccess, isError }] =
    useUpdateYouthAccountMutation()

  useEffect(() => {
    if (isSuccess) {
      toast.success('Profile updated successfully')
      revalidateYouthDetail(id)
    }
  }, [isSuccess])

  useEffect(() => {
    if (userData) {
      setFormData((prev) => ({
        ...prev,
        firstName: userData.FirstName || '',
        lastName: userData.LastName || '',
        phone: userData.Phone || '',
        academicLevel: userData.Academic_Level__c || '',
        allergy: userData.Allergies__c || false,
        allergyInfo: userData.Allergy_Info__c || null,
        disablityStatus: userData.Disability_Status__c || null,
        highSchool: userData.High_School__c,
        photoUrl: userData.Photo__c,
      }))
      setDateOfBirth(userData.PersonBirthdate)
    }
  }, [userData])

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    const today = new Date()
    const birthDate = new Date(dateOfBirth)
    const age = today.getFullYear() - birthDate.getFullYear()
    const monthDiff = today.getMonth() - birthDate.getMonth()
    const dayDiff = today.getDate() - birthDate.getDate()

    const is18OrMore =
      age > 18 ||
      (age === 18 && (monthDiff > 0 || (monthDiff === 0 && dayDiff >= 0)))

    if (is18OrMore) {
      toast.error('Child must be below 18 years old', { autoClose: 3000 })
      return
    }
    if (userData && userData?.Id) {
      let imageUrl = ''
      if (file) {
        setIsUploadingImage(true)
        imageUrl = (await addPhotoToS3Bucket(file)) || ''
        setIsUploadingImage(false)
      }
      const updateData = {
        firstName: formData.firstName,
        lastName: formData.lastName,
        phone: formData.phone,
        dateOfBirth: dateOfBirth,
        allergy: formData.allergy,
        allergyInfo: formData.allergyInfo,
        disablityStatus: formData.disablityStatus,
        highSchool: formData.highSchool,
        photoUrl: file ? imageUrl : formData.photoUrl,
      }
      const res = await updateYouthAccount({
        isTokenRequired: true,
        youthId: userData?.Id,
        data: updateData,
      })
    }
  }
  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
    >,
  ) => {
    const { name, value } = e.target
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }))
  }

  const handlePhoneChange = (value: string) => {
    setFormData((prev) => ({
      ...prev,
      phone: value,
    }))
  }

  const handleRadioChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target

    setFormData((prev) => ({
      ...prev,
      [name]: value === 'yes' ? true : false,
    }))
  }

  return (
    <div className="w-full max-w-2xl flex justify-center m-auto mt-4">
      <div className="px-5 md:px-10 pb-5 space-y-6 w-full">
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-2">
            <label
              htmlFor="profilePicture"
              className="block text-sm font-medium mb-3"
            >
              Profile Picture
            </label>
            <ProfileUploadYouth
              setSelectedFile={setFile}
              profilePhoto={userData.Photo__c}
            />
          </div>
          <div className="space-y-1">
            <label htmlFor="firstName" className="block text-sm font-medium">
              First Name
            </label>
            <input
              type="text"
              id="firstName"
              name="firstName"
              value={formData.firstName}
              onChange={handleChange}
              className="text-sm w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-300"
              placeholder="Enter first name"
              required
            />
          </div>
          <div className="space-y-1">
            <label htmlFor="lastName" className="block text-sm font-medium">
              Last Name
            </label>
            <input
              type="text"
              id="lastName"
              name="lastName"
              value={formData.lastName}
              onChange={handleChange}
              className="text-sm w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-300"
              placeholder="Enter last name"
              required
            />
          </div>
          <CustomDatePicker
            key={dateOfBirth}
            title="Birthday"
            setDateOfBirth={setDateOfBirth}
            dateOfBirth={dateOfBirth}
          />
          <div className="space-y-1">
            <label htmlFor="phone" className="block text-sm font-medium">
              Phone Number
            </label>
            <PhoneInput
              country={'us'}
              value={formData.phone}
              onChange={(_, __, ___, formattedValue) => {
                setFormData((prev) => ({
                  ...prev,
                  phone: formattedValue.replace(/\s/g, ''),
                }))
              }}
              inputProps={{
                name: 'parentPhoneNumber',
                required: true,
                className: 'w-full p-2 border border-gray-300 rounded pl-12',
              }}
            />
          </div>

          <div className="space-y-1">
            <label htmlFor="email" className="block text-sm font-medium">
              Email
            </label>
            <input
              type="text"
              id="email"
              name="email"
              value={userData?.PersonEmail || 'Email not added yet'}
              onChange={handleChange}
              className="text-sm w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-300"
              placeholder=""
              required
              disabled
            />
          </div>

          <div className="space-y-1">
            <h1 className="text-lg font-bold mb-1">Other details</h1>
            <div className="grid grid-cols-2 gap-3">
              <div className="space-y-1">
                <label className="block text-sm font-medium">Allergy</label>
                <div className="flex gap-4">
                  <label className="inline-flex items-center gap-1">
                    <input
                      type="radio"
                      name="allergy"
                      value="yes"
                      checked={formData.allergy === true}
                      onChange={handleRadioChange}
                    />
                    Yes
                  </label>
                  <label className="inline-flex items-center gap-1">
                    <input
                      type="radio"
                      name="allergy"
                      value="no"
                      checked={formData.allergy === false}
                      onChange={handleRadioChange}
                    />
                    No
                  </label>
                </div>
              </div>
              <div className="space-y-1">
                <label
                  htmlFor="allergyInfo"
                  className="block text-sm font-medium"
                >
                  Allergy Info
                </label>
                <input
                  type="text"
                  id="allergyInfo"
                  name="allergyInfo"
                  value={formData.allergyInfo || ''}
                  onChange={handleChange}
                  className="text-sm w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-300"
                  placeholder="Enter allergy info"
                />
              </div>
              <div className="space-y-1">
                <label
                  htmlFor="highSchool"
                  className="block text-sm font-medium"
                >
                  High School
                </label>
                <input
                  type="text"
                  id="highSchool"
                  name="highSchool"
                  value={formData.highSchool || ''}
                  onChange={handleChange}
                  className="text-sm w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-300"
                  placeholder="Enter high school"
                />
              </div>
            </div>
          </div>
          <CustomButton
            title="Update"
            isLoading={isUploadingImage || isLoading}
            onClick={undefined}
            type="submit"
          />
        </form>
      </div>
    </div>
  )
}

export default EditYouthAccount
