'use client'
import React, { useEffect, useMemo, useState } from 'react'
import Share from '../Icons/Share'
import Person from '../Icons/Person'
import Class from '../Icons/Class'
import Clock from '../Icons/Clock'
import CalendarIcon from '../Icons/CalendarIcon'
import CustomButton from '../CustomComponents/CustomButton/CustomButton'
import CustomTooltip from '../CustomComponents/CustomTooltip/CustomTooltip'
import { addToCart } from '@/lib/actions/cart.actions'
import { toast } from 'react-toastify'
import { usePathname, useRouter } from 'next/navigation'
import { useApi } from '@/hooks/useApi'
import { formatDateToAmerican, formatSalesforceTime } from '@/utils/utils'
import { useDispatch } from 'react-redux'
import { increaseCartCount } from '@/lib/redux'
import AddToCalendarButton from '../AddToCalendarButton/AddToCalendarButton'
import { EVENT_TYPES, ROLES } from '@/types/enum'
import { useAuth } from '@/contexts/AuthContext'
import { buyClassForYouth } from '@/lib/actions/parent.actions'

const PurchaseClass = ({
  classData,
  userData,
  isPrerequisiteSatisfied,
  isAlreadyEnrolled,
  isApprovalRaised,
  parentChildren,
}: {
  classData: any
  userData: any
  isPrerequisiteSatisfied: boolean
  isAlreadyEnrolled: boolean
  isApprovalRaised?: boolean
  parentChildren?: any[]
}) => {
  const [isAdded, setIsAdded] = useState<boolean>(false)
  const [response, addCart] = useApi(
    (
      access: string,
      id: string,
      amount: number,
      classType?: 'In Person' | 'Online',
    ) => addToCart(access, id, amount, classType),
  )
  const [classPrice, setClassPrice] = useState<string>(classData?.Price__c)
  const [classType, setClassType] = useState<string>('')
  const [error, setError] = useState<boolean>(false)
  const [childId, setChildId] = useState<string>('')
  const pathname = usePathname()
  const [youthResponse, youthPurchased] = useApi(
    (
      accessToken: string,
      youthId: string,
      classId: string,
      userAgreesToPay?: number,
      attendType?: 'In Person' | 'Online',
    ) =>
      buyClassForYouth(
        accessToken,
        youthId,
        classId,
        userAgreesToPay,
        attendType,
      ),
  )
  const router = useRouter()
  const dispatch = useDispatch()
  useEffect(() => {
    if (response.isSuccess) {
      toast.success('Successfully added!', { autoClose: 1500 })
      dispatch(increaseCartCount())
      setIsAdded(true)
    }
  }, [response])

  useEffect(() => {
    if (youthResponse.isSuccess) {
      toast.success('Successfully purchased for child!', { autoClose: 1500 })
    }
  }, [youthResponse])

  useEffect(() => {
    if (Number(classPrice) < Number(classData?.Minimum_Price__c)) {
      setError(true)
    } else {
      setError(false)
    }
  }, [classPrice])

  const isUserAllowed = useMemo(() => {
    if (!userData) return { isAllowed: true, roleName: '', Type: '' }
    const userRole: string = userData?.role.split(' ')[0] || ''
    const classType: string = classData?.recordType?.Name.split(' ')[0] || ''
    if (classType.toLocaleLowerCase() === userRole.toLocaleLowerCase()) {
      return { isAllowed: true, roleName: userRole, Type: classType }
    } else if (
      userRole.toLocaleLowerCase() === 'youth' &&
      classType.toLocaleLowerCase() === 'adult'
    ) {
      return { isAllowed: false, roleName: userRole, Type: classType }
    } else if (
      userRole.toLocaleLowerCase() === 'adult' &&
      classType.toLocaleLowerCase() === 'youth'
    ) {
      return { isAllowed: false, roleName: userRole, Type: classType }
    }
    return { isAllowed: true, roleName: userRole, Type: classType }
  }, [classData, userData])

  const tooltipMessage = useMemo(() => {
    if (!isUserAllowed.isAllowed) {
      return `This class is only available for ${(isUserAllowed.Type || '').toLocaleLowerCase()} students`
    }
    if (!classData?.Available_for_booking__c) {
      return 'This class is not available for booking'
    }
    return ''
  }, [isUserAllowed, classData?.Available_for_booking__c])
  const handleAddToCart = async () => {
    if (childId && childId !== '') {
      youthPurchased(childId, classData?.Id, undefined, classType)
      return
    }
    if (isAlreadyEnrolled) {
      toast.error('You are already enrolled in this class!')
      return
    }
    if (!isPrerequisiteSatisfied) {
      toast.error('Please complete the prerequisites first!')
      return
    }
    if (
      userData &&
      classData?.Flexible_Pricing__c &&
      Number(classPrice) < Number(classData?.Minimum_Price__c)
    ) {
      toast.error(`Minimum price is $${classData?.Minimum_Price__c}`)
      return
    }
    if (userData) {
      const classCartData = [classData?.Id, Number(classPrice)]
      if (classData?.Type__c === EVENT_TYPES.HYBRID) {
        classCartData.push(classType)
      }
      await addCart(...classCartData)
    } else {
      toast.error('Please sign in first', { autoClose: 3000 })
      localStorage.setItem('redirect', pathname)
      router.push('/sign-in')
    }
  }

  const classTime = useMemo(
    () =>
      formatSalesforceTime(
        classData?.Meetings__r?.records[0]?.Starts_On__c,
        classData?.Meetings__r?.records[0]?.Ends_On__c,
      ),
    [],
  )
  const handleShare = () => {
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL
    navigator.clipboard.writeText(baseUrl + '/' + `classes/${classData?.Id}`)
    toast.success('The link has been copied to your clipboard', {
      autoClose: 2500,
    })
  }

  const meetings: Array<any> =
    classData?.Meetings__r?.records?.sort(
      (a: any, b: any) =>
        new Date(a.Starts_On__c).getTime() - new Date(b.Starts_On__c).getTime(),
    ) || []

  const completedMeetings = useMemo(() => {
    return meetings.reduce((acc, item) => {
      return item?.Is_Active__c === false ? acc + 1 : acc
    }, 0)
  }, [meetings])
  return (
    <div className="p-7 bg-white rounded-xl w-full">
      {classData?.Category__c && (
        <div className="rounded-md bg-color-yellow-3 px-2 py-1 w-max flex items-center">
          <span className="px-2 py-1 font-bold text-[10px] leading-[15px] text-center align-middle text-color-grey-14">
            {classData?.Category__c}
          </span>
        </div>
      )}
      <div className="flex items-center justify-between gap-3 mt-2">
        <h1 className="text-lg font-bold w-full">
          {classData?.Title__c ?? ''}
        </h1>
        <Share onClick={handleShare} />
      </div>
      <div className="flex items-center justify-start w-full flex-col gap-2 text-sm mt-2">
        <div className="flex items-center gap-3 w-full">
          <div className="flex items-center gap-2">
            <Person />
            <span>
              {' '}
              {classData?.Total_Seats__c - classData?.Booked_Seats__c} out of{' '}
              {classData?.Total_Seats__c} seats
            </span>
          </div>
          <div className="flex items-center gap-2">
            <Class />
            <span>
              {classData?.Total_Meetings_Count__c <= 0
                ? 0
                : classData?.Total_Meetings_Count__c}{' '}
              Meetings
            </span>
          </div>
        </div>
        <div className="flex items-center gap-2 w-full">
          <CalendarIcon />
          <span className="w-max">
            {formatDateToAmerican(classData?.Start_Date__c)} -{' '}
            {formatDateToAmerican(classData?.End_Date__c)}
          </span>
        </div>
        <div className="flex items-center gap-2 w-full">
          <Clock />
          <span>{classTime}</span>
        </div>
      </div>
      {classData?.Meetings__r?.totalSize > 0 && (
        <>
          <div className="flex items-center justify-between mt-4">
            <h2 className="text-base font-bold">{`${classData?.Meetings__r?.totalSize} Meetings`}</h2>
            <span className="text-color-grey-1">
              {completedMeetings}/{classData?.Meetings__r?.totalSize} Done
            </span>
          </div>
          <div
            className={`grid grid-cols-[10%,40%,auto] gap-4 text-sm mt-5 px-2.5  pt-2.5 rounded-md  ${
              meetings.length > 9
                ? 'border border-color-grey-2 overflow-scroll no-scrollbar -mx-5 overflow-x-hidden max-h-[400px]'
                : 'mx-0'
            }`}
          >
            {meetings.map((meeting: any, index: number) => {
              const meetingStartDate = formatDateToAmerican(
                meeting?.Starts_On__c,
              )
              const meetingTime = formatSalesforceTime(
                meeting?.Starts_On__c,
                meeting?.Ends_On__c,
              )
              return (
                <React.Fragment key={meeting?.Id}>
                  <div className="p-1 bg-color-grey rounded-lg flex items-center justify-center font-medium">
                    {index + 1}
                  </div>
                  <div className="text-color-grey-1 flex items-center justify-center">
                    {meetingStartDate}
                  </div>
                  <div className="text-color-grey-1 flex items-center justify-end">
                    {meetingTime}
                  </div>
                </React.Fragment>
              )
            })}
          </div>
        </>
      )}
      <>
        {!classData?.Flexible_Pricing__c && !isAlreadyEnrolled && (
          <h1 className="text-color-black font-bold text-2xl mt-3">
            $ {classData?.Price__c || 0}
          </h1>
        )}
        <div className="my-3 flex gap-4 flex-col">
          {classData?.Flexible_Pricing__c && userData && !isAlreadyEnrolled && (
            <div className="w-full">
              <h1 className="text-base font-bold mb-1">Name your own price</h1>

              <div
                className={`flex items-center border-2 ${error ? 'border-color-red' : 'border-gray-200'} rounded-lg h-10 p-3 bg-white`}
              >
                <span className="text-xl font-medium mr-1">$</span>
                <input
                  type="text"
                  className="text-lg text-gray-400 focus:outline-none flex-grow"
                  placeholder="0"
                  value={classPrice}
                  onChange={(e) => {
                    // Allow only numbers with decimal point
                    const value = e.target.value
                    const regex = /^[0-9]*\.?[0-9]*$/

                    if (regex.test(value) || value === '') {
                      setClassPrice(value)
                    }
                  }}
                  onBlur={() => setError(classPrice === '')}
                />
              </div>

              {error && (
                <p className="text-color-red font-medium mt-2">
                  You need to enter minimum amount ${' '}
                  {classData?.Minimum_Price__c}
                </p>
              )}
            </div>
          )}
          {classData?.Type__c === EVENT_TYPES.HYBRID &&
            !isAlreadyEnrolled &&
            userData && (
              <div className="w-full">
                <select
                  id="classTypeSelect"
                  className="w-full border border-gray-300 rounded-md text-sm font-medium p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 transition"
                  value={classType}
                  onChange={(e) => setClassType(e.target.value)}
                >
                  <option value="" disabled>
                    Choose class mode
                  </option>
                  <option value="Online">Online</option>
                  <option value="In Person">In Person</option>
                </select>
              </div>
            )}
          {parentChildren && parentChildren.length > 0 && (
            <div className="w-full">
              <select
                id="classTypeSelect"
                className="w-full shadow-md border border-gray-300 rounded-md text-sm font-medium p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 transition"
                value={childId}
                onChange={(e) => setChildId(e.target.value)}
              >
                <option value="" disabled>
                  Choose child
                </option>
                {parentChildren.map((child: any) => (
                  <option key={child.Id} value={child.Id}>
                    {child.Name}
                  </option>
                ))}
              </select>
            </div>
          )}

          {isAdded ? (
            <CustomButton
              isLoading={false}
              title="Go to cart"
              onClick={() => router.push('/cart')}
              height={50}
            />
          ) : (
            <CustomTooltip
              text={tooltipMessage}
              isVisible={
                !isUserAllowed.isAllowed || !classData?.Available_for_booking__c
              }
            >
              <CustomButton
                title={
                  childId && childId !== ''
                    ? 'Purchase for Child'
                    : isApprovalRaised
                      ? 'Approval Requested'
                      : isAlreadyEnrolled
                        ? 'Already enrolled'
                        : 'Add to cart'
                }
                isLoading={response.isFetching}
                isDisabled={
                  !classData?.Available_for_booking__c ||
                  isApprovalRaised ||
                  isAlreadyEnrolled ||
                  !isUserAllowed.isAllowed ||
                  (classType === '' &&
                    classData?.Type__c === EVENT_TYPES.HYBRID &&
                    userData)
                }
                onClick={handleAddToCart}
                height={50}
              />
            </CustomTooltip>
          )}
        </div>
        {classData?.Show_Add_to_Calendar__c && classData?.Rrule__c && (
          <AddToCalendarButton
            title={classData?.Title__c}
            rrule={classData?.Rrule__c}
          />
        )}
        <p className="font-medium text-sm text-color-black mt-3">
          All meetings will be booked after a single payment.
        </p>
      </>
    </div>
  )
}

export default PurchaseClass
