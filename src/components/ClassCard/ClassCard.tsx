'use client'
import Image from 'next/image'
import React from 'react'
import Star from '../Icons/Star'
import Person from '../Icons/Person'
import Class from '../Icons/Class'
import Genre from '../Icons/Genre'
import Clock from '../Icons/Clock'
import EventType from '../EventType/EventType'
import { useRouter } from 'next/navigation'
import ClassType from '../ClassType/ClassType'
import clsx from 'clsx'

interface ClassCardProps {
  data?: any
  showRating?: boolean
  showTeachers?: boolean
  showDetails?: boolean
  showProgress?: boolean
  shadow?: string
  border?: boolean
  theme?: 'light' | 'dark'
}

const ClassCard = ({
  data = null,
  showRating = false,
  showTeachers = true,
  showDetails,
  shadow = 'sm',
  border = false,
  theme = 'light',
}: ClassCardProps) => {
  const router = useRouter()
  const totalSeats = Number(data?.Total_Seats__c) || 0
  const bookedSeats = Number(data?.Booked_Seats__c) || 0
  const availableSeats =
    totalSeats - bookedSeats < 0 ? 28 : totalSeats - bookedSeats
  const handleNavigationTeacher = (e: any) => {
    e.preventDefault()
    e.stopPropagation()
    router.push(`/teachers/${data?.teacher_data?.Id}`)
  }
  const teachers = data?.associated_teachers
    ? data?.associated_teachers[0]
    : null
  const teacherImage = data?.teacher_data?.Photo__c || teachers?.teacher_photo
  const teacherName = data?.teacher_data?.Name || teachers?.teacher_name
  return (
    <div
      className={clsx(
        `w-full mb-2 p-4 md:p-5 rounded-2xl shadow-${shadow} ${border ? 'border border-color-grey-2' : ''}`,
        {
          'bg-gray-900 text-white': theme === 'dark',
          'bg-white': theme === 'light',
        },
      )}
    >
      <div className="relative">
        {data?.Banner_Image__c && (
          <div className="w-full aspect-video overflow-hidden relative">
            <Image
              src={data?.Banner_Image__c}
              alt="User avatar"
              fill
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              className="rounded-xl object-cover"
              priority
            />
          </div>
        )}
        {data?.Type__c && <ClassType title={data?.Type__c} />}
      </div>
      {data?.Garnish__c && <EventType title={data?.Garnish__c} />}
      <h1
        className={`text-lg ${data?.Garnish__c ? '' : 'mt-3'} font-semibold line-clamp-1`}
        title={data?.Title__c ?? data?.Name}
      >
        {data?.Title__c ?? data?.Name}
      </h1>

      <div className="flex items-center justify-between mt-3">
        {showTeachers && (
          <div className="flex items-center gap-3">
            <Image
              width={45}
              height={45}
              onClick={handleNavigationTeacher}
              src={teacherImage || '/teacher-dummy.png'}
              alt="User avatar"
              className="rounded-full w-12 h-12 object-cover"
              priority
            />
            <span
              className="text-color-grey-1 text-sm"
              onClick={handleNavigationTeacher}
            >
              {teacherName || 'Anonymous Teacher'}
            </span>
          </div>
        )}
        {showRating && (
          <div className="flex items-center">
            <Star />
            <span>4.5</span>
          </div>
        )}
      </div>
      {showDetails ? (
        <div>
          <div className="flex items-center gap-4 mt-2" title={data?.Genre__c}>
            <Genre />
            <span className="truncate">{data?.Genre__c}</span>
          </div>
          <div className="flex flex-wrap items-center justify-between mt-2 text-sm">
            <div className="flex items-center gap-3">
              <Person />
              <span>
                {data?.Booked_Seats__c}{' '}
                {Number(data?.Booked_Seats__c) === 1 ? 'Attendee' : 'Attendees'}
              </span>
            </div>
            <div className="flex items-center gap-3">
              <Class />
              <span>{data?.Total_Meetings_Count__c} Meetings</span>
            </div>
            <div className="flex items-center gap-3">
              <Clock />
              <span>
                {Number.isInteger(Number(data?.Duration__c) / 60)
                  ? Number(data?.Duration__c) / 60
                  : (Number(data?.Duration__c) / 60).toFixed(1)}{' '}
                h
              </span>
            </div>
          </div>
        </div>
      ) : (
        <div className="flex items-center justify-start gap-5 mt-3">
          <div className="flex items-center gap-2">
            <Person />
            <span>{availableSeats} Seats Available</span>
          </div>
          <div className="flex items-center gap-2">
            <Class />
            <span>Online</span>
          </div>
        </div>
      )}
    </div>
  )
}

export default ClassCard
