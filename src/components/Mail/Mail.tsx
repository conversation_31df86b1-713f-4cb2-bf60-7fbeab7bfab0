import React, { useEffect } from 'react'
import DeleteIcon from '../Icons/DeleteIcon'
import { toast } from 'react-toastify'
import { useApi } from '@/hooks/useApi'
import { deleteMailingListItem } from '@/lib/actions/mailingList.actions'
import GreenTick from '../Icons/GreenTick'
import CopyIcon from '../Icons/CopyIcon'

const Mail = ({
  mail,
  isEdit,
  setMailingList,
  showNotificationModal,
}: {
  mail: any
  isEdit: boolean
  setMailingList: any
  showNotificationModal?: any
}) => {
  const [mailId, setMailId] = React.useState<string>('')
  const [copied, setCopied] = React.useState(false)
  const [deletMailItemResponse, deletMailItem] = useApi(
    (id: string) => deleteMailingListItem(id),
    false,
  )
  useEffect(() => {
    if (deletMailItemResponse.isSuccess) {
      toast.success('Deleted successfully', {
        autoClose: 2000,
      })
      setMailingList((prev: any) =>
        prev.filter((item: any) => item?.id !== mailId),
      )
      setMailId('')
    }
  }, [deletMailItemResponse])

  const deleteMailFromList = (data: any) => {
    if (isEdit) {
      setMailId(data?.id)
      deletMailItem(data?.id)
    } else {
      setMailingList((prev: any) => prev.filter((item: any) => item !== data))
    }
  }

  const handleCopy = (text: string) => {
    if (!text) return
    navigator.clipboard.writeText(text)
    setCopied(true)
    setTimeout(() => {
      setCopied(false)
    }, 2000)
  }

  const handleNotifySingle = (e: any) => {
    e.stopPropagation()
    showNotificationModal && showNotificationModal(mail?.id, true, mail?.email)
  }

  return (
    <div className="flex justify-between items-center w-full h-12 border-b-2 border-color-grey-2 rounded-lg">
      <span className="text-sm">{mail?.email ?? mail}</span>
      <div className="flex items-center justify-center gap-4">
        <h2
          className="text-sm font-medium text-color-blue-1 hover:text-color-blue-3 cursor-pointer"
          onClick={handleNotifySingle}
        >
          Notify
        </h2>
        <button
          onClick={() => handleCopy(mail?.email ?? mail)}
          className="w-8 h-8 flex items-center justify-center text-gray-400 hover:text-gray-600 transition-colors"
          aria-label="Copy user information"
        >
          {(mail?.email ?? mail) && (
            <>{copied ? <GreenTick color="#DFDFDF" /> : <CopyIcon />}</>
          )}
        </button>
        {isEdit && (
          <>
            {deletMailItemResponse.isFetching ? (
              <div className="delete-icon-loader"></div>
            ) : (
              <DeleteIcon handleClick={() => deleteMailFromList(mail)} />
            )}
          </>
        )}
      </div>
    </div>
  )
}

export default Mail
