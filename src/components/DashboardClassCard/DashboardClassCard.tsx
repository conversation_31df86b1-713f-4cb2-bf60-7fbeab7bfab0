import Image from 'next/image'
import React from 'react'
import Genre from '../Icons/Genre'

const DashboardClassCard = () => {
  return (
    <div className="flex flex-col p-4 bg-white rounded-2xl w-full">
      <div className="relative w-full aspect-video rounded-xl overflow-hidden">
        <Image
          src={'/class_pic.jpeg'}
          alt="User avatar"
          fill
          className="object-cover"
          priority
        />
      </div>
      <h1
        className="text-sm font-semibold mt-3 flex flex-wrap"
        title="Sound Bath Meditation: Enhance Your Creativity"
      >
        Sound Bath Meditation: Enhance Your Creativity
      </h1>
      <div className="flex items-center justify-between mt-3">
        <div className="flex items-center gap-3">
          <Image
            width={45}
            height={45}
            src={'/sample_image.jpeg'}
            alt="Author avatar"
            className="rounded-full h-7 w-7 object-cover"
          />
          <span className="text-gray-400 text-sm"><PERSON></span>
        </div>
      </div>
      <div className="flex flex-col gap-3 mt-2">
        <div className="flex items-center gap-2">
          <Genre />
          <span className="font-medium text-sm">Adventure</span>
        </div>
        <h1 className="text-xs text-blue-500 font-semibold cursor-pointer">
          View Class Details
        </h1>
      </div>
    </div>
  )
}

export default DashboardClassCard
