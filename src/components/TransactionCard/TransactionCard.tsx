import React from 'react'

const TransactionCard = ({ transaction }: { transaction: any }) => {
  const isCredit = transaction.type === 'Credit'
  return (
    <div className="grid grid-cols-4 gap-4 px-5 py-3 border-b last:border-b-0 items-center w-full">
      <div className="flex items-center gap-2">
        {isCredit ? (
          <div className="h-6 w-6 bg-color-green-3 text-neutral-white rounded-full grid place-items-center shrink-0">
            <span className="text-base font-mono  leading-none">+</span>
          </div>
        ) : (
          <div className="h-6 w-6 bg-color-orange text-neutral-white rounded-full grid place-items-center shrink-0">
            <span className="text-base font-mono leading-none">-</span>
          </div>
        )}
        <span className="font-semibold text-sm">{transaction.type}</span>
      </div>
      <div className={`font-medium text-sm`}>
        $
        {!isCredit && transaction.amount < 0
          ? Math.abs(transaction.amount)
          : transaction.amount}
      </div>
      <div className="font-medium text-sm">
        {new Date(transaction.createdDate).toLocaleDateString('en-US', {
          month: 'short',
          day: 'numeric',
          year: 'numeric',
        })}
      </div>
      <div className="text-sm font-medium text-color-blue-6 truncate overflow-hidden whitespace-nowrap">
        {transaction.id || 'N/A'}
      </div>
    </div>
  )
}

export default TransactionCard
