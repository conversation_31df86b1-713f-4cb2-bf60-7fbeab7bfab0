'use client'

import { useState } from 'react'
import Image from 'next/image'

interface Teacher {
  id: number
  name: string
  category?: string
  classes?: string
  students?: string
  image: string
  bgColor: string
}

const HomeMeetTeachers: React.FC = () => {
  // State to track which teacher card is expanded
  const [expandedCard, setExpandedCard] = useState<number>(0) // Default to first card

  // Handle card click
  const handleCardClick = (id: number) => {
    setExpandedCard(id)
  }

  // Teachers data
  const teachers: Teacher[] = [
    {
      id: 0,
      name: '<PERSON><PERSON> <PERSON><PERSON>',
      category: 'Fiction',
      classes: '60+ Classes',
      students: '450+ Students',
      image: '/lady1.png',
      bgColor: 'bg-color-orange',
    },
    {
      id: 1,
      name: '<PERSON>',
      image: '/lady2.png',
      bgColor: 'bg-color-brown',
    },
    {
      id: 2,
      name: '<PERSON>',
      image: '/lady3.png',
      bgColor: 'bg-color-green-3',
    },
    {
      id: 3,
      name: '<PERSON>',
      image: '/lady4.png',
      bgColor: 'bg-color-blue-5',
    },
  ]

  return (
    <section className="bg-color-pink px-40 py-32">
      <style jsx>{`
        .expanded-content {
          animation: expand 0.5s forwards ease-in-out;
        }

        @keyframes expand {
          0% {
            transform: scale(0.95);
            opacity: 0.7;
          }
          100% {
            transform: scale(1);
            opacity: 1;
          }
        }
      `}</style>

      <h1 className="font-extrabold text-5xl w-full text-center">
        Meet your teachers
      </h1>
      <div className="flex h-[387px] grid-cols-10 items-center justify-between gap-4 mt-20 teacher-cards">
        {teachers.map((teacher, index) => (
          <div
            key={index}
            onClick={() => handleCardClick(teacher.id)}
            className={`card rounded-3xl pt-7 h-[387px] ${
              expandedCard === teacher.id ? 'col-span-4' : 'col-span-2'
            } flex ${
              expandedCard === teacher.id
                ? 'items-end justify-end'
                : 'items-center justify-between flex-col'
            } ${teacher.bgColor} overflow-hidden transition-all duration-300 ease-in-out cursor-pointer`}
          >
            {expandedCard === teacher.id ? (
              // Expanded view with animation
              <div className="w-full h-full flex items-end justify-end expanded-content">
                <div className="flex flex-col justify-start ml-10 gap-2 mb-20">
                  <svg
                    width="49"
                    height="49"
                    viewBox="0 0 49 49"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      opacity="0.2"
                      d="M28.5638 40.8021L25.5261 43.8398C25.3478 44.0183 25.1361 44.1599 24.9031 44.2565C24.6701 44.3531 24.4203 44.4028 24.168 44.4028C23.9157 44.4028 23.666 44.3531 23.4329 44.2565C23.1999 44.1599 22.9882 44.0183 22.8099 43.8398L16.6481 37.6828L8.57388 45.7594C8.3956 45.9378 8.1839 46.0794 7.95086 46.176C7.71783 46.2726 7.46804 46.3223 7.21578 46.3223C6.96352 46.3223 6.71373 46.2726 6.4807 46.176C6.24767 46.0794 6.03596 45.9378 5.85768 45.7594L2.81276 42.7216C2.45304 42.3617 2.25098 41.8736 2.25098 41.3647C2.25098 40.8559 2.45304 40.3678 2.81276 40.0078L10.8894 31.924L4.73233 25.7718C4.55386 25.5935 4.41227 25.3818 4.31567 25.1488C4.21907 24.9158 4.16935 24.666 4.16935 24.4137C4.16935 24.1615 4.21907 23.9117 4.31567 23.6786C4.41227 23.4456 4.55386 23.2339 4.73233 23.0556L7.77006 20.0083C7.94833 19.8298 8.16004 19.6882 8.39307 19.5916C8.62611 19.495 8.87589 19.4453 9.12816 19.4453C9.38042 19.4453 9.6302 19.495 9.86324 19.5916C10.0963 19.6882 10.308 19.8298 10.4863 20.0083L28.5542 38.0763C28.7348 38.2541 28.8783 38.4659 28.9766 38.6995C29.0749 38.933 29.1259 39.1838 29.1268 39.4372C29.1277 39.6906 29.0784 39.9417 28.9818 40.1759C28.8852 40.4102 28.7431 40.623 28.5638 40.8021Z"
                      fill="white"
                    />
                    <path
                      d="M46.4021 0.251953H31.0455C30.7515 0.251924 30.4615 0.319411 30.1977 0.449209C29.934 0.579007 29.7036 0.767648 29.5242 1.00059L14.1677 20.9665L11.8498 18.6535C11.4932 18.2965 11.0698 18.0133 10.6037 17.8201C10.1377 17.6269 9.63811 17.5275 9.13359 17.5275C8.62906 17.5275 8.12949 17.6269 7.66342 17.8201C7.19736 18.0133 6.77394 18.2965 6.41739 18.6535L3.37247 21.7008C3.01588 22.0573 2.73301 22.4805 2.54002 22.9464C2.34703 23.4122 2.2477 23.9115 2.2477 24.4158C2.2477 24.92 2.34703 25.4193 2.54002 25.8852C2.73301 26.351 3.01588 26.7743 3.37247 27.1308L8.1714 31.9297L1.45289 38.6482C1.0963 39.0047 0.813436 39.428 0.620446 39.8938C0.427456 40.3597 0.328125 40.859 0.328125 41.3632C0.328125 41.8674 0.427456 42.3667 0.620446 42.8326C0.813436 43.2984 1.0963 43.7217 1.45289 44.0782L4.49782 47.1207C5.21772 47.8402 6.19384 48.2443 7.21161 48.2443C8.22938 48.2443 9.2055 47.8402 9.92541 47.1207L16.6439 40.4022L21.4428 45.2011C21.7994 45.5581 22.2228 45.8413 22.6889 46.0345C23.1549 46.2277 23.6545 46.3271 24.159 46.3271C24.6636 46.3271 25.1631 46.2277 25.6292 46.0345C26.0953 45.8413 26.5187 45.5581 26.8752 45.2011L29.9202 42.1538C30.2767 41.7973 30.5596 41.3741 30.7526 40.9082C30.9456 40.4424 31.0449 39.9431 31.0449 39.4388C31.0449 38.9346 30.9456 38.4353 30.7526 37.9694C30.5596 37.5036 30.2767 37.0803 29.9202 36.7238L27.6071 34.4107L47.573 19.0542C47.8066 18.8744 47.9956 18.6432 48.1255 18.3785C48.2553 18.1139 48.3224 17.8229 48.3217 17.5281V2.17153C48.3217 1.66242 48.1194 1.17417 47.7594 0.814183C47.3994 0.454194 46.9112 0.251953 46.4021 0.251953ZM7.21641 44.4021L4.17149 41.3596L10.89 34.6411L13.9325 37.6836L7.21641 44.4021ZM24.159 42.4826L6.09106 24.417L9.13838 21.3673L27.2064 39.4376L24.159 42.4826ZM44.4825 16.5827L24.8693 31.6706L22.2443 29.0455L34.3232 16.9666C34.683 16.6064 34.8851 16.118 34.8849 15.6089C34.8846 15.0997 34.6822 14.6115 34.322 14.2516C33.9618 13.8918 33.4734 13.6897 32.9642 13.6899C32.4551 13.6902 31.9669 13.8926 31.607 14.2528L19.5281 26.3293L16.9055 23.7043L31.9909 4.0911H44.4825V16.5827Z"
                      fill="white"
                    />
                  </svg>
                  <h1 className="font-bold text-2xl text-white">
                    {teacher.name}
                  </h1>
                  <div className="bg-white px-4 py-2 rounded-3xl w-max font-bold">
                    {teacher.category || 'Category'}
                  </div>
                  <h1 className="text-white text-base">
                    {teacher.classes || 'Classes'}
                  </h1>
                  <h1 className="text-white text-base">
                    {teacher.students || 'Students'}
                  </h1>
                </div>
                <Image
                  src={teacher.image}
                  alt={`Teacher ${index + 1}`}
                  width={400}
                  height={400}
                  className="object-contain h-60 aspect-square rounded-br-3xl translate-x-1/4"
                />
              </div>
            ) : (
              // Collapsed view - preserving original layout
              <>
                <h1 className="font-bold text-2xl text-white">
                  {teacher.name}
                </h1>
                <Image
                  src={teacher.image}
                  alt={`Teacher ${index + 1}`}
                  width={400}
                  height={400}
                  className="rounded-3xl object-contain h-60 aspect-square"
                />
              </>
            )}
          </div>
        ))}
      </div>
    </section>
  )
}

export default HomeMeetTeachers
