'use client'
import React, { useEffect, useRef, useState } from 'react'
import MonthView from '../CalendarView/MonthView'
import EventDate from '../EventDate/EventDate'
import { addMonths, differenceInMonths } from 'date-fns'

const ClubCalendarView = () => {
  const calendarRef = useRef<any>(null)
  const [currentDate, setCurrentDate] = useState(new Date())
  const [prevDate, setPrevDate] = useState(new Date())

  useEffect(() => {
    const monthDifference = differenceInMonths(currentDate, prevDate)
    if (monthDifference > 0) {
      for (let i = 0; i < monthDifference; i++) {
        handleNextMonth()
      }
    } else if (monthDifference < 0) {
      for (let i = 0; i < Math.abs(monthDifference); i++) {
        handlePrevMonth()
      }
    }
  }, [currentDate])

  useEffect(() => {
    setCurrentDate(prevDate)
  }, [prevDate])

  const handlePrevMonth = () => {
    if (calendarRef.current) {
      const calendarApi = (calendarRef.current as any)?.getApi()
      calendarApi.prev()
    }
    setPrevDate((prev) => addMonths(prev, -1))
  }
  const handleNextMonth = () => {
    if (calendarRef.current) {
      const calendarApi = (calendarRef.current as any)?.getApi()
      calendarApi.next()
    }
    setPrevDate((prev) => addMonths(prev, 1))
  }
  const handlePrevNextClick = () => {
    return { onPrev: handlePrevMonth, onNext: handleNextMonth }
  }

  return (
    <div className="flex items-center justify-center flex-col">
      <div className="w-11/12 mb-5">
        <div className="w-full mt-5">
          <div className="w-full flex items-center justify-between">
            <h1 className="font-extrabold text-base w-max">
              Calender Color Guide
            </h1>
            <EventDate
              currentDate={currentDate}
              setCurrentDate={setCurrentDate}
              calendarView={false}
              handlePrevNextClick={handlePrevNextClick}
            />
          </div>
        </div>
        <div className="flex flex-col space-y-4 my-4 max-w-md">
          <div className="flex items-center space-x-3">
            <div className="w-5 h-5 bg-green-600 rounded"></div>
            <span className="font-medium text-sm">
              In Person Events & Classes
            </span>
          </div>

          <div className="flex items-center space-x-3">
            <div className="w-5 h-5 bg-blue-600 rounded"></div>
            <span className="font-medium text-sm">Hybrid Events & Classes</span>
          </div>

          <div className="flex items-center space-x-3">
            <div className="w-5 h-5 bg-purple-600 rounded"></div>
            <span className="font-medium text-sm">Online Events & Classes</span>
          </div>
        </div>
        <MonthView
          currentMonth={currentDate}
          calendarRef={calendarRef}
          isEventView={true}
          isClubView={true}
        />
      </div>
    </div>
  )
}

export default ClubCalendarView
