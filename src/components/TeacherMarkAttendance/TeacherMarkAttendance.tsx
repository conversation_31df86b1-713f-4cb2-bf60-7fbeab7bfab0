'use client'
import React, { useEffect, useMemo } from 'react'
import { useSearchParams } from 'next/navigation'
import { formatSalesforceTime } from '@/utils/utils'
import { DateTime } from 'luxon'
import CustomButton from '../CustomComponents/CustomButton/CustomButton'
import { useAuth } from '@/contexts/AuthContext'

interface Meeting {
  Id: string
  Starts_On__c: string
  Meeting_Time__c: string
  Duration__c: number
  MeetingLocation__c: string
}

interface ClassDetails {
  Title__c: string
  Total_Meetings_Count__c: number
  classAttendees: {
    name: string
    email: string
  }[]
  upcomingMeetings: Meeting[]
  Meetings__r?: {
    records: Meeting[]
  }
  pastMeetings?: Meeting[]
}

const TeacherMarkAttendance = ({
  classDetails,
  handleModal,
  pastMeetings,
  hideButtonsInSchedule = false,
  attendanceState = {},
}: {
  classDetails: ClassDetails
  pastMeetings: Meeting[]
  handleModal?: (
    meetingId: string,
    markedAttendance: boolean,
    countType?: boolean,
  ) => void
  hideButtonsInSchedule?: boolean
  attendanceState?: any
}) => {
  const searchParams = useSearchParams()
  const meetingId = searchParams.get('meetingId')
  const { user } = useAuth()

  useEffect(() => {
    if (meetingId) {
      handleModal && handleModal(meetingId, false)
    }
  }, [])

  const formatDate = (dateString: string) => {
    const dateTime = DateTime.fromISO(dateString, { zone: 'utc' }).setZone(
      'America/New_York',
    )
    return dateTime.toFormat('cccc, MMMM d, yyyy')
  }

  const allMeetings = [
    ...((hideButtonsInSchedule
      ? classDetails?.pastMeetings
      : pastMeetings
    )?.map((meeting, index) => ({
      ...meeting,
      type: 'past',
      order: index + 1,
    })) || []),
    ...(classDetails?.upcomingMeetings || []).map((meeting, index) => ({
      ...meeting,
      type: 'upcoming',
      order: (classDetails?.pastMeetings?.length || 0) + index + 1,
    })),
  ]

  return (
    <>
      <div className="space-y-4 max-h-96 overflow-y-auto no-scrollbar">
        {allMeetings.map((meeting: any) => {
          const isPast = meeting.type === 'past'
          const hasAttendanceMarked =
            attendanceState[meeting.Id] || meeting?.Attendance_Marked__c
          const meetingTime = formatSalesforceTime(
            meeting?.Starts_On__c,
            meeting?.Ends_On__c,
          )
          return (
            <div
              key={meeting.Id}
              className={`grid grid-cols-12 gap-2 items-center`}
            >
              <div className="bg-color-grey p-2 col-span-1 rounded-lg w-7 h-7 flex items-center justify-center font-medium text-gray-700">
                {meeting.order}
              </div>
              <h3
                className={`text-sm font-medium ${hideButtonsInSchedule ? 'col-span-4' : 'col-span-4'} ${isPast ? 'text-gray-500 line-through' : 'text-gray-900'}`}
              >
                {formatDate(meeting.Starts_On__c)}
              </h3>
              {hideButtonsInSchedule && (
                <h3
                  className={`text-sm font-medium text-center col-span-3 ${isPast ? 'text-gray-500 line-through' : 'text-gray-900'}`}
                >
                  {meetingTime || ''}
                </h3>
              )}
              <h3
                className={`text-sm font-medium text-center ${hideButtonsInSchedule ? 'col-span-4' : 'col-span-3'} ${isPast ? 'text-gray-500 line-through' : 'text-gray-900'}`}
              >
                {meeting.Teacher__r?.Name || ''}
              </h3>
              {!hideButtonsInSchedule && (
                <div className="flex-shrink-0 flex justify-end col-span-4">
                  {isPast ? (
                    <CustomButton
                      onClick={() => {
                        handleModal &&
                          handleModal(
                            meeting?.Id,
                            meeting?.Attendance_Marked__c,
                            meeting?.Headcount_based_attendance__c,
                          )
                      }}
                      title={
                        hasAttendanceMarked
                          ? 'Edit Attendance'
                          : 'Mark Attendance'
                      }
                      isLoading={false}
                      height={12}
                      isDisabled={user && user.id !== meeting?.Teacher__r?.Id}
                      backgroundColor={
                        hasAttendanceMarked ? 'bg-white' : 'bg-color-yellow'
                      }
                      classes={
                        hasAttendanceMarked
                          ? 'text-gray-700 border border-gray-200 hover:bg-gray-50'
                          : 'hover:bg-color-yellow-2'
                      }
                    />
                  ) : (
                    <div className="h-12"></div>
                  )}
                </div>
              )}
            </div>
          )
        })}
      </div>

      {allMeetings.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          No meetings scheduled
        </div>
      )}
    </>
  )
}

export default TeacherMarkAttendance
