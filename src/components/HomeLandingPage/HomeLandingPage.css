.new-classes {
    animation: zoomFlip 2s ease-in-out;
    transform-origin: center;
}

@keyframes zoomFlip {
    0% {
        transform: scale(0) rotateX(0deg);
    }

    50% {
        transform: scale(1.2) rotateX(180deg);
    }

    100% {
        transform: scale(1) rotateX(360deg);
    }
}

.banner-image {
    animation: slideInFromLeft 1.5s ease-out;
}

.video-left {
    animation: slideInFromLeft 1.5s ease-out;
}


@keyframes slideInFromLeft {
    0% {
        transform: translateX(-100%);
        opacity: 0;
    }

    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

.reverse-string {
    display: inline-block;
    overflow: hidden;
    white-space: nowrap;
    width: max-content;
    border-right: 3px solid;
    width: 0;
    animation: typing 3s steps(20) infinite alternate-reverse;
}

@keyframes typing {
    from {
        width: 0
    }

    to {
        width: 20%
    }
}

.reverse-string-1 {
    display: inline-block;
    overflow: hidden;
    white-space: nowrap;
    width: max-content;
    border-right: 3px solid;
    width: 0;
    animation: typing-1 3s steps(20) infinite alternate-reverse;
}

@keyframes typing-1 {
    from {
        width: 0
    }

    to {
        width: 100%
    }
}

.video-right {
    animation: slideInFromRight 1.5s ease-out;
}

@keyframes slideInFromRight {
    0% {
        transform: translateX(500px);
        opacity: 0;
    }

    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes pulse {

    0%,
    100% {
        transform: scale(1);
        opacity: 1;
    }

    50% {
        transform: scale(1.05);
        opacity: 0.9;
    }
}

@keyframes float {
    0% {
        transform: translateY(0);
    }

    50% {
        transform: translateY(-10px);
    }

    100% {
        transform: translateY(0);
    }
}

.floating-pulse {
    animation: float 4s ease-in-out infinite, pulse 2.5s ease-in-out infinite;
}