import React from 'react'
import HomeUpcommingClasses from '@/components/HomeUpcommingClasses/HomeUpcommingClasses'
import ForwardArrow from '@/components/Icons/ForwardArrow'
import PlayHomeVideo from '@/components/PlayHomeVideo/PlayHomeVideo'
import { getTeachers } from '@/lib/actions/account.actions'
import FindClass from '@/components/FindClass/FindClass'
import {
  getClassesGenreFilters,
  getClassesLevelFilters,
  getClassesTypeFilters,
  getFeaturedClasses,
  getMeetings,
} from '@/lib/actions/class.actions'
import Image from 'next/image'
import Link from 'next/link'
import './HomeLandingPage.css'
import HomeDonationSection from '../HomeDonationSection/HomeDonationSection'
import { getBanners } from '@/lib/actions/banner.actions'
import BannerImage from '../BannerImage/BannerImage'
import FeaturedClassesCarousel from '../FeaturedClassesCarousel/FeaturedClassesCarousel'

const HomeLandingPage = async () => {
  const [
    genres,
    types,
    levels,
    teachers,
    bannerImage,
    meetings,
    featuredClassesResponse,
  ] = await Promise.all([
    getClassesGenreFilters(),
    getClassesTypeFilters(),
    getClassesLevelFilters(),
    getTeachers('', [], 100, 0),
    getBanners(),
    getMeetings(),
    getFeaturedClasses(),
  ])

  const teachersData = (teachers?.data?.data || []).map((teacher: any) => ({
    label: teacher?.Name,
    value: teacher?.Id,
  }))

  const featuredClasses = featuredClassesResponse?.data || []
  return (
    <main className="w-full max-w-[1800px] mx-auto">
      {/* Main Banner */}
      <section>
        <BannerImage bannerImage={bannerImage[0]?.imageUrl} />
      </section>

      <div>
        {/* Impact Section */}
        <section className="pb-16 lg:pb-28">
          <div className="px-6 sm:px-10 md:px-20 lg:px-32 xl:px-56 py-16 lg:py-28">
            <h1 className="font-bold text-lg sm:text-xl md:text-2xl tracking-[5px] md:tracking-[9px] reverse-string text-center lg:text-left">
              OUR IMPACT
            </h1>
            <div className="flex flex-col lg:flex-row items-start lg:items-end justify-between gap-6 lg:gap-10 mt-5">
              <h2 className="font-bold text-3xl sm:text-4xl md:text-5xl lg:text-6xl lg:w-7/12">
                3300+ Classes attended by over 10K students
              </h2>
              <Link href={'/classes'}>
                <button className="bg-color-yellow-1 text-base sm:text-lg lg:text-xl flex items-center justify-center gap-2 py-2 sm:py-3 px-4 sm:px-5 rounded-3xl w-max font-semibold">
                  Find a Class <ForwardArrow />
                </button>
              </Link>
            </div>
          </div>
          <FeaturedClassesCarousel featuredClasses={featuredClasses} />
        </section>

        {/* Find new Class */}
        <FindClass
          genres={genres}
          types={types}
          levels={levels}
          teachers={teachersData}
        />

        {/* Muse Writer Center */}
        <section className="flex flex-col lg:flex-row gap-6 sm:gap-8 md:gap-10 px-4 sm:px-6 md:px-10 lg:px-16 xl:px-24 py-10 sm:py-12 lg:py-16 xl:py-20 items-center justify-between">
          {/* Left Side (Videos & Images) */}
          <div className="w-full lg:w-1/2">
            {/* <div className="grid grid-cols-2 gap-2 xs:gap-3 sm:gap-4 md:gap-5 bg-home-gradient p-4 xs:p-6 sm:p-8 md:p-10 lg:p-12 xl:p-16 rounded-xl xs:rounded-2xl sm:rounded-3xl video-left">
              <div className="relative aspect-square overflow-hidden rounded-lg xs:rounded-xl sm:rounded-2xl">
                <Image
                  src={'/v1.jpg'}
                  alt="Muse Center 1"
                  fill
                  className="hover:scale-105 transition duration-300 object-cover w-full h-full"
                />
              </div>
              <div className="relative aspect-square overflow-hidden rounded-lg xs:rounded-xl sm:rounded-2xl">
                <div className="w-full h-full flex items-center justify-center">
                  <PlayHomeVideo />
                </div>
              </div>
              <div className="relative aspect-square overflow-hidden rounded-lg xs:rounded-xl sm:rounded-2xl">
                <Image
                  src={'/v3.jpg'}
                  alt="Muse Center 3"
                  fill
                  className="hover:scale-105 transition duration-300 object-cover w-full h-full"
                />
              </div>
              <div className="relative aspect-square overflow-hidden rounded-lg xs:rounded-xl sm:rounded-2xl">
                <Image
                  src={'/v4.jpg'}
                  alt="Muse Center 4"
                  fill
                  className="hover:scale-105 transition duration-300 object-cover w-full h-full"
                />
              </div>
            </div> */}
            <div className="grid grid-cols-2 gap-5 bg-home-gradient p-20 rounded-3xl video-left">
              <Image
                src={'/v1.jpg'}
                alt="Muse Center 1"
                width={400}
                height={400}
                className="rounded-3xl hover:scale-105 transition duration-300"
              />
              <PlayHomeVideo />
              <Image
                src={'/v3.jpg'}
                alt="Muse Center 1"
                width={400}
                height={400}
                className="rounded-3xl hover:scale-105 transition duration-300"
              />
              <Image
                src={'/v4.jpg'}
                alt="Muse Center 1"
                width={400}
                height={400}
                className="rounded-3xl hover:scale-105 transition duration-300"
              />
            </div>
          </div>

          {/* Right Side (Text Content) */}
          <div className="w-full lg:w-1/2 p-4 xs:p-6 sm:p-8 md:p-10 lg:p-12 xl:p-16 flex flex-col justify-center">
            <h5 className="text-brown-800 uppercase font-bold mb-2 text-xs xs:text-sm sm:text-base tracking-[4px] xs:tracking-[5px] md:tracking-[7px] lg:tracking-[9px] reverse-string-1 text-center lg:text-left">
              THE MUSE WRITERS CENTER...
            </h5>
            <h2 className="text-2xl xs:text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold mb-3 sm:mb-4 text-center lg:text-left">
              Who We Are
            </h2>
            <p className="text-color-grey-7 mb-3 sm:mb-4 text-sm xs:text-base sm:text-lg md:text-xl text-center lg:text-left">
              {`The Muse Writers Center promotes creative writing and literary arts in Hampton Roads, Virginia and beyond. We provide in-person, online, and hybrid classes in all writing genres for writers of all ages and experience levels.`}
            </p>
            <p className="text-color-grey-7 mb-3 sm:mb-4 text-sm xs:text-base sm:text-lg md:text-xl text-center lg:text-left">
              {`Our Norfolk center includes a library and writers' workspace. We organize literary events, readings, and open mics both locally and online.`}
            </p>
            <p className="text-color-grey-7 mb-4 sm:mb-6 text-sm xs:text-base sm:text-lg md:text-xl text-center lg:text-left">
              {`We ensure classes are accessible through financial aid and scholarships, and conduct outreach to youth, seniors, and military communities.`}
            </p>
            <a
              href="#"
              className="mx-auto lg:mx-0 inline-block bg-yellow-400 text-sm xs:text-base sm:text-lg md:text-xl font-semibold text-black py-2 xs:py-2.5 sm:py-3 px-4 xs:px-5 sm:px-6 rounded-full hover:bg-yellow-500 transition duration-300 w-max"
            >
              Learn More →
            </a>
          </div>
        </section>

        {/* Upcoming Classes */}
        <HomeUpcommingClasses data={meetings?.data?.data || []} />

        {/* Donation */}
        <HomeDonationSection />
      </div>
    </main>
  )
}

export default HomeLandingPage
