'use client'
import React from 'react'
import HomeHeader from '../HomeHeader/HomeHeader'
import SideHeader from '../SideHeader/SideHeader'
import { ROLES } from '@/types/enum'
import { usePathname } from 'next/navigation'
import { isNonLoginProtectedRoute } from '@/utils/utils'
import { URL_PATHS_NO_HEADER_FOOTER_SIDEBAR } from '@/utils/constants'

interface HeaderProps {
  userData?: any
  headerData: any
}

const Header = ({ userData, headerData }: HeaderProps) => {
  const pathname = usePathname()
  const currentRoute = '/' + pathname.split('/')[1]

  if (URL_PATHS_NO_HEADER_FOOTER_SIDEBAR.includes(currentRoute)) {
    return null // Do not render header/sidebar/footer for these paths
  }

  if (isNonLoginProtectedRoute(currentRoute))
    return (
      <HomeHeader
        headerData={headerData}
        role={userData?.role}
        userData={userData}
      />
    )
  switch (userData?.role) {
    case ROLES.Youth_Student:
    case ROLES.Adult_Student:
    case ROLES.Teacher:
    case ROLES.Parent:
      return <SideHeader />
    default:
      return (
        <HomeHeader
          headerData={headerData}
          role={userData?.role}
          userData={userData}
        />
      )
  }
}

export default Header
