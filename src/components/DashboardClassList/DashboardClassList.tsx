'use client'
import React, { useEffect, useState } from 'react'
import ClassCard from '../ClassCard/ClassCard'
import Link from 'next/link'
import { useApi } from '@/hooks/useApi'
import DashboardClassListShimmer from './DashboardClassListShimmer'
import { getTeacherClasses } from '@/lib/actions/teacher.actions'
import {
  selectTeacherClasses,
  setTeacherClasses,
  useDispatch,
  useSelector,
} from '@/lib/redux'

type DashboardClassListProps = {
  selfClasses?: boolean
}

const DashboardClassList = ({ selfClasses }: DashboardClassListProps) => {
  const [classesData, setClassesData] = useState<any[]>([])
  const cachedClasses = useSelector(selectTeacherClasses)
  const dispatch = useDispatch()

  const [classResponse, fetchClassData] = useApi(
    (
      accessToken: string,
      classType: 'Attendance Pending' | 'Active' | 'Previous',
    ) => getTeacherClasses(accessToken, classType),
    selfClasses,
  )

  useEffect(() => {
    if (cachedClasses?.length > 0) {
      setClassesData(cachedClasses)
    }
  }, [cachedClasses])

  useEffect(() => {
    if (!cachedClasses || cachedClasses.length === 0) {
      fetchClassData('Active')
    }
  }, [])

  useEffect(() => {
    if (classResponse.isSuccess) {
      const classData = classResponse?.data?.data?.data || []
      dispatch(setTeacherClasses(classData))
      setClassesData(classData)
    }
  }, [classResponse])

  return (
    <div className="w-full">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-medium leading-[1.5] tracking-normal align-middle text-color-black">
          My Classes
        </h2>
        <Link
          href="/my-classes/active"
          className="text-sm font-normal leading-[2] tracking-normal text-right text-color-grey-1"
        >
          View All
        </Link>
      </div>

      {cachedClasses?.length > 0 ? (
        <div className="grid md:grid-cols-2 gap-6">
          {cachedClasses?.slice(0, 2)?.map((item, i) => (
            <Link href={`/classes/${item?.Id}`} key={item?.Id}>
              <ClassCard
                data={item}
                showRating={false}
                showDetails={true}
                shadow={'sm'}
              />
            </Link>
          ))}
        </div>
      ) : classResponse.isFetching ? (
        <DashboardClassListShimmer />
      ) : classesData.length > 0 ? (
        <div className="grid md:grid-cols-2 gap-6">
          {classesData?.slice(0, 2)?.map((item, i) => (
            <Link href={`/classes/${item?.Id}`} key={item?.Id}>
              <ClassCard
                data={item}
                showRating={false}
                showDetails={true}
                shadow={'sm'}
              />
            </Link>
          ))}
        </div>
      ) : (
        <div className="flex items-center font-bold justify-center mb-10 text-color-grey-1">
          No classes to show
        </div>
      )}
    </div>
  )
}

export default DashboardClassList
