import './CustomToggleButton.css'
import React from 'react'

const CustomToggleButton = ({
  title,
  label1,
  label2,
  onClick,
}: {
  title: string
  label1: string
  label2: string
  onClick: any
}) => {
  return (
    <p>
      <label className="toggleSwitch" onClick={onClick}>
        <input type="checkbox" />
        <span className="text-sm font-medium">
          <span>{label1}</span>
          <span>{label2}</span>
          {title}
        </span>
        <a></a>
      </label>
    </p>
  )
}

export default CustomToggleButton
