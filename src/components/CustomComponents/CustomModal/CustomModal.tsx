'use client'
import React from 'react'
import CustomButton from '../CustomButton/CustomButton'

interface CustomModalProps {
  icon: any
  title: string
  desc?: string
  onClose?: () => void
  showBtn?: boolean
  showCancel?: boolean
  isLoading: boolean
  handleClick?: () => void
  btnText?: string
}

const CustomModal = ({
  icon,
  title,
  desc,
  onClose,
  showCancel = false,
  showBtn = true,
  isLoading,
  handleClick,
  btnText,
}: CustomModalProps) => {
  return (
    <div className="flex flex-col items-center gap-3 w-full">
      {icon}
      <h1 className="font-bold text-lg">{title}</h1>
      <h2 className="text-color-grey-1 text-sm mb-3">{desc}</h2>

      <div className="flex items-center justify-between gap-3 w-full">
        {showCancel && (
          <button
            className="w-full inline-flex items-center h-12 justify-center whitespace-nowrap disabled:pointer-events-none disabled:opacity-50  rounded-md text-sm focus-visible:outline-none focus-visible:ring-1 text-black focus-visible:ring-ring font-semibold shadow px-4 py-2"
            onClick={() => {
              onClose && onClose()
            }}
          >
            Cancel
          </button>
        )}
        {showBtn && (
          <CustomButton
            title={btnText || 'BUTTON_TEXT'}
            isLoading={isLoading}
            onClick={handleClick}
            height={12}
          />
        )}
      </div>
    </div>
  )
}

export default CustomModal
