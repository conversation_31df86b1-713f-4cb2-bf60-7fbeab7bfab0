import UpArrow from '@/components/Icons/UpArrow'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import React, { useEffect, useState } from 'react'

const CustomLinkDropDown = ({
  icon,
  title,
  options,
  isActive,
}: {
  icon: React.ReactNode
  title: string
  options: any[]
  isActive: boolean
}) => {
  const pathname = usePathname() // Get the current route
  const currentRoute = '/' + pathname.split('/')[2]
  const [showDropDown, setShowDropDown] = useState<boolean>(isActive)
  useEffect(() => {
    setShowDropDown(isActive)
  }, [isActive])
  return (
    <div className="flex items-start flex-col gap-2 w-full">
      <div
        onClick={() => setShowDropDown(!showDropDown)}
        className={`px-7 py-5 flex items-center justify-start gap-5 w-full rounded-xl ${
          isActive ? `bg-color-grey` : ``
        } hover:bg-color-grey`}
      >
        <div className="flex items-center gap-5 cursor-pointer">
          {icon}
          <span
            className={`text-sm ${isActive ? 'font-bold' : 'text-color-grey-1'}`}
          >
            {title}
          </span>
        </div>
        <div className="hidden md:block">
          <UpArrow showDownArrow={!showDropDown} isActive={isActive} />
        </div>
      </div>
      {showDropDown && (
        <div className="px-7">
          {options.map((item: any, index: number) => {
            const isActive =
              item?.link == pathname || item?.link.includes(currentRoute)
            return (
              <Link
                href={item?.link}
                key={item?.title + index}
                className={`text-xs font-medium text-color-grey-1 flex items-center gap-2 px-2 py-2 hover:bg-color-grey rounded-md cursor-pointer ${index == 0 ? `` : `mt-1`} ${isActive ? `bg-color-grey` : ``}`}
              >
                <div
                  className={`w-1 h-1 ${isActive ? 'bg-color-black' : 'bg-color-grey-2'} rounded-full`}
                ></div>
                <span
                  className={`${isActive ? 'font-bold text-color-black' : 'text-color-grey-1'}`}
                >
                  {item?.title}
                </span>
              </Link>
            )
          })}
        </div>
      )}
    </div>
  )
}

export default CustomLinkDropDown
