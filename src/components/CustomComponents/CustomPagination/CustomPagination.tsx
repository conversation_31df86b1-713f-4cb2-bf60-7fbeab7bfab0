'use client'
import <PERSON><PERSON><PERSON> from '@/components/Icons/LeftArrow'
import <PERSON>Arrow from '@/components/Icons/RightArrow'
import React, { useRef } from 'react'
import { useRouter } from 'next/navigation'

interface CustomPaginationProps {
  activePage: number
  total?: number
}

const CustomPagination = ({
  activePage,
  total = 200,
}: CustomPaginationProps) => {
  const router = useRouter()
  const totalPagesRef = useRef(Math.ceil(total / 10))

  if (totalPagesRef.current <= 1) {
    return null
  }

  const handlePrev = () => {
    if (activePage > 1) {
      const newPage = activePage - 1
      const query = new URLSearchParams(window.location.search)
      const tag = query.get('tag')
      const url = tag ? `?tag=${tag}&page=${newPage}` : `?page=${newPage}`
      router.push(url)
    }
  }

  const handleNext = () => {
    if (activePage < totalPagesRef.current) {
      const newPage = activePage + 1
      const query = new URLSearchParams(window.location.search)
      const tag = query.get('tag')
      const url = tag ? `?tag=${tag}&page=${newPage}` : `?page=${newPage}`
      router.push(url)
    }
  }

  const handlePage = (page: number) => {
    const query = new URLSearchParams(window.location.search)
    const tag = query.get('tag')
    const url = tag ? `?tag=${tag}&page=${page}` : `?page=${page}`
    router.push(url)
  }

  const totalPages = totalPagesRef.current
  const startIndex = Math.max(2, activePage - 2) // Dynamic start index
  const endIndex = Math.min(totalPages - 1, activePage + 2) // Dynamic end index

  return (
    <div className="w-full font-semibold flex justify-center items-center gap-5">
      <button
        onClick={handlePrev}
        className="flex items-center justify-center text-sm py-2 px-4 rounded-md border border-color-grey-2"
      >
        <LeftArrow dark={true} />
        Previous
      </button>

      {/* First page */}
      <button
        onClick={() => handlePage(1)}
        className={`w-10 h-10 text-sm p-2 rounded-md flex items-center justify-center border  ${activePage == 1 ? 'bg-color-yellow' : 'border-color-grey-2'}`}
      >
        {1}
      </button>

      {/* Ellipsis if needed before startIndex */}
      {startIndex > 2 && (
        <div className="w-10 h-10 text-sm p-2 rounded-md flex items-center justify-center border border-color-grey-2">
          ...
        </div>
      )}

      {/* Middle pages */}
      {Array.from(
        { length: endIndex - startIndex + 1 },
        (_, i) => startIndex + i,
      ).map((index) => (
        <button
          onClick={() => handlePage(index)}
          className={`w-10 h-10 text-sm p-2 rounded-md flex items-center justify-center border  ${activePage == index ? 'bg-color-yellow' : 'border-color-grey-2'}`}
          key={index}
        >
          {index}
        </button>
      ))}

      {/* Ellipsis if needed before the last page */}
      {endIndex < totalPages - 1 && (
        <div className="w-10 h-10 text-sm p-2 rounded-md flex items-center justify-center border border-color-grey-2">
          ...
        </div>
      )}

      {/* Last page */}
      <button
        onClick={() => handlePage(totalPages)}
        className={`w-10 h-10 text-sm p-2 rounded-md flex items-center justify-center border  ${activePage == totalPages ? 'bg-color-yellow' : 'border-color-grey-2'}`}
      >
        {totalPages}
      </button>

      <button
        onClick={handleNext}
        className="flex items-center justify-center text-sm py-2 px-4 rounded-md border border-color-grey-2"
      >
        Next
        <RightArrow dark={true} />
      </button>
    </div>
  )
}

export default CustomPagination
