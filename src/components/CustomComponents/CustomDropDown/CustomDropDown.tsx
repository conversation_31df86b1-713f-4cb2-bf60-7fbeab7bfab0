'use client'
import React, { useState, useEffect, ReactNode, useRef } from 'react'
import UpArrow from '@/components/Icons/UpArrow'
import { useIsMobile } from '@/hooks/useIsMobile'

interface CustomDropDownProps {
  icon: ReactNode
  title: string
  options: any[]
  handleOptionClick: any
  column?: number
  selectedList?: string[] | string
  isTitleType?: boolean
  width?: string
  height?: number
  text?: string
  padding?: number
}

const CustomDropDown = ({
  icon,
  title,
  options,
  handleOptionClick,
  column = 1,
  selectedList = '',
  isTitleType = false,
  width = 'w-max',
  height = 12,
  text = 'xs',
  padding = 5,
}: CustomDropDownProps) => {
  const [showDropDown, setShowDropDown] = useState<boolean>(false)
  const [timer, setTimer] = useState<NodeJS.Timeout>()
  const isMobile = useIsMobile()
  const dropdownRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (showDropDown) {
      !isMobile && hideUserBar()
    }
  }, [showDropDown])

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setShowDropDown(false)
      }
    }

    if (showDropDown) {
      document.addEventListener('mousedown', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showDropDown])

  const hideUserBar = (delay: number = 1500) => {
    clearTimeout(timer)
    const id = setTimeout(() => {
      setShowDropDown(false)
    }, delay)
    setTimer(id)
  }

  return (
    <div
      ref={dropdownRef}
      className={`flex items-center justify-between w-full relative gap-2 h-${height} p-${padding} rounded-xl border-color-grey-2 border cursor-pointer`}
      onClick={() => setShowDropDown(!showDropDown)}
    >
      <div className="flex items-center gap-2">
        {icon}
        <span className={`text-${text} font-semibold w-max`}>{title}</span>
      </div>
      {options && options.length > 0 && (
        <>
          <div className="block">
            <UpArrow showDownArrow={!showDropDown} />
          </div>
          {showDropDown && (
            <div
              className={`absolute ${width} text-sm font-medium bg-white flex flex-col items-start right-0 top-12 rounded-2xl p-5 shadow-2xl z-50`}
              onPointerEnter={() => {
                if (timer) clearInterval(timer)
              }}
              onPointerLeave={() => hideUserBar()}
            >
              <div className={`grid grid-cols-${column} ${width}`}>
                {options.map((item, index) => {
                  return (
                    <div
                      key={item?.value + index}
                      className={`w-full px-5 py-3 flex items-center hover:bg-color-grey ${
                        selectedList?.includes(item?.value) ||
                        selectedList == item.value
                          ? 'text-color-yellow'
                          : ''
                      } rounded-xl cursor-pointer`}
                      onClick={(e) => {
                        e.stopPropagation()
                        setShowDropDown(false)
                        handleOptionClick(
                          item.value,
                          isTitleType ? title : item.label,
                        )
                      }}
                      data-value={item?.value}
                    >
                      <span>{item?.label || item}</span>
                    </div>
                  )
                })}
              </div>
            </div>
          )}
        </>
      )}
    </div>
  )
}

export default CustomDropDown
