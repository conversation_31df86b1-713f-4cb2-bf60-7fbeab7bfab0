import { useState } from 'react'
import DatePicker from 'react-datepicker'
import InputMask from 'react-input-mask'
import 'react-datepicker/dist/react-datepicker.css'
import { format } from 'date-fns'
import { toast } from 'react-toastify'

function isValidFullDate(value: string): boolean {
  const [mm, dd, yyyy] = value.split('-')
  if (mm?.length !== 2 || dd?.length !== 2 || yyyy?.length !== 4) return false

  const month = parseInt(mm, 10)
  const day = parseInt(dd, 10)
  const year = parseInt(yyyy, 10)

  if (month < 1 || month > 12 || day < 1 || day > 31) return false

  const date = new Date(`${yyyy}-${mm}-${dd}`)
  return (
    date instanceof Date &&
    !isNaN(date.getTime()) &&
    date.getFullYear() === year &&
    date.getMonth() + 1 === month &&
    date.getDate() === day
  )
}

interface CustomDatePickerProps {
  title: string
  setDateOfBirth: (date: string) => void
  dateOfBirth?: string
}

const formatDateOfBirth = (value?: string): string => {
  if (!value) return ''
  try {
    const date = new Date(`${value}T00:00:00`)
    if (isNaN(date.getTime())) return ''
    return format(date, 'MM-dd-yyyy')
  } catch {
    return ''
  }
}

export default function CustomDatePicker({
  title,
  setDateOfBirth,
  dateOfBirth,
}: CustomDatePickerProps) {
  const [inputValue, setInputValue] = useState(formatDateOfBirth(dateOfBirth))
  const [selectedDate, setSelectedDate] = useState<Date | null>(() => {
    if (!dateOfBirth) return null
    const date = new Date(dateOfBirth)
    return isNaN(date.getTime()) ? null : date
  })

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const val = e.target.value
    setInputValue(val)

    if (val.length === 10) {
      if (!isValidFullDate(val)) {
        toast.error('Invalid date! Please check MM-DD-YYYY.', {
          autoClose: 2000,
        })
        setSelectedDate(null)
        setDateOfBirth('')
        return
      }

      const [mm, dd, yyyy] = val.split('-')
      const date = new Date(`${yyyy}-${mm}-${dd}`)
      if (!isNaN(date.getTime())) {
        setSelectedDate(date)
        setDateOfBirth(format(date, 'dd-MM-yyyy'))
      }
    } else {
      setSelectedDate(null)
      setDateOfBirth('')
    }
  }

  const handleDateChange = (date: Date | null) => {
    if (!date || isNaN(date.getTime())) {
      setSelectedDate(null)
      setInputValue('')
      setDateOfBirth('')
      return
    }

    try {
      const formatted = format(date, 'MM-dd-yyyy')
      const dobFormatted = format(date, 'yyyy-MM-dd')
      setSelectedDate(date)
      setInputValue(formatted)
      setDateOfBirth(dobFormatted)
    } catch (err) {
      toast.error('Invalid date format!', { autoClose: 2000 })
    }
  }

  return (
    <div className="space-y-1">
      <label htmlFor="birthday" className="block text-sm font-medium">
        {title}
      </label>
      <DatePicker
        selected={selectedDate}
        onChange={handleDateChange}
        dateFormat="MM-dd-yyyy"
        showMonthDropdown
        showYearDropdown
        dropdownMode="select"
        customInput={
          <InputMask
            mask="99-99-9999"
            value={inputValue}
            onChange={handleInputChange}
            maskChar={null}
            placeholder="MM-DD-YYYY"
          >
            {(inputProps: any) => (
              <input
                {...inputProps}
                className="text-sm w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-300"
                required
              />
            )}
          </InputMask>
        }
      />
    </div>
  )
}
