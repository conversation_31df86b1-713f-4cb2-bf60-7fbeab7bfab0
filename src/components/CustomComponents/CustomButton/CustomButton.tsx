import React, { memo } from 'react'

const CustomButton = ({
  title,
  isLoading,
  onClick,
  height = 10,
  isVerified = true,
  isDisabled = false,
  backgroundColor = 'bg-color-yellow',
  width = 'w-full',
  textColor = 'text-black',
  classes = '',
  icon,
  border = '',
  type = 'button',
}: {
  title: string
  isLoading: boolean
  onClick: any
  height?: number
  isVerified?: boolean
  isDisabled?: boolean
  backgroundColor?: string
  width?: string
  textColor?: string
  classes?: string
  icon?: React.ReactNode
  border?: string
  type?: 'button' | 'submit' | 'reset'
}) => {
  return (
    <button
      onClick={onClick}
      type={type}
      disabled={!isVerified || isDisabled}
      className={`${width} inline-flex items-center ${backgroundColor} ${border} justify-center disabled:pointer-events-none disabled:opacity-50 rounded-md text-sm ${textColor} font-semibold shadow h-${height} px-4 py-2 ${
        isLoading || isDisabled ? 'opacity-50 cursor-not-allowed' : ''
      } ${classes}`}
    >
      {isLoading ? (
        <div className="loader-spinner" role="status"></div>
      ) : (
        <div className="flex items-center gap-2">
          <span>{title}</span>
          {icon && <span className="flex-shrink-0">{icon}</span>}
        </div>
      )}
    </button>
  )
}

export default memo(CustomButton)
