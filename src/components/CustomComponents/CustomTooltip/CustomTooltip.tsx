import { ReactNode } from 'react'

interface CustomTooltipProps {
  children: ReactNode
  text: string
  isVisible: boolean
}

const CustomTooltip = ({ children, text, isVisible }: CustomTooltipProps) => {
  return (
    <div className="relative inline-block group w-full h-full">
      {children}
      {isVisible && (
        <div className="absolute z-10 invisible opacity-0 group-hover:visible group-hover:opacity-100 transition-opacity duration-300 bg-gray-800 text-white text-xs rounded-lg py-2 px-3 -top-12 left-1/2 -translate-x-1/2 w-max flex items-center justify-center">
          {text}
          <div className="absolute -bottom-2 left-1/2 -translate-x-1/2 w-0 h-0 border-l-8 border-r-8 border-t-8 border-l-transparent border-r-transparent border-t-gray-800" />
        </div>
      )}
    </div>
  )
}

export default CustomTooltip
