'use client'
import React from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { Swiper, SwiperSlide } from 'swiper/react'
import { Autoplay } from 'swiper/modules'

// Import Swiper styles
import 'swiper/css'

interface FeaturedClass {
  class_id: string
  image: string
}

interface FeaturedClassesProps {
  featuredClasses: FeaturedClass[]
}

const FeaturedClassesCarousel = ({ featuredClasses }: FeaturedClassesProps) => {
  return (
    featuredClasses?.length > 0 && (
      <div className="w-full flex items-center justify-center">
        <div className="relative w-[calc(100vw-20px)] h-full">
          {/* Left cloud effect */}
          <div className="absolute top-0 left-0 w-[100px] h-full z-10 bg-gradient-to-r from-white via-white/50 to-transparent pointer-events-none" />

          {/* Right cloud effect */}
          <div className="absolute top-0 right-0 w-[100px] h-full z-10 bg-gradient-to-l from-white via-white/50 to-transparent pointer-events-none" />

          <Swiper
            modules={[Autoplay]}
            loop={true} // Enable infinite loop
            slidesPerView="auto" // Auto-adjust slides based on width
            spaceBetween={40} // Gap between slides
            centeredSlides={featuredClasses.length <= 3} // Center slides for 3 or fewer
            autoplay={{
              delay: 0, // Continuous scroll
              disableOnInteraction: false,
            }}
            speed={2000} // Adjust speed (lower is faster)
            className="w-full"
            style={{ overflow: 'hidden' }}
          >
            {featuredClasses.map((item: FeaturedClass, index: number) => {
              return (
                <div key={`${item?.class_id}-${index}`}>
                  {item?.image && (
                    <SwiperSlide style={{ width: '316px' }}>
                      <Link
                        href={`/classes/${item?.class_id}`}
                        className="flex-shrink-0"
                      >
                        <Image
                          src={item?.image}
                          alt={`Class ${index}`}
                          width={316}
                          height={331}
                          className="bg-center h-80 rounded-xl border-2 shadow-sm"
                          style={{ objectFit: 'cover', width: '100%' }}
                        />
                      </Link>
                    </SwiperSlide>
                  )}
                </div>
              )
            })}
          </Swiper>
        </div>
      </div>
    )
  )
}

export default FeaturedClassesCarousel
