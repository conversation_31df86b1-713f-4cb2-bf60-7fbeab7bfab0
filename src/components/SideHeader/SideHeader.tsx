'use client'
import React, { useEffect, useMemo } from 'react'
import LogoIcon from '@/assets/icons/LogoIcon'
import Link from 'next/link'
import Cross from '../Icons/Cross'
import { useSelector, useDispatch } from 'react-redux'
import {
  setSideHeaderVisiblity,
  sideHeaderVisiblity,
} from '@/lib/redux/slices/metaSlice'
import { useIsMobile } from '@/hooks/useIsMobile'
import { usePathname } from 'next/navigation'
import CustomLinkDropDown from '../CustomComponents/CustomLinkDropDown/CustomLinkDropDown'
import {
  getStudentNavigation,
  ICONS,
  PARENT_SIDE_NAVBAR,
  TEACHER_SIDE_NAVBAR,
} from '@/utils/constants'
import { SideNavItem } from '@/utils/interface'
import { ROLES } from '@/types/enum'
import { isHeaderFooterSidebarPath } from '@/utils/utils'
import { useAuth } from '@/contexts/AuthContext'
import Image from 'next/image'

const SideHeader = () => {
  const dispatch = useDispatch()
  const pathname = usePathname()
  const isMobile = useIsMobile()
  const currentRoute = '/' + pathname.split('/')[1]
  const [showSideHeader, setShowSideHeader] = React.useState<boolean>(
    !isHeaderFooterSidebarPath(currentRoute) && currentRoute !== '/',
  )
  const { user } = useAuth()
  const isSideHeaderVisible = useSelector(sideHeaderVisiblity)

  useEffect(() => {
    if (!isMobile) {
      dispatch(setSideHeaderVisiblity(false))
    }
  }, [isMobile, dispatch])

  useEffect(() => {
    setShowSideHeader(
      !isHeaderFooterSidebarPath(currentRoute) && currentRoute !== '/',
    )
  }, [currentRoute])

  /* Removed conflicting useEffect manipulating body overflow */
  // useEffect(() => {
  //   if (isSideHeaderVisible) {
  //     document.body.style.overflow = 'hidden'
  //   } else {
  //     document.body.style.overflow = 'auto'
  //   }
  //   return () => {
  //     document.body.style.overflow = 'auto'
  //   }
  // }, [isSideHeaderVisible])

  const navigationSideNavbar = useMemo(() => {
    if (user?.role === ROLES.Teacher) {
      return TEACHER_SIDE_NAVBAR
    } else if (user?.role === ROLES.Parent) {
      return PARENT_SIDE_NAVBAR // Define this accordingly
    } else {
      return getStudentNavigation(user?.role)
    }
  }, [user])

  return (
    <>
      {showSideHeader && (
        <div
          className="px-5 py-10 w-64 flex-col items-end justify-between  bg-white hidden lg:flex"
          style={
            isSideHeaderVisible
              ? {
                  display: 'flex',
                  position: 'absolute',
                  zIndex: '1000',
                  width: '100%',
                }
              : {}
          }
        >
          <div className="w-full">
            <div className="flex items-center justify-between">
              <Link
                href="/"
                onClick={() => {
                  dispatch(setSideHeaderVisiblity(false))
                }}
              >
                <LogoIcon width={160} height={68} />
              </Link>
              {isSideHeaderVisible && <Cross />}
            </div>
            <div
              className="flex flex-col gap-1 mt-12 max-h-[calc(100% - 100px)] overflow-y-auto no-scrollbar"
              onClick={() =>
                isMobile && dispatch(setSideHeaderVisiblity(false))
              }
            >
              {navigationSideNavbar.map((item: SideNavItem) => {
                const IconComponent = ICONS[item.icon]
                const isActive =
                  currentRoute === item.link ||
                  String(item.link).indexOf(currentRoute) > -1
                return (
                  <div key={item.link}>
                    {item.subItems ? (
                      <CustomLinkDropDown
                        icon={<IconComponent isActive={isActive} />}
                        title={item.title}
                        options={item.subItems || []}
                        isActive={isActive}
                      />
                    ) : (
                      <Link
                        href={item.link}
                        onClick={() => {
                          // if (isMobile) {
                          dispatch(setSideHeaderVisiblity(false))
                          // }
                        }}
                      >
                        <div
                          className={`px-7 py-5 flex items-center justify-start gap-5 rounded-xl ${
                            isActive ? `bg-color-grey` : ``
                          } hover:bg-color-grey`}
                        >
                          <IconComponent isActive={isActive} />
                          <span
                            className={`text-sm ${isActive ? 'font-bold' : 'text-color-grey-1'}`}
                          >
                            {item.title}
                          </span>
                        </div>
                      </Link>
                    )}
                  </div>
                )
              })}
            </div>
          </div>
          {user &&
            [ROLES.Teacher, ROLES.Parent, ROLES.Adult_Student].includes(
              user?.role,
            ) && (
              <div className="px-7 py-5 w-full">
                <Link
                  href="/donations"
                  onClick={() => {
                    dispatch(setSideHeaderVisiblity(false))
                  }}
                >
                  <button className="w-full rounded-full bg-black hover:bg-black/80 text-white h-12 text-center py-3 font-semibold">
                    Donate
                  </button>
                </Link>
              </div>
            )}
        </div>
      )}
    </>
  )
}

export default SideHeader
