'use client'

import { setSideHeaderVisiblity, useDispatch } from '@/lib/redux'
import Hamburger from '../Icons/Hamburger'

const ClientHeaderActions = () => {
  const dispatch = useDispatch()

  const handleMenuToggle = () => {
    dispatch(setSideHeaderVisiblity(true))
  }

  return (
    <button className="block lg:hidden" onClick={handleMenuToggle}>
      <Hamburger />
    </button>
  )
}

export default ClientHeaderActions
