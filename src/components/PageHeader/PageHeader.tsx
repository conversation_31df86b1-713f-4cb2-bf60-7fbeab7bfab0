import React from 'react'
import Search from '../Icons/Search'
import Message from '../Icons/Message'
import RedDot from '../RedDot/RedDot'
import UserProfile from '../UserProfile/UserProfile'
import Link from 'next/link'
import CartIcon from '../Icons/CartIcon'
import Notification from '../Notification/Notification'
import HeaderIcon from '../Icons/HeaderIcon'
import ClientHeaderActions from './ClientHeaderActions'

interface PageHeaderProps {
  heading?: string
  showTittle?: boolean
  showSearch?: boolean
  showPadding?: string
  showMessage?: boolean
  backgroundColor?: string
  showCart?: boolean
  userData?: any
}

const PageHeader = ({
  heading,
  showTittle = true,
  showSearch = true,
  showPadding = '28px',
  showMessage = true,
  backgroundColor,
  showCart = true,
  userData,
}: PageHeaderProps) => {
  return (
    <>
      <div
        className={`flex gap-3 items-center w-full`}
        style={{
          padding: showPadding,
          backgroundColor: backgroundColor ? backgroundColor : 'white',
        }}
      >
        <ClientHeaderActions />
        <div className="flex justify-between items-center gap-1 w-full">
          <div className="flex-col gap-3 w-fit">
            {/* Hamburger visible on small screens only */}
            <h1 className="text-xl sm:text-2xl md:text-3xl font-medium hidden lg:block">
              {heading}
            </h1>
            {showTittle && (
              <span className="text-sm sm:text-base text-gray-500 hidden lg:block">
                Welcome back, nice to see you again!
              </span>
            )}
          </div>
          <div className="flex justify-center items-center gap-10">
            {showSearch && <Search />}
            {showCart && (
              <Link href={'/cart'}>
                <CartIcon />
              </Link>
            )}
            {showMessage && (
              <div className="relative">
                <Message />
                <RedDot />
              </div>
            )}
            <Notification />
            <div className="h-12 w-12">
              <UserProfile userData={userData} />
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

export default PageHeader
