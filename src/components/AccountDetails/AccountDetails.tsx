'use client'
import React, { useState, ChangeEvent } from 'react'
import CustomButton from '../CustomComponents/CustomButton/CustomButton'

interface FormData {
  firstName: string
  lastName: string
  username: string
  email: string
  displayName: string
  password: string
  repeatPassword: string
  hasAllergies: boolean
  allergyInformation: string
  disabilityStatus: string
  veteranStatus: string
  raceEthnicity: string
  customRaceEthnicity: string
  gender: string
  customGender: string
  hearAboutUs: string
}

const AccountDetails: React.FC = () => {
  const [formData, setFormData] = useState<FormData>({
    firstName: '',
    lastName: '',
    username: '',
    email: '',
    displayName: '',
    password: '',
    repeatPassword: '',
    hasAllergies: false,
    allergyInformation: '',
    disabilityStatus: 'none',
    veteranStatus: 'non-military',
    raceEthnicity: '',
    customRaceEthnicity: '',
    gender: '',
    customGender: '',
    hearAboutUs: '',
  })

  const handleInputChange = (
    e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    const { name, value, type } = e.target
    const checked = (e.target as HTMLInputElement).checked

    setFormData((prev) => ({
      ...prev,
      [name]: type === 'radio' || type === 'checkbox' ? checked : value,
    }))
  }

  const handleSubmit = () => {}

  return (
    <>
      <div className="">
        {/* Required Information Section */}
        <div className="max-w-2xl bg-white rounded-2xl mx-auto p-6 space-y-6">
          <div>
            <label className="text-sm font-bold">
              First Name{' '}
              <span className="text-xs font-medium text-color-grey-1">
                (Required)
              </span>
            </label>
            <input
              type="text"
              name="firstName"
              value={formData.firstName}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
            />
          </div>

          <div>
            <label className="text-sm font-bold">
              Last Name{' '}
              <span className="text-xs font-medium text-color-grey-1">
                (Required)
              </span>
            </label>
            <input
              type="text"
              name="lastName"
              value={formData.lastName}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
            />
          </div>

          <div>
            <label className="text-sm font-bold">
              Email{' '}
              <span className="text-xs font-medium text-color-grey-1">
                (Required)
              </span>
            </label>
            <input
              type="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
            />
          </div>

          <div>
            <label className="text-sm font-bold">
              Display name publicly as
            </label>
            <select
              name="displayName"
              value={formData.displayName}
              onChange={(e) =>
                setFormData((prev) => ({
                  ...prev,
                  displayName: e.target.value,
                }))
              }
              className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
            >
              <option value="">Select display name</option>
              <option value="username">Username</option>
              <option value="fullname">Full Name</option>
            </select>
          </div>

          <div>
            <label className="text-sm font-bold">
              Do you have any allergies we should know about?
            </label>
            <div className="flex items-center gap-4">
              <label className="flex items-center gap-2 text-sm font-medium mt-1">
                <input
                  type="radio"
                  name="hasAllergies"
                  checked={formData.hasAllergies}
                  onChange={handleInputChange}
                  className="w-4 h-4"
                />
                Yes
              </label>
              <label className="flex items-center gap-2 text-sm font-medium mt-1">
                <input
                  type="radio"
                  name="hasAllergies"
                  checked={!formData.hasAllergies}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      hasAllergies: !e.target.checked,
                    }))
                  }
                  className="w-4 h-4"
                />
                No
              </label>
            </div>
          </div>
          <div>
            <label className="text-sm font-bold">Allergy Information</label>
            <textarea
              name="allergyInformation"
              value={formData.allergyInformation}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
              rows={3}
            />
          </div>
        </div>

        <div className="max-w-2xl space-y-6 mt-8">
          <h2 className="mb-1 font-bold text-lg">Optional Information</h2>
          <p className="text-sm text-gray-600 mb-6">
            Because we are a nonprofit, we report statistics to our grantors.
            Your personal information is never shared, but it would be a great
            help if you would fill out this optional information, especially if
            you are taking a class.
          </p>
        </div>
        <div className="max-w-2xl bg-white rounded-2xl mx-auto p-6 space-y-6 mt-8">
          {/* Disability Status */}
          <div className="space-y-2 mb-6">
            <h3 className="font-bold text-sm">Disability Status</h3>
            {[
              'none',
              'needs-accommodation',
              'no-accommodation',
              'prefer-not-answer',
            ].map((status) => (
              <label
                key={status}
                className="flex items-center gap-2 text-sm font-medium"
              >
                <input
                  type="radio"
                  name="disabilityStatus"
                  value={status}
                  checked={formData.disabilityStatus === status}
                  onChange={handleInputChange}
                  className="w-4 h-4"
                />
                <span className="text-sm">
                  {status === 'none' && 'I do not have a disability'}
                  {status === 'needs-accommodation' &&
                    'I have a disability that needs accommodation'}
                  {status === 'no-accommodation' &&
                    'I have a disability that does not require accommodation'}
                  {status === 'prefer-not-answer' && 'Prefer not to answer'}
                </span>
              </label>
            ))}
          </div>

          {/* Veteran Status */}
          <div className="space-y-2 mb-6">
            <h3 className="font-bold text-sm">Veteran Status</h3>
            <div className="grid grid-cols-2 gap-4">
              {[
                { value: 'non-military', label: 'Non-military' },
                { value: 'veteran', label: 'Veteran' },
                { value: 'active-duty', label: 'Active Duty' },
                { value: 'family-member', label: 'Family Member' },
                { value: 'caregiver', label: 'Caregiver' },
                { value: 'prefer-not-answer', label: 'Prefer not to answer' },
              ].map(({ value, label }) => (
                <label
                  key={value}
                  className="flex items-center gap-2 text-sm font-medium"
                >
                  <input
                    type="radio"
                    name="veteranStatus"
                    value={value}
                    checked={formData.veteranStatus === value}
                    onChange={handleInputChange}
                    className="w-4 h-4"
                  />
                  <span className="text-sm">{label}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Race/Ethnicity */}
          <div className="space-y-2 mb-6">
            <h3 className="font-bold text-sm">Race/Ethnicity</h3>
            {[
              'American Indian or Alaska Native',
              'Asian',
              'Black or African American',
              'Latino',
              'Native Hawaiian or Other Pacific Islander',
              'White',
              'A race/ethnicity not identified above',
              'Prefer not to answer',
            ].map((race) => (
              <label
                key={race}
                className="flex items-center gap-2 text-sm font-medium"
              >
                <input
                  type="radio"
                  name="raceEthnicity"
                  value={race}
                  checked={formData.raceEthnicity === race}
                  onChange={handleInputChange}
                  className="w-4 h-4"
                />
                <span className="text-sm">{race}</span>
              </label>
            ))}
          </div>

          {formData.raceEthnicity ===
            'A race/ethnicity not identified above' && (
            <div className="mb-6">
              <label className="text-sm font-bold">
                Please describe your race or ethnicity
              </label>
              <textarea
                name="customRaceEthnicity"
                value={formData.customRaceEthnicity}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                rows={3}
              />
            </div>
          )}

          {/* Gender */}
          <div className="space-y-2 mb-6">
            <h3 className="font-bold text-sm">Gender</h3>
            {[
              'Male',
              'Female',
              'NonBinary',
              'Other',
              'Prefer not to answer',
            ].map((gender) => (
              <label
                key={gender}
                className="flex items-center gap-2 text-sm font-medium"
              >
                <input
                  type="radio"
                  name="gender"
                  value={gender}
                  checked={formData.gender === gender}
                  onChange={handleInputChange}
                  className="w-4 h-4"
                />
                <span className="text-sm">{gender}</span>
              </label>
            ))}
          </div>

          {formData.gender === 'Other' && (
            <div className="mb-6">
              <label className="text-sm font-bold">
                Please describe your gender identity
              </label>
              <textarea
                name="customGender"
                value={formData.customGender}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                rows={3}
              />
            </div>
          )}

          {/* How did you hear about us */}
          <div className="mb-6">
            <label className="text-sm font-bold">How did you hear of us?</label>
            <textarea
              name="hearAboutUs"
              value={formData.hearAboutUs}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
              rows={3}
            />
          </div>
        </div>
        <div className="mt-6">
          <CustomButton
            title={'Update Account Details'}
            onClick={handleSubmit}
            height={65}
            isLoading={false}
          />
        </div>
      </div>
    </>
  )
}

export default AccountDetails
