'use client'
import { useAuth } from '@/contexts/AuthContext'
import { ROLES } from '@/types/enum'
import { useState } from 'react'
import PreviousClasses from '../PreviousClasses/PreviousClasses'
import { TagSection } from '../TagSection/TagSection'
import UpcomingClasses from '../UpcomingClasses/UpcomingClasses'
import AccountDeactivatedInfo from './AccountDeactivatedInfo'
import ActionCard from './ActionCard'
import ProfileWithStats from './ProfileWithStats'
import YouthInfo from './YouthInfo'

interface YouthDetailProps {
  youthDetail: any
  youthMeetings: any
  previousClasses: any
}

const YouthDetail: React.FC<YouthDetailProps> = ({
  youthDetail,
  youthMeetings,
  previousClasses,
}) => {
  const { user } = useAuth()
  const favoriteAuthorsArray =
    youthDetail?.data?.Favorite_Authors__c?.split(';')
  const favoriteGenresArray = youthDetail?.data?.Favorite_Genres__c?.split(';')
  const [loginAllowed, setLoginAllowed] = useState<boolean>(
    youthDetail?.data?.Login_Allowed__c,
  )
  return (
    <div className="w-full flex flex-col items-center justify-center">
      {!loginAllowed && (
        <div className="p-6 w-full">
          <AccountDeactivatedInfo name={youthDetail?.data?.Name} />
        </div>
      )}
      <div className={`w-full max-w-6xl p-6 flex flex-col xl:flex-row gap-10`}>
        <div
          className={`${!loginAllowed ? 'opacity-40 pointer-events-none' : ''} flex-1 flex flex-col items-center md:items-start gap-6`}
        >
          <ProfileWithStats youthDetail={youthDetail} />
          <YouthInfo youthDetail={youthDetail} role={user?.role} />
        </div>
        <div className="xl:w-[568px] flex flex-col gap-8">
          {user?.role == ROLES.Parent && (
            <ActionCard
              youthId={youthDetail?.data?.Id}
              loginAllowed={loginAllowed}
              setLoginAllowed={setLoginAllowed}
            />
          )}
          <div
            className={`${!loginAllowed ? 'opacity-40 pointer-events-none' : ''} flex flex-col gap-8`}
          >
            <UpcomingClasses
              userMeetings={youthMeetings}
              user={youthDetail}
              title={'Upcoming Classes'}
              description={`Check out upcoming classes for ${youthDetail?.data?.Name}`}
            />
            <TagSection title="Favorite Authors" items={favoriteAuthorsArray} />
            <TagSection title="Favorite Genres" items={favoriteGenresArray} />
          </div>
        </div>
      </div>
      <div
        className={`${!loginAllowed ? 'opacity-40 pointer-events-none' : ''} p-6 w-full max-w-6xl`}
      >
        <PreviousClasses
          previousClasses={previousClasses}
          user={youthDetail}
          title={'Completed Classes'}
          description={`Explore ${youthDetail?.data?.Name} previous classes`}
          showViewAll={false}
        />
      </div>
    </div>
  )
}

export default YouthDetail
