import Image from 'next/image'
import BookOpenBlue from '../Icons/BookOpenBlue'
import BookClosedIcon from '../Icons/BookClosedIcon'
import StatsCard from '../StatsCard/StatsCard'
import BadgesIcon from '../Icons/BadgesIcon'
import { getNameLetter } from '@/utils/utils'

interface ProfileWithStatsProps {
  youthDetail: any
}

const ProfileWithStats: React.FC<ProfileWithStatsProps> = ({ youthDetail }) => {
  return (
    <div className="flex flex-col md:flex-row items-center gap-6">
      <div className="w-[249px] h-[249px] relative">
        {youthDetail?.data?.Photo__c ? (
          <Image
            src={youthDetail.data.Photo__c}
            alt="Profile Picture"
            fill
            className="rounded-full object-cover shadow"
          />
        ) : (
          <div className="rounded-full bg-gray-300 text-white flex items-center justify-center font-bold text-base w-full h-full">
            <span className="text-5xl font-bold">
              {getNameLetter(youthDetail?.data?.Name)}
            </span>
          </div>
        )}
      </div>

      <div className="flex flex-col gap-4">
        <StatsCard
          icon={<BookOpenBlue />}
          count={youthDetail?.data?.stats?.ongoingClasses || 0}
          label="Ongoing Classes"
          iconBg="bg-color-blue-7"
        />
        <StatsCard
          icon={<BookClosedIcon />}
          count={youthDetail?.data?.stats?.classesAttended || 0}
          label="Classes Taken"
          iconBg="bg-color-green-5"
        />
        <StatsCard
          icon={<BadgesIcon />}
          count={youthDetail?.data?.stats?.badgesEarned || 0}
          label="Badges"
          iconBg="bg-color-orange-2"
        />
      </div>
    </div>
  )
}

export default ProfileWithStats
