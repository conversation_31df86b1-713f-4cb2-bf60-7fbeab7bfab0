'use client'
import useModal from '@/hooks/useModal'
import CustomButton from '../CustomComponents/CustomButton/CustomButton'
import DeactivateModal from './DeactivateModal'
import { useRouter } from 'next/navigation'
import { useUpdateYouthAccountMutation } from '@/lib/redux/slices/apiSlice/apiSlice'
import { FormEvent, useEffect } from 'react'
import { toast } from 'react-toastify'

interface ActionCardProps {
  youthId: string
  loginAllowed: boolean
  setLoginAllowed: any
}

const ActionCard: React.FC<ActionCardProps> = ({
  youthId,
  loginAllowed,
  setLoginAllowed,
}) => {
  const [modal, showModal] = useModal()
  const handleModal = () => {
    showModal({
      title: ``,
      contentFn: (onClose) => (
        <DeactivateModal
          onClose={onClose}
          youthId={youthId}
          setLoginAllowed={setLoginAllowed}
        />
      ),
      closeOnClickOutside: true,
      size: 'lg',
      rounded: '2xl',
      padding: 0,
      showCloseButton: false,
    })
  }
  const router = useRouter()
  const [updateYouthAccount, { isLoading, isSuccess }] =
    useUpdateYouthAccountMutation()

  useEffect(() => {
    if (isSuccess) {
      setLoginAllowed((prev: boolean) => !prev)
      toast.success('Profile activated successfully')
    }
  }, [isSuccess])

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    const updateData = {
      loginAllowed: true,
    }
    const res = await updateYouthAccount({
      isTokenRequired: true,
      youthId: youthId,
      data: updateData,
    })
  }

  return (
    <>
      <div className="flex gap-3">
        <CustomButton
          title={loginAllowed ? 'Deactivate' : 'Activate'}
          isLoading={(!loginAllowed && isLoading) || false}
          isDisabled={false}
          onClick={(e: any) => {
            if (loginAllowed) {
              handleModal() // Show modal if already allowed
            } else {
              handleSubmit(e) // Trigger activation
            }
          }}
          height={12}
          backgroundColor={
            loginAllowed ? 'bg-neutral-white' : 'bg-color-yellow'
          }
          border="border border-color-grey-2"
        />

        <CustomButton
          title={'Edit Account'}
          isLoading={false}
          isDisabled={false}
          onClick={() => router.push(`/my-kids/${youthId}/edit`)}
          height={12}
          backgroundColor={
            !loginAllowed ? 'bg-neutral-white' : 'bg-color-yellow'
          }
        />
      </div>
      {modal}
    </>
  )
}

export default ActionCard
