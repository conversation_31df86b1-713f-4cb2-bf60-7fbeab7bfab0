'use client'
import useModal from '@/hooks/useModal'
import { ROLES } from '@/types/enum'
import { formatDate } from '@/utils/utils'
import { ReactNode } from 'react'
import AddMoneyModal from '../AddMoneyModal/AddMoneyModal'

interface YouthInfoProps {
  youthDetail: any
  role?: string
}

const YouthInfo: React.FC<YouthInfoProps> = ({ youthDetail, role = '' }) => {
  const [modal, showModal] = useModal()
  const handleModal = (availableBalance: number) => {
    showModal({
      title: ``,
      contentFn: (onClose) => (
        <AddMoneyModal
          onClose={onClose}
          availableBalance={availableBalance}
          youthId={youthDetail?.data?.Id}
        />
      ),
      closeOnClickOutside: true,
      size: 'lg',
      rounded: '2xl',
      padding: 0,
      showCloseButton: false,
    })
  }

  return (
    <div className="p-6 space-y-6 text-sm w-full">
      <h1 className="font-bold text-2xl tracking-normal text-color-black">
        {youthDetail?.data?.Name}
      </h1>
      <div className="space-y-2">
        <InfoRow
          label="Birthdate"
          value={formatDate(youthDetail?.data?.PersonBirthdate)}
        />
        <InfoRow label="Email" value={youthDetail?.data?.PersonEmail} />
        <InfoRow label="Phone" value={youthDetail?.data?.Phone} />
        <InfoRow
          label="Academic Level"
          value={youthDetail?.Academic_Level__c}
        />
        <InfoRow
          label="High School"
          value={youthDetail?.data?.High_School__c}
        />
      </div>

      {role == ROLES.Parent && (
        <div className="space-y-2">
          <h2 className="font-semibold">Payment Details</h2>
          <InfoRow
            label="Wallet Balance"
            value={
              <div className="flex gap-6">
                <span className="font-medium">
                  ${youthDetail?.data?.Wallet_Balance__c}
                </span>
                <div
                  className="text-color-blue-1 cursor-pointer"
                  onClick={() =>
                    handleModal(youthDetail?.data?.Wallet_Balance__c)
                  }
                >
                  +Add funds to wallet
                </div>
              </div>
            }
          />
          <InfoRow label="Self-Payment" value="Allowed" />
        </div>
      )}
      <div className="space-y-2">
        <h2 className="font-semibold">Other Details</h2>
        <InfoRow
          label="Allergies"
          value={youthDetail?.data?.Allergies__c ? 'Yes' : 'NA'}
        />
        <InfoRow
          label="Allergy Info"
          value={youthDetail?.data?.Allergy_Info__c || 'NA'}
        />
        <InfoRow
          label="Disability Status"
          value={youthDetail?.data?.Disability_Status__c || 'NA'}
        />
      </div>
      {modal}
    </div>
  )
}

export default YouthInfo

interface InfoRowProps {
  label: string
  value: string | ReactNode
}

const InfoRow: React.FC<InfoRowProps> = ({ label, value }) => {
  return (
    <div className="flex text-color-grey-15 gap-10">
      <span className="font-normal text-sm tracking-normal w-28">{label}</span>
      <span className="font-bold text-sm tracking-normal text-right text-black">
        {value}
      </span>
    </div>
  )
}
