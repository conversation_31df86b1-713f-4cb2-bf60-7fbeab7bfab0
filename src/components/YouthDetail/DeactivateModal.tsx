'use client'
import CustomButton from '../CustomComponents/CustomButton/CustomButton'
import { X } from 'lucide-react'
import { useUpdateYouthAccountMutation } from '@/lib/redux/slices/apiSlice/apiSlice'
import { FormEvent, useEffect } from 'react'
import { toast } from 'react-toastify'

const DeactivateModal = ({
  onClose,
  youthId,
  setLoginAllowed,
}: {
  onClose: () => void
  youthId: string
  setLoginAllowed: any
}) => {
  const [updateYouthAccount, { isLoading, isSuccess, isError }] =
    useUpdateYouthAccountMutation()

  useEffect(() => {
    if (isSuccess) {
      setLoginAllowed((prev: boolean) => !prev)
      toast.success('Profile deactivated successfully')
      onClose()
    }
  }, [isSuccess])

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    const updateData = {
      loginAllowed: false,
    }
    const res = await updateYouthAccount({
      isTokenRequired: true,
      youthId: youthId,
      data: updateData,
    })
  }
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white rounded-2xl shadow-xl p-6 w-full max-w-md">
        {/* Modal Header */}
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold">Deactivate Account</h2>
          <button
            onClick={onClose}
            className="p-1 hover:bg-color-grey-5 rounded-full transition-colors"
          >
            <X size={20} className="text-color-grey-1" />
          </button>
        </div>

        {/* Warning Box */}
        <div className="bg-red-100 text-red-800 rounded-lg p-4 mb-6">
          <p className="font-semibold text-red-700">Are you sure?</p>
          <p className="font-normal text-sm text-gray-700 mt-1">
            The student will not be able to login to request class bookings once
            their account is deactivated.
          </p>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end gap-3">
          <CustomButton
            title={'Cancel'}
            isLoading={false}
            isDisabled={false}
            onClick={onClose}
            height={12}
            width="222px"
            backgroundColor="bg-white text-color-black border border-gray-200"
          />
          <CustomButton
            title={'Deactivate'}
            isLoading={isLoading}
            isDisabled={false}
            onClick={handleSubmit}
            height={12}
            width="222px"
            backgroundColor="bg-color-red-3 text-neutral-white"
          />
        </div>
      </div>
    </div>
  )
}
export default DeactivateModal
