interface AccountDeactivatedInfoProps {
  name: string
}

const AccountDeactivatedInfo: React.FC<AccountDeactivatedInfoProps> = ({
  name,
}) => {
  return (
    <div className="bg-red-100 border text-red-700 rounded-lg p-4 w-full leading-[1.5] tracking-normal">
      <h2 className="font-bold text-lg mb-1">Account Inactive</h2>
      <p className="font-normal text-sm text-color-grey-18">
        {name} account is inactive. Click the activate button below to allow
        them to login and use the Muse website.
      </p>
    </div>
  )
}
export default AccountDeactivatedInfo
