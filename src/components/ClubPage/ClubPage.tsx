'use client'
import React, { useEffect, useState } from 'react'
import ListIcon from '../Icons/ListIcon'
import CalendarIcon from '../Icons/CalendarIcon'
import ClubCard from '../ClubCard/ClubCard'
import ClubCalendarView from '../ClubCalendarView/ClubCalendarView'
import { useIsMobile } from '@/hooks/useIsMobile'

const ClubPage = ({ data }: { data: any }) => {
  const [clubData, setEventData] = useState<any>(data)
  const [showListings, setShowListings] = useState<boolean>(true)
  const isMobile = useIsMobile()
  useEffect(() => {
    if (isMobile) {
      setShowListings(true)
    }
  }, [isMobile])
  return (
    <div>
      <div
        className={`bg-white px-3 md:px-7 pb-7 flex items-center ${!showListings ? 'justify-end w-full' : 'justify-between'} md:gap-3 md:flex-row`}
      >
        <h1 className="text-2xl font-semibold md:hidden">Clubs</h1>
        <div className="w-full flex items-center justify-end gap-2 md:gap-4 flex-row">
          {!isMobile && (
            <div className="flex items-center justify-end gap-2 md:gap-4 flex-row">
              <button onClick={() => setShowListings(true)}>
                <div
                  className={`flex items-center gap-2 h-12 p-5 rounded-xl ${showListings ? 'bg-color-yellow' : ''} border cursor-pointer`}
                >
                  <ListIcon />
                  <span className="hidden md:block text-xs font-semibold">
                    List view
                  </span>
                </div>
              </button>
              <button onClick={() => setShowListings(false)}>
                <div
                  className={`flex items-center gap-2 h-12 p-5 rounded-xl ${!showListings ? 'bg-color-yellow' : ''} border cursor-pointer`}
                >
                  <CalendarIcon />
                  <span className="hidden md:block text-xs font-semibold">
                    Month view
                  </span>
                </div>
              </button>
            </div>
          )}
        </div>
      </div>
      <div className="w-full flex justify-center">
        {showListings ? (
          <div className="w-11/12 md:w-8/12 mt-5">
            <ClubCard clubData={clubData} />
          </div>
        ) : (
          <ClubCalendarView />
        )}
      </div>
    </div>
  )
}

export default ClubPage
