import React, { useEffect, useState, useCallback, useMemo, memo } from 'react'
import {
  format,
  addDays,
  startOfWeek,
  formatISO,
  subWeeks,
  addWeeks,
} from 'date-fns'
import EventCard from './EventCard'
import { getClassesByDate } from '@/lib/actions/class.actions'
import { toast } from 'react-toastify'
import PrevNextButton from '../PrevNextButton/PrevNextButton'
import { useIsMobile } from '@/hooks/useIsMobile'
import EventCardSkeleton from '../Skeleton/EventCardSkeleton'
import { useApi } from '@/hooks/useApi'

const weekDays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']
const weekDaysMobile = ['S', 'M', 'T', 'W', 'T', 'F', 'S']

interface WeekViewProps {
  currentWeek: any
  category: 'All' | 'Adult Class' | 'Youth Class'
  selectedType: string
  selectedGenre: string[]
  selectedLevel: string[]
  paramGeners: string | null
  paramLevels: string | null
  paramTypes: string | null
  classCategory: string | null
  selfClasses?: boolean
  activeTab?: string
}

const WeekView = ({
  currentWeek,
  category,
  selectedType,
  selectedGenre,
  selectedLevel,
  paramGeners,
  paramLevels,
  paramTypes,
  classCategory,
  selfClasses = false,
  activeTab,
}: WeekViewProps) => {
  const [classData, setClassData] = useState<any>(new Map())
  const [selectedDate, setSelectedDate] = useState<any>(
    formatISO(new Date(), { representation: 'date' }),
  )
  const [currentWeekDate, setCurrentWeekDate] = useState(currentWeek)
  const [todayEvents, setTodayEvents] = useState<any>([])
  const isMobile = useIsMobile()
  const [loading, setLoading] = useState<boolean>(false)
  const [classResponse, fetchClassData] = useApi(
    (
      accessToken: string,
      startDate: string,
      endDate: string,
      classCategory: 'All' | 'Adult Class' | 'Youth Class',
      type?: string,
      genre?: string[],
      level?: string[],
    ) =>
      getClassesByDate(
        accessToken,
        startDate,
        endDate,
        classCategory,
        type,
        genre,
        level,
      ),
    selfClasses,
  )

  useEffect(() => {
    setCurrentWeekDate(currentWeek)
  }, [currentWeek])

  const startDate = useMemo(
    () => startOfWeek(currentWeekDate),
    [currentWeekDate],
  )

  useEffect(() => {
    if (classResponse.isSuccess) {
      let newEventMap = new Map()
      classResponse?.data?.data?.forEach((event: any) => {
        let getValue = newEventMap.get(event?.startDate) || []
        newEventMap.set(event?.startDate, [...getValue, event])
      })
      setTodayEvents(newEventMap.get(selectedDate) || [])
      setClassData(newEventMap)
    }
  }, [classResponse])

  const getClassData = useCallback(
    async (start: any, end: any) => {
      setLoading(true)
      try {
        const classData = [
          start,
          end,
          category,
          selectedType,
          selectedGenre,
          selectedLevel,
        ]
        if (!selfClasses) {
          classData.unshift('')
        }
        await fetchClassData(...classData)
        setLoading(false)
      } catch (error) {
        toast.error('Something went wrong!' + error, { autoClose: 1500 })
        setLoading(false)
      }
    },
    [
      category,
      selectedType,
      selectedGenre,
      selectedLevel,
      selectedDate,
      isMobile,
    ],
  )

  useEffect(() => {
    const formattedStartDate = formatISO(startDate, { representation: 'date' })
    const endDate = addDays(startDate, 6)
    const formattedEndDate = formatISO(endDate, { representation: 'date' })
    getClassData(formattedStartDate, formattedEndDate)
  }, [
    startDate,
    getClassData,
    paramGeners,
    paramLevels,
    paramTypes,
    classCategory,
  ])

  const handleDateClick = (date: Date) => {
    const formattedDate = formatISO(date, { representation: 'date' })
    setSelectedDate(formattedDate)
    setTodayEvents(classData.get(formattedDate) || [])
  }

  const handlePrevWeek = () => {
    setCurrentWeekDate((prev: Date) => subWeeks(prev, 1))
  }

  const handleNextWeek = () => {
    setCurrentWeekDate((prev: Date) => addWeeks(prev, 1))
  }

  const renderWeekMobile = useCallback(() => {
    return weekDaysMobile.map((day, index) => {
      const date = addDays(startDate, index)
      const formattedDate = formatISO(date, { representation: 'date' })
      const isToday = date.toDateString() === new Date().toDateString()
      const isSelected = formattedDate === selectedDate

      return (
        <div
          key={index + day + 'mobile'}
          className={`flex flex-col items-center justify-center ${
            isSelected ? 'bg-color-yellow rounded-full' : ''
          } p-1.5 min-w-10 cursor-pointer`}
          onClick={() => handleDateClick(date)}
        >
          <div
            className={`text-sm ${
              isSelected || isToday ? 'text-black' : 'text-gray-500'
            }`}
          >
            {day}
          </div>
          <div
            className={`mt-2 flex items-center justify-center rounded-full ${
              isSelected
                ? 'bg-black text-white'
                : isToday
                  ? 'bg-gray-400 text-white'
                  : 'bg-gray-200'
            } w-6 h-6 text-sm`}
          >
            {format(date, 'd')}
          </div>
        </div>
      )
    })
  }, [startDate, selectedDate])

  const skeletonArray = [1, 2, 3]

  const renderWeek = useCallback(() => {
    return (
      <div className="grid grid-cols-7 gap-2 w-full">
        {weekDays.map((day, index) => {
          const date = addDays(startDate, index)
          const isToday = date.toDateString() === new Date().toDateString()
          const currentFormatedDate = formatISO(date, {
            representation: 'date',
          })
          const currentDateEvents = classData.get(currentFormatedDate) || []
          return (
            <div
              key={date.toString()}
              className="flex flex-col items-center w-full min-w-0"
            >
              <div
                className={`mb-4 text-center font-bold ${
                  isToday ? 'text-black' : 'text-gray-400'
                }`}
              >
                <p className="text-4xl md:text-5xl lg:text-6xl">
                  {format(date, 'd')}
                </p>
                <p className="font-extrabold text-sm md:text-base">{day}</p>
              </div>
              <div className="w-full flex items-center justify-center mb-3">
                <div
                  className={`w-1 h-8 md:h-12 ${
                    isToday ? 'bg-black' : 'bg-gray-300'
                  }`}
                ></div>
              </div>
              <div className="w-full space-y-2">
                {loading ? (
                  <EventCardSkeleton
                    count={skeletonArray[index % 3]}
                    weekView={true}
                  />
                ) : (
                  currentDateEvents.length > 0 &&
                  currentDateEvents.map((event: any) => (
                    <EventCard
                      key={event.meetingId}
                      classData={event}
                      weekView={true}
                    />
                  ))
                )}
              </div>
            </div>
          )
        })}
      </div>
    )
  }, [startDate, classData, loading])

  return (
    <div className="w-full">
      <div className="hidden md:block">{renderWeek()}</div>
      <div className="md:hidden">
        <div className="flex justify-between items-center px-2 mb-4">
          <span className="text-sm font-semibold">
            {format(startDate, 'MMM d')} -{' '}
            {format(addDays(startDate, 6), 'MMM d, yyyy')}
          </span>
          <PrevNextButton
            onPrev={handlePrevWeek}
            onNext={handleNextWeek}
            showBorder={false}
            notDisable={true}
          />
        </div>
        <div className="grid grid-cols-7 gap-2 px-2">{renderWeekMobile()}</div>
        <div className="mt-5 px-2">
          <h2 className="font-semibold text-sm my-5 w-full text-center">
            {format(new Date(selectedDate), 'EEEE - d MMMM yyyy')}
          </h2>
          <div className="space-y-2">
            {loading ? (
              <EventCardSkeleton count={3} weekView={true} />
            ) : (
              <>
                {todayEvents.map((event: any) => (
                  <EventCard
                    key={event.meetingId}
                    classData={event}
                    weekView={true}
                  />
                ))}
              </>
            )}
            {!loading && todayEvents.length === 0 && (
              <p className="text-center text-gray-500">
                No events for this date
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default memo(WeekView)
