import {
  formatDateToAmerican,
  formatSalesforceTime,
  getBackgroundColor,
} from '@/utils/utils'
import Image from 'next/image'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import React, { useState, useRef, useEffect, useMemo } from 'react'
import ReactDOM from 'react-dom'

interface Position {
  top: number
  left: number
}

const EventCard: React.FC<{ classData: any; weekView?: boolean }> = ({
  classData,
  weekView,
}) => {
  const [isHovered, setIsHovered] = useState<boolean>(false)
  const [position, setPosition] = useState<Position>({ top: 0, left: 0 })
  const cardRef = useRef<HTMLDivElement>(null)

  const tooltipRef = useRef<HTMLDivElement>(null)
  const [hoveredColor, setHoveredColor] = useState<string>('')
  const [arrowPosition, setArrowPosition] = useState<'left' | 'right'>('right')
  const pathname = usePathname()

  useEffect(() => {
    if (isHovered && cardRef.current && tooltipRef.current) {
      const cardRect = cardRef.current.getBoundingClientRect()
      const tooltipRect = tooltipRef.current.getBoundingClientRect()
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop
      const scrollLeft =
        window.pageXOffset || document.documentElement.scrollLeft

      const windowWidth = window.innerWidth

      let top = cardRect.top + scrollTop - 10
      let left = cardRect.left + scrollLeft

      // Determine arrow position and tooltip placement
      if (cardRect.left < windowWidth / 2) {
        // Position the hover card to the right of the classData card
        left = cardRect.right + scrollLeft + 10
        setArrowPosition('left')
      } else {
        // Position the hover card to the left of the classData card
        left = cardRect.left + scrollLeft - tooltipRect.width - 15
        setArrowPosition('right')
      }

      if (left + tooltipRect.width > windowWidth) {
        left = windowWidth - tooltipRect.width - 10
      }
      if (top < scrollTop) {
        top = cardRect.bottom + scrollTop + 10
      }
      setPosition({ top, left })
    }
  }, [isHovered])

  const handleMouseEnter = (e: React.MouseEvent<HTMLDivElement>) => {
    setIsHovered(true)
    const currentColor = getBackgroundColor(
      classData?.Level__c || classData?.classLevel,
    )
    setHoveredColor(currentColor)
  }

  const handleMouseLeave = () => {
    setIsHovered(false)
    setHoveredColor('')
  }

  const ClockIcon: React.FC = () => (
    <svg
      className="w-4 h-4 mr-2"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
    >
      <circle cx="12" cy="12" r="10" strokeWidth="2" />
      <path d="M12 6v6l4 2" strokeWidth="2" strokeLinecap="round" />
    </svg>
  )

  const UsersIcon: React.FC = () => (
    <svg
      className="w-4 h-4 mr-2"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
    >
      <path strokeWidth="2" d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" />
      <circle cx="9" cy="7" r="4" strokeWidth="2" />
      <path strokeWidth="2" d="M23 21v-2a4 4 0 0 0-3-3.87" />
      <path strokeWidth="2" d="M16 3.13a4 4 0 0 1 0 7.75" />
    </svg>
  )

  const viewType = useMemo(() => {
    const segments = pathname.split('/')[1]
    return segments === 'my-classes' ? 'classes' : segments
  }, [pathname])
  if (weekView) {
    return (
      <Link href={`/classes/${classData?.classId}`}>
        <div
          className="p-4 rounded-lg mb-2 cursor-pointer"
          style={{ backgroundColor: getBackgroundColor(classData?.Level__c) }}
        >
          {classData?.classBannerImage && (
            <Image
              width={330}
              height={120}
              src={classData?.classBannerImage}
              alt="User avatar"
              className="h-16 rounded-md object-cover w-full"
            />
          )}
          <div className="font-bold mt-1 text-xs" title={classData?.classTitle}>
            {classData?.classTitle}
          </div>
          {classData?.meetingTime && (
            <div className="text-xs text-gray-600 mt-2">
              {formatDateToAmerican(classData?.startDate) +
                ', ' +
                formatSalesforceTime(classData?.meetingTime)}
            </div>
          )}
          <div className="flex items-center text-sm text-gray-600 mt-2">
            <UsersIcon />
            {classData?.totalSeats - classData?.bookedSeats == 0 ? (
              <span className="text-xs text-gray-600">Sold Out</span>
            ) : (
              <span className="text-xs text-gray-600">
                {classData?.bookedSeats}/{classData?.totalSeats} Seats available
              </span>
            )}
          </div>
        </div>
      </Link>
    )
  }

  const HoverCard = () => (
    <div
      ref={tooltipRef}
      className="absolute rounded-lg shadow-lg w-64 p-4 hover-card-arrow"
      style={{
        top: `${position.top}px`,
        left: `${position.left}px`,
        backgroundColor:
          hoveredColor ||
          getBackgroundColor(classData?.Level__c || classData?.classLevel),
        zIndex: 9999,
        position: 'absolute',
      }}
    >
      {/* Directional Arrow */}
      <div className={`hover-card-arrow-${arrowPosition}`} />

      {classData?.classBannerImage && (
        <Image
          width={330}
          height={120}
          src={classData?.classBannerImage}
          alt="Class banner"
          className="h-16 object-cover mb-2 rounded-md"
        />
      )}
      <h3 className="font-semibold text-sm mb-2">
        {classData?.classTitle || classData?._def?.title}
      </h3>
      {classData?.meetingTime && (
        <div className="flex items-center text-sm text-gray-700 mb-1">
          <ClockIcon />
          <span>{formatSalesforceTime(classData?.meetingTime)}</span>
        </div>
      )}
      <div className="flex items-center text-sm text-gray-700">
        <UsersIcon />
        {classData?.totalSeats - classData?.bookedSeats === 0 ? (
          <span className="text-xs text-gray-600">Sold Out</span>
        ) : (
          <span className="text-xs text-gray-600">
            {classData?.bookedSeats}/{classData?.totalSeats} Seats available
          </span>
        )}
      </div>
    </div>
  )

  return (
    <div>
      <style jsx>{`
        .hover-card-arrow {
          position: relative;
        }
        .hover-card-arrow-left::before,
        .hover-card-arrow-left::after,
        .hover-card-arrow-right::before,
        .hover-card-arrow-right::after {
          content: '';
          position: absolute;
          border-style: solid;
        }
        .hover-card-arrow-left::before {
          left: -15px;
          top: 20px;
          border-width: 10px 15px 10px 0;
          border-color: transparent currentColor transparent transparent;
        }
        .hover-card-arrow-left::after {
          left: -14px;
          top: 20px;
          border-width: 10px 15px 10px 0;
          border-color: transparent inherit transparent transparent;
        }
        .hover-card-arrow-right::before {
          right: -15px;
          top: 20px;
          border-width: 10px 0 10px 15px;
          border-color: transparent transparent transparent currentColor;
        }
        .hover-card-arrow-right::after {
          right: -14px;
          top: 20px;
          border-width: 10px 0 10px 15px;
          border-color: transparent transparent transparent inherit;
        }
      `}</style>
      <div
        ref={cardRef}
        className="cursor-pointer relative"
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        style={{
          backgroundColor: getBackgroundColor(
            classData?.Level__c || classData?.classLevel,
          ),
          color: '#000', // Ensure text is readable
          borderWidth: '1px',
          borderStyle: 'solid',
          borderRadius: '4px',
          zIndex: 1,
        }}
      >
        <Link href={`${'/' + viewType + '/' + classData?.classId}`}>
          <div className="w-full p-1 truncate text-xs font-medium">
            {classData?.classTitle || classData?._def?.title}
          </div>
        </Link>
        {isHovered && ReactDOM.createPortal(<HoverCard />, document.body)}
      </div>
    </div>
  )
}

export default EventCard
