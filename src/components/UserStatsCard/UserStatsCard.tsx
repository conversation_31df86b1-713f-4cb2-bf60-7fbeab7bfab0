import React from 'react'
import <PERSON><PERSON><PERSON>Arrow from '../Icons/YellowRightArrow'

const UserStatsCard = () => {
  return (
    <div className="flex items-center flex-col gap-3 justify-center bg-color-black rounded-2xl p-7 w-full md:w-72 h-max md:h-72">
      <h3 className="text-left w-full text-white font-bold text-base">
        Your Stats
      </h3>
      <div className="flex justify-between items-center gap-1 w-full">
        <h3 className="text-white font-bold text-5xl w-2/4">58</h3>
        <div className="flex items-center justify-between gap-3 w-full">
          <span className="text-color-grey-1 text-sm">Classes Attended</span>
          <YellowRightArrow />
        </div>
      </div>
      <div className="flex justify-between items-center gap-1 w-full">
        <h3 className="text-white font-bold text-5xl w-2/4">12</h3>
        <div className="flex items-center justify-between gap-3 w-full">
          <span className="text-color-grey-1 text-sm">Events Attended</span>
          <YellowRightArrow />
        </div>
      </div>
    </div>
  )
}

export default UserStatsCard
