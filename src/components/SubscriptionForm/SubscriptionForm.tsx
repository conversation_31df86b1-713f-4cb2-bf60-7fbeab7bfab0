'use client'
import type { NextPage } from 'next'
import CustomButton from '../CustomComponents/CustomButton/CustomButton'
import { useState } from 'react'

const SubscriptionForm: NextPage = () => {
  const [isLoading, setIsLoading] = useState(false)
  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    // Add your form submission logic here
    const formData = new FormData(e.currentTarget)
  }

  return (
    <div className="max-w-md w-full bg-color-grey rounded-lg p-8 shadow-sm">
      <div className="space-y-6">
        <div className="space-y-2">
          <h2 className="text-lg font-bold text-color-black text-center">
            Stay Inspired. Stay Updated.
          </h2>
          <p className="text-color-grey-3 text-sm text-center">
            Subscribe to get the latest blogs, classes and events delivered
            right to your inbox.
          </p>
        </div>
        <div>
          <label
            htmlFor="firstName"
            className="block text-sm font-medium text-gray-700 mb-1"
          >
            First name
          </label>
          <input
            type="text"
            id="firstName"
            name="firstName"
            placeholder="Name"
            required
            className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-orange-200 focus:border-orange-400 outline-none transition duration-200"
          />
        </div>

        {/* Email Field */}
        <div>
          <label
            htmlFor="email"
            className="block text-sm font-medium text-gray-700 mb-1"
          >
            Email
          </label>
          <input
            type="email"
            id="email"
            name="email"
            placeholder="Email"
            required
            className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-orange-200 focus:border-orange-400 outline-none transition duration-200"
          />
        </div>
        <CustomButton
          title="Subscribe"
          isLoading={isLoading}
          onClick={handleSubmit}
          height={12}
        />
      </div>
    </div>
  )
}

export default SubscriptionForm
