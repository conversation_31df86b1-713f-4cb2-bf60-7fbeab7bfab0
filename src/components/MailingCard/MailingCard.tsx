import React, { useEffect, useState } from 'react'
import PenIcon from '../Icons/PenIcon'
import DeleteIcon from '../Icons/DeleteIcon'
import { useApi } from '@/hooks/useApi'
import {
  deleteMailingList,
  getMailingListItems,
} from '@/lib/actions/mailingList.actions'
import { toast } from 'react-toastify'
import GreenTick from '../Icons/GreenTick'
import CopyIcon from '../Icons/CopyIcon'
import clsx from 'clsx'
import ArrowIcon from '../Icons/UpArrow'
import { usePathname } from 'next/navigation'

const MailingCard = ({
  data,
  index,
  setMainListData,
  mainListData,
  setShowAddMailingList,
  setMailingList,
  setIsEdit,
  setMailingName,
  setIsListSaved,
  setCreatedMailListId,
  setShowLoader,
  setModalMode,
  showNotificationModal,
  expandedCard,
  setExpandedCard,
}: {
  data: any
  index: number
  setMainListData: any
  mainListData: any
  setShowAddMailingList: any
  setMailingList: any
  setIsEdit: any
  setMailingName: any
  setIsListSaved: any
  setCreatedMailListId: any
  setShowLoader: any
  setModalMode: React.Dispatch<React.SetStateAction<'create' | 'view' | 'edit'>>
  showNotificationModal?: (
    mailingListId: string,
    notifySingle?: boolean,
  ) => void
  expandedCard?: string | null
  setExpandedCard?: (id: string | null) => void
}) => {
  const pathname = usePathname()
  const isMailingListPage = pathname?.includes('/mailing-list')
  const id = React.useRef<string>(data?.id)
  const [showConfirmModal, setShowConfirmModal] = useState<boolean>(false)
  const [selectedIdToDelete, setSelectedIdToDelete] = useState(null)
  const [copyAllMailingList, setCopyAllMailingList] = React.useState(false)
  const [localExpanded, setLocalExpanded] = useState<boolean>(false)
  const isExpanded = expandedCard ? expandedCard === data?.id : localExpanded
  const [emailList, setEmailList] = useState<any[]>([])
  const [copyIndividualEmail, setCopyIndividualEmail] = useState<string | null>(
    null,
  )
  const [deleteResponse, deleteMailist] = useApi((access: string, id: string) =>
    deleteMailingList(access, id),
  )
  const [getMailingItemResponse, getMailingItem] = useApi(
    (access: string, id: string) => getMailingListItems(access, id),
  )
  const [MailingItemResponse, fetchMailingItem] = useApi(
    (access: string, id: string) => getMailingListItems(access, id),
  )
  const [accordionMailingResponse, fetchAccordionEmails] = useApi(
    (access: string, id: string) => getMailingListItems(access, id),
  )

  const refreshAccordionData = () => {
    if (expandedCard === data?.id || localExpanded) {
      fetchAccordionEmails(data?.id)
    }
  }

  useEffect(() => {
    if (deleteResponse.isSuccess) {
      toast.success('Mailing list deleted successfully', { autoClose: 2000 })
      setMainListData(
        mainListData.filter((item: any) => item.id !== id.current),
      )
    }
  }, [deleteResponse])

  useEffect(() => {
    if (MailingItemResponse.isSuccess) {
      let mailList = MailingItemResponse?.data?.data?.data || []
      const emails = mailList.map((item: any) => item.email).join(', ')
      setCopyAllMailingList(true)
      navigator.clipboard.writeText(emails)
      setTimeout(() => {
        setCopyAllMailingList(false)
      }, 3000)
    }
  }, [MailingItemResponse.isSuccess])

  useEffect(() => {
    if (getMailingItemResponse.isSuccess) {
      let mails = getMailingItemResponse?.data?.data?.data || []
      setMailingList(mails)
      setShowAddMailingList(true)
      setMailingName(data?.name)
      setCreatedMailListId(id.current)
      setIsListSaved(true)
    }
    setShowLoader(getMailingItemResponse.isFetching)
  }, [getMailingItemResponse, getMailingItemResponse.isFetching])

  useEffect(() => {
    if (accordionMailingResponse.isSuccess) {
      let mails = accordionMailingResponse?.data?.data?.data || []
      setEmailList(mails)
    }
  }, [accordionMailingResponse])

  useEffect(() => {
    window.addEventListener(`emailsUpdated-${data?.id}`, refreshAccordionData)
    return () => {
      window.removeEventListener(
        `emailsUpdated-${data?.id}`,
        refreshAccordionData,
      )
    }
  }, [data?.id])

  const handleAccordionToggle = (e: any) => {
    e.stopPropagation()
    if (!isMailingListPage) return

    if (!isExpanded && emailList.length === 0) {
      fetchAccordionEmails(data?.id)
    }

    if (setExpandedCard) {
      setExpandedCard(isExpanded ? null : data?.id)
    } else {
      setLocalExpanded(!localExpanded)
    }
  }

  const handleCopyIndividualEmail = (email: string) => {
    navigator.clipboard.writeText(email)
    setCopyIndividualEmail(email)
    setTimeout(() => {
      setCopyIndividualEmail(null)
    }, 2000)

    if (isMailingListPage) {
      if (setExpandedCard) {
        setExpandedCard(null)
      } else {
        setLocalExpanded(false)
      }
    }
  }

  const handleCardClick = (e: any) => {
    if (isMailingListPage) {
      handleAccordionToggle(e)
    } else {
      setModalMode('view')
      setShowAddMailingList(true)
      getMailingItem(data?.id)
    }
  }

  const handleCopy = (e: any, mailingId: string) => {
    e.stopPropagation()
    if (copyAllMailingList) return

    if (emailList.length > 0) {
      const emails = emailList.map((item: any) => item.email).join(', ')
      navigator.clipboard.writeText(emails)
      setCopyAllMailingList(true)
      setTimeout(() => {
        setCopyAllMailingList(false)
      }, 3000)
    } else {
      fetchMailingItem(mailingId)
    }
  }

  const handleNotifyAll = (e: any) => {
    e.stopPropagation()
    showNotificationModal && showNotificationModal(data?.id)
    if (isMailingListPage) {
      if (setExpandedCard) {
        setExpandedCard(null)
      } else {
        setLocalExpanded(false)
      }
    }
  }

  const handleNotifySingle = (e: any) => {
    e.stopPropagation()
    showNotificationModal && showNotificationModal(data?.id, true)
    if (isMailingListPage) {
      if (setExpandedCard) {
        setExpandedCard(null)
      } else {
        setLocalExpanded(false)
      }
    }
  }

  return (
    <>
      <div className="bg-white rounded-xl border border-color-grey-2 hover:shadow-md transition-all duration-200">
        <div
          key={data.id}
          className="flex flex-col xl:flex-row xl:items-center xl:justify-between px-4 xl:px-6 py-3 xl:py-4 cursor-pointer transition-all duration-200 gap-3 xl:gap-0"
          onClick={handleCardClick}
        >
          <div className="flex items-center space-x-2 sm:space-x-4">
            {!isMailingListPage && <span className="text-sm">{index + 1}</span>}
            {isMailingListPage && (
              <div className="flex items-center justify-center">
                <ArrowIcon showDownArrow={!isExpanded} isActive={true} />
              </div>
            )}
            <h2 className="text-sm sm:text-base font-medium truncate max-w-[200px] sm:max-w-none">
              {data?.name || data?.email}
            </h2>
          </div>

          <div className="flex items-center flex-wrap gap-3 sm:gap-4">
            <h2
              className="text-xs sm:text-sm font-medium text-color-blue-1 hover:text-color-blue-3"
              onClick={handleNotifyAll}
            >
              Notify All
            </h2>
            {!isMailingListPage && (
              <>
                {MailingItemResponse.isFetching ? (
                  <div className="delete-icon-loader"></div>
                ) : (
                  <button
                    onClick={(e: any) => handleCopy(e, data?.id)}
                    className={clsx(
                      'flex items-center gap-2 font-medium text-xs sm:text-sm transition-all duration-200',
                      {
                        'text-green-700 border': copyAllMailingList,
                        'text-gray-700': !copyAllMailingList,
                      },
                    )}
                    disabled={copyAllMailingList}
                  >
                    {copyAllMailingList ? <GreenTick /> : <CopyIcon />}
                  </button>
                )}
              </>
            )}
            <PenIcon
              handleClick={(e: any) => {
                e.stopPropagation()
                setModalMode('edit')
                setShowAddMailingList(true)
                getMailingItem(data?.id)
                setIsEdit(true)
              }}
            />
            {deleteResponse.isFetching ? (
              <div className="delete-icon-loader"></div>
            ) : (
              <DeleteIcon
                handleClick={(e: any) => {
                  e.stopPropagation()
                  setSelectedIdToDelete(data?.id)
                  setShowConfirmModal(true)
                }}
              />
            )}
          </div>
        </div>

        {isMailingListPage && isExpanded && (
          <div className="border-t border-gray-200 px-4 sm:px-6 py-3 sm:py-4">
            {accordionMailingResponse.isFetching ? (
              <div className="flex justify-center py-4">
                <div className="delete-icon-loader"></div>
              </div>
            ) : (
              <>
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-3 sm:mb-4 gap-2">
                  <h3 className="text-sm font-medium text-gray-700">Members</h3>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        handleCopy(e, data?.id)
                      }}
                      className={clsx(
                        'flex items-center gap-2 px-2 sm:px-3 py-1 rounded-lg text-xs sm:text-sm font-medium transition-all duration-200',
                        {
                          'bg-green-100 text-green-700': copyAllMailingList,
                          'bg-gray-100 text-gray-700 hover:bg-gray-200':
                            !copyAllMailingList,
                        },
                      )}
                      disabled={copyAllMailingList}
                    >
                      {copyAllMailingList ? (
                        <>
                          <GreenTick /> Copied
                        </>
                      ) : (
                        <>
                          <CopyIcon /> Copy All
                        </>
                      )}
                    </button>
                  </div>
                </div>

                <div
                  className={clsx(
                    'space-y-2',
                    emailList.length > 5 ? 'max-h-60 overflow-y-auto' : '',
                  )}
                >
                  {emailList.map((member: any, memberIndex: number) => (
                    <div
                      key={memberIndex}
                      className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200"
                    >
                      <div className="flex-1 min-w-0">
                        {member.name && (
                          <p className="text-sm font-medium text-gray-900 truncate">
                            {member.name}
                          </p>
                        )}
                        <p className="text-xs text-gray-600 truncate">
                          {member.email}
                        </p>
                      </div>
                      <div className="flex items-center justify-start sm:justify-end gap-2 w-full sm:w-auto">
                        <h2
                          className="text-xs sm:text-sm font-medium text-color-blue-1 hover:text-color-blue-3 cursor-pointer"
                          onClick={handleNotifySingle}
                        >
                          Notify
                        </h2>
                        <button
                          onClick={(e) => {
                            e.stopPropagation()
                            handleCopyIndividualEmail(member.email)
                          }}
                          className={clsx(
                            'p-2 rounded-lg transition-all duration-200',
                            {
                              'bg-green-100 text-green-700':
                                copyIndividualEmail === member.email,
                              'bg-white text-gray-500 hover:bg-gray-200':
                                copyIndividualEmail !== member.email,
                            },
                          )}
                        >
                          {copyIndividualEmail === member.email ? (
                            <GreenTick />
                          ) : (
                            <CopyIcon />
                          )}
                        </button>
                      </div>
                    </div>
                  ))}
                </div>

                {emailList.length === 0 && (
                  <div className="text-center py-4 text-gray-500 text-sm">
                    No members found
                  </div>
                )}
              </>
            )}
          </div>
        )}
      </div>

      {showConfirmModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 p-4 !mt-0">
          <div className="bg-white rounded-xl shadow-lg w-full max-w-md p-6">
            <h2 className="text-lg font-bold mb-3">Confirm Deletion</h2>
            <p className="text-sm text-gray-700 mb-6">
              Are you sure you want to delete this mailing list? This action
              cannot be undone.
            </p>
            <div className="flex flex-col sm:flex-row justify-end gap-2">
              <button
                className="px-4 py-2 rounded-lg bg-gray-200 hover:bg-gray-300"
                onClick={(e: any) => {
                  e.stopPropagation()
                  setShowConfirmModal(false)
                  setSelectedIdToDelete(null)
                }}
              >
                Cancel
              </button>
              <button
                className="px-4 py-2 rounded-lg bg-red-600 text-white hover:bg-red-700"
                onClick={(e: any) => {
                  e.stopPropagation()
                  if (selectedIdToDelete) {
                    deleteMailist(selectedIdToDelete)
                  }
                  setShowConfirmModal(false)
                }}
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  )
}

export default MailingCard
