'use client'
import React, { useEffect } from 'react'
import CustomButton from '../CustomComponents/CustomButton/CustomButton'
import { useApi } from '@/hooks/useApi'
import { youthCheckoutCart } from '@/lib/actions/cart.actions'
import { toast } from 'react-toastify'
import { useRouter } from 'next/navigation'

interface CustomModalProps {
  icon: any
  title: string
  desc?: string
  onClose?: () => void
  showBtn?: boolean
  showCancel?: boolean
  btnText?: string
}

const NotifyModal = ({
  icon,
  title,
  desc,
  onClose,
  showCancel = false,
  showBtn = false,
  btnText,
}: CustomModalProps) => {
  const [youthResponse, handleYouthCheckout] = useApi((access: string) =>
    youthCheckoutCart(access, false),
  )
  const router = useRouter()
  useEffect(() => {
    if (youthResponse.isSuccess) {
      toast.success('Notification sent to parent', { autoClose: 1500 })
      router.back()
    }
  }, [youthResponse])

  return (
    <div className="flex flex-col items-center gap-3 w-full">
      {icon}
      <h1 className="font-bold text-lg">{title}</h1>
      <h2 className="text-color-grey-1 text-sm mb-3">{desc}</h2>

      <div className="flex items-center justify-between gap-3 w-full">
        {showCancel && (
          <button
            className="w-full inline-flex items-center h-12 justify-center whitespace-nowrap disabled:pointer-events-none disabled:opacity-50  rounded-md text-sm focus-visible:outline-none focus-visible:ring-1 text-black focus-visible:ring-ring font-semibold shadow px-4 py-2"
            onClick={() => {
              onClose && onClose()
            }}
          >
            Cancel
          </button>
        )}
        <CustomButton
          title={btnText || 'BUTTON_TEXT'}
          isLoading={youthResponse.isFetching}
          onClick={handleYouthCheckout}
          height={12}
        />
      </div>
    </div>
  )
}

export default NotifyModal
