import React, { useEffect, useState } from 'react'
import { usePathname, useSearchParams } from 'next/navigation'

const ProgressBar = () => {
  const pathname = usePathname()
  const searchParams = useSearchParams()
  const [progress, setProgress] = useState(0)
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    let timeoutId: any

    const startLoading = () => {
      setIsLoading(true)
      setProgress(5)

      // Simulating different loading phases
      const loadingPhases = [
        { progress: 30, delay: 100 },
        { progress: 60, delay: 200 },
        { progress: 85, delay: 300 },
      ]

      loadingPhases.forEach(({ progress: phaseProgress, delay }) => {
        timeoutId = setTimeout(() => {
          setProgress(phaseProgress)
        }, delay)
      })

      // Complete the loading
      timeoutId = setTimeout(() => {
        setProgress(100)
        timeoutId = setTimeout(() => {
          setIsLoading(false)
          setProgress(0)
        }, 200)
      }, 400)
    }

    // Start loading when pathname or search params change
    startLoading()

    return () => {
      clearTimeout(timeoutId)
    }
  }, [pathname, searchParams]) // Trigger on route changes

  if (!isLoading && progress === 0) return null

  return (
    <div className="fixed top-0 left-0 z-50 w-full">
      <div className="h-1 w-full bg-gray-100">
        <div
          className="h-full bg-color-yellow transition-all duration-300 ease-in-out"
          style={{
            width: `${progress}%`,
            transition:
              progress === 100 ? 'all 0.2s ease-out' : 'all 0.4s ease-in',
          }}
        />
      </div>
    </div>
  )
}

export default ProgressBar
