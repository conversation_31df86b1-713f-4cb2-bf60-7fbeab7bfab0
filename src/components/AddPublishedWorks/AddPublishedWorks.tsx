"use client";
import React, { useState, useEffect } from "react";
import { X } from "lucide-react";
import { useFileUpload } from "@/hooks/useFileUpload";
import { toast } from "react-toastify";
import { updateAccountById } from "@/lib/actions/account.actions";
import { getToken } from "@/lib/actions/login.actions";

interface Work {
  id: string;
  title: string;
  image: string;
  link?: string;
}

interface PublishedWorksProps {
  initialWorks?: Work[];
  onChange?: (works: Work[]) => void;
}

const PublishedWorks: React.FC<PublishedWorksProps> = ({
  initialWorks = [],
  onChange
}) => {
  const [works, setWorks] = useState<Work[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingWork, setEditingWork] = useState<Work | null>(null);

  const [newTitle, setNewTitle] = useState("");
  const [newImage, setNewImage] = useState("");
  const [newLink, setNewLink] = useState("");
  const [previewImage, setPreviewImage] = useState("");
  const [isSaving, setIsSaving] = useState(false);


  const { isUploading, handleFileUpload } = useFileUpload({
    filePrefix: 'published-works',
    onSuccess: (fileUrl) => {
      setPreviewImage(fileUrl);
      setNewImage(fileUrl);
    },
    onError: (error) => {
      console.error('Cover image upload error:', error);
    },
  });

  useEffect(() => {
    if (initialWorks.length > 0) {
      setWorks(initialWorks);
    }
  }, [initialWorks]);

  const openAddModal = () => {
    setEditingWork(null);
    setNewTitle("");
    setNewImage("");
    setNewLink("");
    setPreviewImage("");
    setIsModalOpen(true);
  };

  const openEditModal = (work: Work) => {
    setEditingWork(work);
    setNewTitle(work.title);
    setNewImage(work.image);
    setNewLink(work.link || "");
    setPreviewImage(work.image);
    setIsModalOpen(true);
  };


  const handleSaveWork = async (e?: React.MouseEvent) => {
    e?.preventDefault();

    if (!newTitle?.trim() || !previewImage?.trim()) {
      toast.error('Please fill in the required fields', { autoClose: 2000 });
      return;
    }

    setIsSaving(true);

    try {
      let updatedWorks: Work[];

      if (editingWork) {
        updatedWorks = works.map((work) =>
          work.id === editingWork.id
            ? {
              ...work,
              title: newTitle.trim(),
              image: previewImage,
              link: newLink.trim(),
            }
            : work
        );
      } else {
        // Add new work
        const newWork = {
          id: Date.now().toString(),
          title: newTitle.trim(),
          image: previewImage,
          link: newLink.trim(),
        };
        updatedWorks = [...works, newWork];
      }

      const accessToken = await getToken();
      if (accessToken) {
        const response = await updateAccountById(accessToken, {
          publishedWorks: updatedWorks
        });

        if (response.success) {
          setWorks(updatedWorks);
          onChange?.(updatedWorks);
          toast.success(editingWork ? 'Work updated successfully!' : 'Work added successfully!', { autoClose: 2000 });
          setIsModalOpen(false);
        } else {
          toast.error('Failed to save work. Please try again.', { autoClose: 3000 });
        }
      } else {
        toast.error('Session expired. Please login again.', { autoClose: 3000 });
      }
    } catch (error) {
      console.error('Error saving published work:', error);
      toast.error('Failed to save work. Please try again.', { autoClose: 3000 });
    } finally {
      setIsSaving(false);
    }
  };

  const handleRemoveWork = (id: string) => {
    const updatedWorks = works.filter((work) => work.id !== id);
    setWorks(updatedWorks);
    onChange?.(updatedWorks);
  };

  return (
    <div className="space-y-1">
      <div className="flex justify-between items-center mb-4">
        <div>
          <h2 className="text-sm font-medium">Published Works</h2>
          <p className="text-gray-500 text-xs">
            Showcase your published books and works
          </p>
        </div>
      </div>

      <div className="grid grid-cols-2 xl:grid-cols-6 gap-6">
        {works.map((work) => (
          <div key={work.id} className="flex flex-col cursor-pointer">
            <div
              className="relative border rounded-xl overflow-hidden group"
              onClick={() => openEditModal(work)}
            >
              <img
                src={work.image}
                alt={work.title}
                className="w-full h-48"
              />

              <button
                onClick={(e) => {
                  e.stopPropagation();
                  handleRemoveWork(work.id);
                }}
                className="absolute top-2 right-2 bg-white rounded-full p-1 shadow opacity-0 group-hover:opacity-100 transition"
              >
                <X className="w-5 h-5 text-red-500" />
              </button>
            </div>

            <p className="mt-2 text-center text-sm font-semibold break-words leading-snug">
              {work.link ? (
                <a 
                  href={work.link} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="cursor-pointer"
                  onClick={(e) => e.stopPropagation()}
                >
                  {work.title}
                </a>
              ) : (
                work.title
              )}
            </p>
          </div>
        ))}

        <div
          onClick={openAddModal}
          className="border-2 border-dashed border-gray-300 rounded-xl flex flex-col justify-center items-center h-48 cursor-pointer hover:bg-gray-50 transition"
        >
          <span className="text-2xl">+</span>
          <p className="text-gray-500 text-sm">Add Work</p>
        </div>
      </div>

      {isModalOpen && (
        <div className="fixed inset-0 flex items-center justify-center z-50">
          <div
            className="absolute inset-0 bg-black bg-opacity-50"
            onClick={() => setIsModalOpen(false)}
          ></div>

          <div className="relative bg-white rounded-xl shadow-lg p-6 w-full max-w-md z-10">
            <button
              onClick={() => setIsModalOpen(false)}
              className="absolute top-3 right-3 text-gray-500 hover:text-gray-700"
            >
              <X className="w-5 h-5" />
            </button>

            <h2 className="text-lg font-bold">
              {editingWork ? "Edit Published Work" : "Add Published Work"}
            </h2>
            <p className="text-gray-500 text-sm mb-4">
              {editingWork
                ? "Update the details of your published work."
                : "Add a new published work to your collection."}
            </p>

            <div className="flex flex-col gap-4">
              {/* Title */}
              <div>
                <label className="text-sm font-medium">Title</label>
                <input
                  type="text"
                  placeholder="Enter book title"
                  value={newTitle}
                  onChange={(e) => setNewTitle(e.target.value)}
                  className="border rounded p-2 w-full mt-1 focus:ring focus:ring-blue-200"
                />
              </div>

              {/* Link */}
              <div>
                <label className="text-sm font-medium">Link (Optional)</label>
                <input
                  type="url"
                  placeholder="https://yourlink.com"
                  value={newLink}
                  onChange={(e) => setNewLink(e.target.value)}
                  className="border rounded p-2 w-full mt-1 focus:ring focus:ring-blue-200"
                />
              </div>

              {/* <div>
                <label className="text-sm font-medium">Cover Image</label>
                <div className="mt-1 flex flex-col gap-2">
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleImageUpload}
                    className="border rounded p-2 w-full cursor-pointer"
                  />
                  {previewImage && (
                    <img
                      src={previewImage}
                      alt="Preview"
                      className="rounded-lg h-32 object-cover border"
                    />
                  )}
                </div>
              </div> */}

              <div>
                <label className="text-sm font-medium">Cover Image</label>
                <div className="mt-1">
                  <div
                    className="border-2 border-dashed border-gray-300 rounded-xl flex flex-col items-center justify-center h-48 cursor-pointer hover:bg-gray-50 transition relative"
                    onClick={() => document.getElementById("fileInput")?.click()}
                  >
                    {isUploading ? (
                      <div className="flex flex-col items-center">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                        <p className="text-gray-500 text-sm mt-2">Uploading...</p>
                      </div>
                    ) : previewImage ? (
                      <img
                        src={previewImage}
                        alt="Preview"
                        className="absolute inset-0 w-full h-full object-contain rounded-xl"
                      />
                    ) : (
                      <div className="flex flex-col items-center">
                        <span className="text-3xl text-gray-400">+</span>
                        <p className="text-gray-500 text-sm">Upload Image</p>
                      </div>
                    )}
                  </div>

                  <input
                    id="fileInput"
                    type="file"
                    accept=".png, .jpg, .jpeg"
                    onChange={handleFileUpload}
                    disabled={isUploading}
                    className="hidden"
                  />
                </div>
              </div>


              <div className="flex justify-end gap-2 pt-2">
                <button
                  onClick={() => setIsModalOpen(false)}
                  disabled={isSaving}
                  className="px-4 py-2 border rounded-lg hover:bg-gray-100 transition disabled:opacity-50"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={handleSaveWork}
                  disabled={isSaving}
                  className={`px-4 py-2 rounded-lg transition flex items-center gap-2 min-w-24 justify-center ${
                    
                      'bg-color-yellow'
                  }`}
                >
                  {isSaving ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-black"></div>
                      {/* {editingWork ? "Updating..." : "Adding..."} */}
                    </>
                  ) : (
                    editingWork ? "Update" : "Add"
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PublishedWorks;
