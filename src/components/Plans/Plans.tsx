import React from 'react'

interface PlanOption {
  name: string
  duration: string
  price: number
  regularPrice: number
  buttonText: string
  isFeatures?: boolean
}

const Plans = () => {
  const plans: PlanOption[] = [
    {
      name: 'Basic',
      duration: '3 Month Access to All Premium Class',
      price: 159,
      regularPrice: 188,
      buttonText: 'Go Basic',
    },
    {
      name: 'Pro',
      duration: '6 Month Access to All Premium Class',
      price: 236,
      regularPrice: 278,
      buttonText: 'Go Pro',
      isFeatures: true,
    },
    {
      name: 'Elite',
      duration: '1 Year Access to All Premium Class',
      price: 312,
      regularPrice: 368,
      buttonText: 'Go Elite',
    },
  ]

  return (
    <div className="">
      <h1 className="text-lg font-bold mb-8">Plans</h1>

      <div className="space-y-6">
        {plans.map((plan, index) => (
          <div
            key={plan.name}
            className={`rounded-xl p-6 ${
              plan.isFeatures ? 'bg-gray-900 text-white' : 'bg-gray-50'
            }`}
          >
            <div className="flex justify-between items-start flex-col">
              <div className="flex w-full justify-between items-center flex-col md:flex-row">
                <div className="w-full">
                  <h2 className="text-3xl font-bold">{plan.name}</h2>
                  <p className="text-sm text-color-grey-1 dark:text-gray-400">
                    {plan.duration}
                  </p>
                </div>
                <div className="text-7xl font-bold w-full text-left md:text-right">
                  ${plan.price}
                </div>
              </div>
              <div className="flex justify-between w-full flex-col-reverse md:flex-row">
                <button
                  className={`px-6 py-2 w-44 h-12 rounded-lg bg-color-yellow text-sm hover:bg-amber-500 transition-colors text-black font-medium`}
                >
                  {plan.buttonText}
                </button>
                <p className="text-xs md:text-base text-gray-500 dark:text-gray-400 mb-5">
                  15% renewal discount - reg. price ${plan.regularPrice}
                </p>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

export default Plans
