'use client'
import { useEffect, useState } from 'react'
import LeftArrow from '../Icons/LeftArrow'
import RightArrow from '../Icons/RightArrow'
import { monthNames } from '@/utils/constants'

export const CalenderDropDown = ({
  show,
  setShowCalender,
  currentDate,
  setCurrentDate,
  todayMonth,
  setTodayMonth,
}: {
  show: boolean
  setShowCalender: any
  currentDate: any
  setCurrentDate: any
  todayMonth: any
  setTodayMonth: any
}) => {
  const [timer, setTimer] = useState<NodeJS.Timeout>()

  useEffect(() => {
    if (show) {
      hideUserBar()
    }
  }, [show])

  const daysInMonth = (date: any) => {
    return new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate()
  }

  const startDayOfMonth = (date: any) => {
    return new Date(date.getFullYear(), date.getMonth(), 1).getDay()
  }

  const navigateMonth = (direction: any) => {
    setTodayMonth(
      new Date(todayMonth.getFullYear(), todayMonth.getMonth() + direction, 1),
    )
  }

  const hideUserBar = (delay: number = 1500) => {
    const id = setTimeout(() => {
      setShowCalender(false)
    }, delay)
    setTimer(id)
  }

  const generateDays = () => {
    const days = []
    const totalDays = daysInMonth(todayMonth)
    const startDay = startDayOfMonth(todayMonth)

    // Get today's date to compare
    const isToday = (day: number) =>
      currentDate.getDate() === day &&
      currentDate.getMonth() === todayMonth.getMonth() &&
      currentDate.getFullYear() === todayMonth.getFullYear()

    // Add empty cells for days before the start of the month
    for (let i = 0; i < startDay; i++) {
      days.push(
        <div
          key={`empty-${i}`}
          className="h-10 text-center text-gray-400"
        ></div>,
      )
    }

    // Add the days of the month
    for (let i = 1; i <= totalDays; i++) {
      days.push(
        <div key={i} className="h-10 text-center py-2">
          <span
            className={`inline-block w-8 h-8 leading-8 rounded-full cursor-pointer ${
              isToday(i) ? 'bg-color-yellow text-white' : 'hover:bg-gray-100'
            }`}
            onClick={() =>
              setCurrentDate(
                new Date(todayMonth.getFullYear(), todayMonth.getMonth(), i),
              )
            }
          >
            {i}
          </span>
        </div>,
      )
    }

    return days
  }

  return (
    <>
      {show && (
        <div
          className="w-max bg-white shadow-lg rounded-lg absolute top-10 right-0 z-20"
          onPointerEnter={() => {
            if (timer) clearInterval(timer)
          }}
          onPointerLeave={() => hideUserBar()}
        >
          <div className="p-4">
            <div className="mb-4">
              <div className="flex items-center justify-between mb-4">
                <button
                  onClick={() => navigateMonth(-1)}
                  className="p-2 hover:bg-gray-100 rounded-full"
                >
                  <LeftArrow />
                </button>
                <h2 className="text-xl font-semibold">
                  {monthNames[todayMonth.getMonth()]} {todayMonth.getFullYear()}
                </h2>
                <button
                  onClick={() => navigateMonth(1)}
                  className="p-2 hover:bg-gray-100 rounded-full"
                >
                  <RightArrow />
                </button>
              </div>
              <div className="grid grid-cols-7 gap-1 mb-2">
                {['S', 'M', 'T', 'W', 'T', 'F', 'S'].map((day, index) => (
                  <div
                    key={day + index}
                    className="text-center font-semibold text-sm text-gray-600"
                  >
                    {day}
                  </div>
                ))}
              </div>
              <div className="grid grid-cols-7 text-sm gap-1">
                {generateDays()}
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  )
}
