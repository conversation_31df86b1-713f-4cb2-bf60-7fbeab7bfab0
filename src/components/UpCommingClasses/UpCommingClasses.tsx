import React from 'react'
import PrevNextButton from '../PrevNextButton/PrevNextButton'
import DashboardClassCard from '../DashboardClassCard/DashboardClassCard'

const UpCommingClasses = ({
  title = 'Classes this month',
}: {
  title?: string
}) => {
  return (
    <div>
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">{title}</h1>
        <PrevNextButton notDisable={true} />
      </div>
      <div className="flex flex-col md:flex-row items-center justify-evenly gap-5 mt-4">
        <DashboardClassCard />
        <DashboardClassCard />
      </div>
    </div>
  )
}

export default UpCommingClasses
