'use client'
import React, { useCallback, useRef } from 'react'
import GreenTick from '../Icons/GreenTick'
import Otp from '../MobileLogin/Otp'
import { loginRequestOtp } from '@/lib/auth'
import ReCAPTCHA from 'react-google-recaptcha'
import { toast } from 'react-toastify'
import axios from 'axios'
import {
  isEmailAlreadyExist,
  triggerSendMagicLink,
} from '@/lib/actions/account.actions'

interface EmailVerifyProps {
  user: any
  email: string
  setEmail: any
  phoneNumber: string
  studentOnBoarding?: boolean
  setIsEmailVerified: any
  isEmailVerified: any
  setDisableButton?: any
  classNames?: string
  title?: string
}

const EmailVerify = ({
  user,
  email,
  setEmail,
  phoneNumber,
  studentOnBoarding = true,
  setIsEmailVerified,
  isEmailVerified,
  setDisableButton,
  classNames,
  title = 'Email',
}: EmailVerifyProps) => {
  const [showOtp, setShowOtp] = React.useState<boolean>(false)
  const [showCaptcha, setShowCaptcha] = React.useState<boolean>(false)
  const [isVerified, setIsVerified] = React.useState<boolean>(false)
  const [isLoading, setIsLoading] = React.useState<boolean>(false)
  const [token, setToken] = React.useState<string | null>(null)
  const recaptchaRef = useRef<ReCAPTCHA>(null)

  const handleOTP = useCallback(async () => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!isVerified) {
      toast.error('Please verify captcha.', { autoClose: 1000 })
      return
    }
    if (!email) {
      toast.error('Please enter an email address')
      return
    }
    if (!emailRegex.test(email)) {
      toast.error('Please enter a valid email address')
      return
    }
    setIsLoading(true)
    try {
      await loginRequestOtp(email, 'email', token)
      setShowOtp(true)
    } catch (error) {
      toast.error('Something went wrong!' + error, { autoClose: 1500 })
    } finally {
      setIsLoading(false)
    }
  }, [email, isVerified, token])

  const handleCaptchaSubmission = useCallback(
    async (token: string | null) => {
      if (token) {
        setToken(token)
        setShowCaptcha(false)
        setIsVerified(true)
      } else {
        setIsVerified(false)
      }
    },
    [token],
  )

  const handleOtpSubmit = useCallback(
    async (otp: string) => {
      setIsLoading(true)
      try {
        const result = await axios.post('/api/otp/verify', {
          identifier: email,
          otp: otp,
        })
        if (studentOnBoarding) {
          const isEmailExist = await isEmailAlreadyExist(email, phoneNumber)
          if (isEmailExist) {
            await triggerSendMagicLink(email)
            setDisableButton && setDisableButton(true)
          }
        }
        setIsVerified(true)
        setIsEmailVerified(true)
        setShowOtp(false)
        toast.success(result?.data?.message, { autoClose: 1500 })
      } catch (err) {
        toast.error('Something went wrong!' + err, { autoClose: 1500 })
      } finally {
        setIsLoading(false)
      }
    },
    [email, phoneNumber],
  )

  return (
    <div>
      {!showOtp && (
        <div className="space-y-1">
          <label
            htmlFor="fullName"
            className={classNames ? classNames : `block text-sm font-medium`}
          >
            {title}
          </label>
          <div className="relative">
            <input
              type="email"
              onChange={(e) => setEmail(e.target.value)}
              className="text-sm w-full p-3 border rounded-lg pr-24 focus:outline-none focus:ring-2 focus:ring-orange-300"
              placeholder="Enter your email"
              value={user?.primaryEmail || email}
              readOnly={
                (user && user?.primaryEmail) || isEmailVerified ? true : false
              }
              required
            />
            {!user && (
              <>
                {isLoading ? (
                  <div className="absolute right-4 top-4">
                    <div className="delete-icon-loader"></div>
                  </div>
                ) : (
                  <>
                    {isEmailVerified ? (
                      <span className="absolute right-3 top-3 text-sm font-semibold text-color-green-4 flex items-center gap-2 cursor-pointer">
                        <GreenTick />
                        Verified
                      </span>
                    ) : (
                      <span
                        className="absolute right-3 top-3 text-sm font-semibold text-color-blue-1 cursor-pointer"
                        onClick={handleOTP}
                      >
                        Verify Email
                      </span>
                    )}
                  </>
                )}
              </>
            )}
          </div>
        </div>
      )}
      {showOtp && (
        <Otp
          onOtpSubmit={handleOtpSubmit}
          isLoading={isLoading}
          setShowCaptcha={setShowCaptcha}
          showCaptcha={showCaptcha}
          email={email}
          setShowOtp={setShowOtp}
          handleOTP={() => handleOTP()}
        />
      )}
      {!showOtp && !user && !isEmailVerified && (
        <div className="w-full flex items-center justify-center mt-4">
          <ReCAPTCHA
            sitekey={process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY || ''}
            ref={recaptchaRef}
            onChange={handleCaptchaSubmission}
            onExpired={() => setIsVerified(false)}
            className="w-full"
          />
        </div>
      )}
    </div>
  )
}

export default EmailVerify
