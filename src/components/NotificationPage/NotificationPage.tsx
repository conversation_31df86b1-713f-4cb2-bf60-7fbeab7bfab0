'use client'
import React from 'react'
import MarkNotifications from '../MarkNotifications/MarkNotifications'
import { NotificationCard } from '../NotificationCard/NotificationCard'

const NotificationPage = ({ notifications }: { notifications: any }) => {
  const [allNotificationsMarked, setAllNotificationsMarked] =
    React.useState(false)
  return (
    <div className="flex items-center justify-center w-full">
      <div className="m-5 p-7 bg-white rounded-3xl w-8/12">
        <div className="flex items-center justify-between">
          <h1 className="text-lg font-bold">Notification</h1>
          <MarkNotifications
            setAllNotificationsMarked={setAllNotificationsMarked}
          />
        </div>
        <div>
          {notifications.length > 0 ? (
            notifications.map((item: any, index: number) => (
              <div key={index}>
                <NotificationCard
                  notification={item}
                  isRead={allNotificationsMarked}
                />
                {index !== notifications.length - 1 && (
                  <span className="block w-full border bg-color-grey-2"></span>
                )}
              </div>
            ))
          ) : (
            <div className="h-96 flex justify-center items-center">
              <h2 className="font-medium text-4 text-color-grey-1">
                No notification found
              </h2>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default NotificationPage
