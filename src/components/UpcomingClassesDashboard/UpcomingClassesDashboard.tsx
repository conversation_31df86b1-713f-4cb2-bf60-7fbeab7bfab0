'use client'
import dayjs from 'dayjs'
import { useGetMeetingsByRoleQuery } from '@/lib/redux/slices/apiSlice/apiSlice'
import ClassEventCardSkeleton from '../Skeletons/ClassEventCardSkeleton'
import ClassEventCard from '../ClassEventCard/ClassEventCard'
import React from 'react'

interface UpcomingClassesProps {
  role: any
}
const UpcomingClassesDashboard: React.FC<UpcomingClassesProps> = ({ role }) => {
  const start = React.useMemo(() => dayjs().format('YYYY-MM-DD'), [])
  const end = React.useMemo(
    () => dayjs().add(30, 'day').format('YYYY-MM-DD'),
    [],
  )

  const queryArgs = React.useMemo(
    () => ({
      isTokenRequired: true,
      startDate: start,
      endDate: end,
      role,
    }),
    [start, end, role],
  )

  const { data, error, isFetching, isLoading } = useGetMeetingsByRoleQuery(
    queryArgs,
    {
      skip: !role,
    },
  )

  const showLoader = !role || isLoading || (isFetching && !data?.data)

  return (
    <div className="p-4 bg-white rounded-xl border border-gray-200 shadow-sm">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-sm font-bold leading-[1.5] text-neutral-black">
          Upcoming Classes & Events
        </h2>
      </div>

      {showLoader ? (
        <div className="w-full">
          {[...Array(2)].map((_, index) => (
            <ClassEventCardSkeleton key={index} />
          ))}
        </div>
      ) : data?.data?.length > 0 ? (
        <div className="w-full flex flex-col gap-2">
          {data?.data
            ?.slice(0, 3)
            ?.map((event: any, index: number) => (
              <ClassEventCard
                key={event?.Id || `event-${index}`}
                event={event}
                index={index}
                isDashboard={true}
                role={role}
              />
            ))}
        </div>
      ) : (
        <div className="rounded-2xl flex flex-col w-full items-center justify-center">
          <h2 className="font-medium text-4 text-color-grey-1">No Classes</h2>
        </div>
      )}
    </div>
  )
}

export default UpcomingClassesDashboard
