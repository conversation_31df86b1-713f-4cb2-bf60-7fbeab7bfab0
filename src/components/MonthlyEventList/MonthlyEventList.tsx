import { ChevronLeft, ChevronRight } from 'lucide-react'
import dayjs, { Dayjs } from 'dayjs'
import { useEffect, useState } from 'react'
import { toast } from 'react-toastify'
import { useApi } from '@/hooks/useApi'
import ClassEventCardSkeleton from '../Skeletons/ClassEventCardSkeleton'
import {
  getAllTeacherMeetings,
  getTeacherClassesByDate,
} from '@/lib/actions/teacher.actions'
import {
  selectEventsToday,
  setEventsForToday,
  useDispatch,
  useSelector,
} from '@/lib/redux'
import ClassEventCard from '../ClassEventCard/ClassEventCard'

type MonthlyEventListProps = {
  selectedDate: Dayjs
  setSelectedDate: React.Dispatch<React.SetStateAction<Dayjs>>
}

const MonthlyEventList = ({
  selectedDate,
  setSelectedDate,
}: MonthlyEventListProps) => {
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const dispatch = useDispatch()
  const cachedClasses = useSelector(selectEventsToday)

  const [classResponse, fetchClassDataDay] = useApi(
    (accessToken: string, startDate: string, endDate: string) =>
      getAllTeacherMeetings(accessToken, startDate, endDate),
  )

  const getClassDataDayWise = async (start: any, end: any) => {
    setIsLoading(true)
    try {
      await fetchClassDataDay(start, end)
    } catch (error) {
      toast.error('Something went wrong ss!' + error, { autoClose: 1500 })
    } finally {
      setIsLoading(false)
    }
  }

  const onPrevDate = () => {
    setSelectedDate((prev) => prev.subtract(1, 'day'))
  }

  const onNextDate = () => {
    setSelectedDate((prev) => prev.add(1, 'day'))
  }
  useEffect(() => {
    const formattedStartDate = selectedDate.format('YYYY-MM-DD')
    getClassDataDayWise(formattedStartDate, formattedStartDate)
  }, [selectedDate])

  useEffect(() => {
    if (classResponse?.isSuccess) {
      const data = classResponse?.data?.data
      if (data.length > 0) {
        const todayKey = dayjs().format('YYYY-MM-DD')
        const responseKey = dayjs(data[0].startDate).format('YYYY-MM-DD')
        if (responseKey === todayKey) {
          dispatch(setEventsForToday(data))
        }
      }
    }
  }, [classResponse])

  return (
    <div className="p-4 bg-white rounded-xl border border-gray-200 shadow-sm">
      {/* Header */}
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-sm font-bold leading-[1.5] tracking-normal align-middle text-neutral-black">
          Classes & Events on {selectedDate.format('MMM D')}
        </h2>
        <div className="flex gap-2">
          <ChevronLeft
            className="w-4 h-4 text-gray-500 cursor-pointer"
            onClick={onPrevDate}
          />
          <ChevronRight
            className="w-4 h-4 text-gray-500 cursor-pointer"
            onClick={onNextDate}
          />
        </div>
      </div>

      {/* Events List */}
      {dayjs(selectedDate).isSame(dayjs(), 'day') &&
      cachedClasses?.length > 0 ? (
        <div className="w-full">
          {cachedClasses?.map((event: any, index: number) => (
            <ClassEventCard key={event?.Id} event={event} index={index} />
          ))}
        </div>
      ) : classResponse?.isFetching ? (
        <div className="space-y-4">
          <ClassEventCardSkeleton />
          <ClassEventCardSkeleton />
          <ClassEventCardSkeleton />
        </div>
      ) : (
        <div className="w-full">
          {classResponse?.data?.data?.length > 0 ? (
            classResponse?.data?.data?.map((event: any, index: number) => (
              <ClassEventCard key={event?.Id} event={event} index={index} />
            ))
          ) : (
            <div className="rounded-2xl flex flex-col w-full items-center justify-center">
              <h2 className="font-medium text-4 text-color-grey-1">
                No Classes
              </h2>
            </div>
          )}
        </div>
      )}
    </div>
  )
}

export default MonthlyEventList
