'use client'
import React, { useState, useEffect } from 'react'
import LocationIcon from '../Icons/LocationIcon'
import { formatISO, startOfWeek, endOfWeek, addDays } from 'date-fns'
import { getMeetings } from '@/lib/actions/class.actions'
import { useApi } from '@/hooks/useApi'
import DatePicker from 'react-datepicker'
import 'react-datepicker/dist/react-datepicker.css'
import UpcomingClassEventSkeleton from '../Skeletons/UpcomingClassEventSkeleton'
import Link from 'next/link'

const getColor = (index: number) => {
  switch (index) {
    case 0:
      return 'border-l-color-orange'
    case 1:
      return 'border-l-color-blue-2'
    case 2:
      return 'border-l-color-red'
    default:
      return 'border-l-color-brown'
  }
}

const HomeUpcomingClasses = ({ data }: { data: any }) => {
  const [meetingsData, setMeetingsData] = useState<any[]>(data || [])
  const [response, meetingsApi] = useApi(getMeetings, false)
  const [activeTab, setActiveTab] = useState<string>('nextUp')
  const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([
    null,
    null,
  ])
  const [startDate, endDate] = dateRange
  const [showDatePicker, setShowDatePicker] = useState<boolean>(false)

  useEffect(() => {
    if (response.isSuccess) {
      setMeetingsData(response?.data?.data?.data || [])
    }
  }, [response])

  const getMonth = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleString('default', { month: 'short' }).toUpperCase()
  }

  const getDay = (dateString: string) => {
    const date = new Date(dateString)
    return date.getDate()
  }

  const getTime = (startTime: string, endTime: string) => {
    const start = new Date(startTime).toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit',
    })
    const end = new Date(endTime).toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit',
    })
    return `${start} - ${end}`
  }

  const handleTabClick = (
    tab: string,
    startDate?: string,
    endDate?: string,
  ) => {
    setActiveTab(tab)
    setShowDatePicker(tab === 'chooseDate')
    if (tab === 'chooseDate') return
    setMeetingsData([])
    meetingsApi(startDate, endDate)
  }

  // Apply date range when both dates are selected
  useEffect(() => {
    if (startDate && endDate) {
      const formattedStartDate = formatISO(startDate, {
        representation: 'date',
      })
      const formattedEndDate = formatISO(endDate, { representation: 'date' })
      meetingsApi(formattedStartDate, formattedEndDate)
    }
  }, [dateRange])

  const tabs = [
    { id: 'nextUp', label: 'Next Up', action: () => handleTabClick('nextUp') },
    {
      id: 'today',
      label: 'Today',
      action: () =>
        handleTabClick('today', formatISO(new Date()), formatISO(new Date())),
    },
    {
      id: 'tomorrow',
      label: 'Tomorrow',
      action: () => {
        const tomorrow = addDays(new Date(), 1)
        handleTabClick('tomorrow', formatISO(tomorrow), formatISO(tomorrow))
      },
    },
    {
      id: 'thisWeek',
      label: 'This Week',
      action: () =>
        handleTabClick(
          'thisWeek',
          formatISO(startOfWeek(new Date())),
          formatISO(endOfWeek(new Date())),
        ),
    },
    {
      id: 'thisWeekend',
      label: 'This Weekend',
      action: () => {
        const saturday = addDays(new Date(), 6 - new Date().getDay())
        const sunday = addDays(new Date(), 7 - new Date().getDay())
        handleTabClick('thisWeekend', formatISO(saturday), formatISO(sunday))
      },
    },
    {
      id: 'chooseDate',
      label: 'Choose a date →',
      action: () => {
        handleTabClick('chooseDate')
      },
    },
  ]

  return (
    <section className="pb-28 px-6 md:px-12 lg:px-36">
      <h1 className="font-bold text-4xl md:text-5xl lg:text-6xl">
        Upcoming Classes & Events
      </h1>

      <div className="flex flex-wrap mt-5 md:mt-10 gap-2 md:gap-3">
        {tabs.map((tab, index: number) => (
          <div key={tab.id}>
            <button
              className={`px-4 md:px-10 py-3 md:py-4 font-semibold text-sm md:text-base rounded-[60px] transition-all
              ${
                activeTab === tab.id
                  ? 'bg-black text-white'
                  : 'border border-black hover:bg-gray-100'
              }`}
              onClick={tab.action}
            >
              {tab.label}
            </button>
            {showDatePicker && index == tabs.length - 1 && (
              <DatePicker
                selectsRange={true}
                startDate={startDate}
                endDate={endDate}
                onChange={(update) => setDateRange(update)}
                isClearable={true}
                className="p-3 w-72 text-sm font-medium h-full border border-gray-300 rounded-md"
                placeholderText="Select date range"
                dateFormat="MMM d, yyyy"
              />
            )}
          </div>
        ))}
      </div>

      {response.isFetching ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 mt-6 lg:mt-10 gap-4">
          <UpcomingClassEventSkeleton />
          <UpcomingClassEventSkeleton />
          <UpcomingClassEventSkeleton />
          <UpcomingClassEventSkeleton />
          <UpcomingClassEventSkeleton />
          <UpcomingClassEventSkeleton />
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 mt-6 lg:mt-10 gap-4">
          {meetingsData.length > 0 ? (
            meetingsData.map((item: any, index: number) => (
              <Link href={`/classes/${item?.class_id}`} key={item.id + index}>
                <div
                  className={`border-l-4 ${getColor(index % 4)} grid grid-cols-10 border-2 p-6`}
                >
                  <div className="col-span-3 text-center">
                    <h1 className="text-sm md:text-base font-bold">
                      {getMonth(item.meeting_date)}
                    </h1>
                    <h2 className="font-bold text-3xl md:text-5xl mt-2 md:mt-3">
                      {getDay(item.meeting_date)}
                    </h2>
                  </div>
                  <div className="col-span-7">
                    <h1 className="text-sm md:text-base">
                      {getTime(item.meeting_start_time, item.meeting_end_time)}
                    </h1>
                    <div
                      className="font-bold mt-2 md:mt-3 text-lg md:text-xl line-clamp-1"
                      title={item.class_title}
                    >
                      {item.class_title}
                    </div>
                    {item?.meeting_location && (
                      <h2 className="flex items-center mt-2 md:mt-3 text-sm md:text-base">
                        <LocationIcon /> {item?.meeting_location}
                      </h2>
                    )}
                  </div>
                </div>
              </Link>
            ))
          ) : (
            <div className="col-span-full text-center py-10">
              <p className="text-lg text-gray-500 font-medium">
                No classes or events found for this time period.
              </p>
            </div>
          )}
        </div>
      )}
    </section>
  )
}

export default HomeUpcomingClasses
