'use client'
import React, { useState, useEffect } from 'react'

interface AddToCalendarProps {
  title?: string
  description?: string
  location?: string
  rrule?: string
  className?: string
}

const AddToCalendarButton: React.FC<AddToCalendarProps> = ({
  title = 'Event',
  description = '',
  location = '',
  rrule = '',
  className = '',
}) => {
  const [showOptions, setShowOptions] = useState(false)
  // Generate Google Calendar URL
  const generateGoogleCalendarUrl = (): string => {
    const googleCalendarUrl =
      `https://www.google.com/calendar/render?action=TEMPLATE` +
      `&details=${encodeURIComponent(description)}` +
      `&location=${encodeURIComponent(location)}` +
      (rrule ? `&recur=RRULE:${encodeURIComponent(rrule)}` : '')
    return googleCalendarUrl
  }

  // Generate iCalendar .ics file content
  const generateICalendarData = (): string => {
    const iCal = [
      'BEGIN:VCALENDAR',
      'VERSION:2.0',
      'PRODID:-//xAI//AddToCalendarButton//EN',
      'BEGIN:VEVENT',
      `UID:${Date.now()}@xai-calendar`,
      `DTSTAMP:${new Date().toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z'}`,
      `SUMMARY:${title}`,
      `DESCRIPTION:${description}`,
      `LOCATION:${location}`,
      ...(rrule ? [`RRULE:${rrule}`] : []),
      'END:VEVENT',
      'END:VCALENDAR',
    ].join('\r\n')

    return 'data:text/calendar;charset=utf8,' + encodeURIComponent(iCal)
  }

  const handleOptionClick = (type: 'google' | 'ical'): void => {
    setShowOptions(false)
    switch (type) {
      case 'google':
        window.open(generateGoogleCalendarUrl(), '_blank')
        break
      case 'ical':
        const link = document.createElement('a')
        link.href = generateICalendarData()
        link.download = `${title.replace(/\s+/g, '_')}.ics`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        break
    }
  }

  useEffect(() => {
    const handleOutsideClick = (e: MouseEvent) => {
      if (
        !(e.target instanceof Element) ||
        !e.target.closest('.calendar-button-container')
      ) {
        setShowOptions(false)
      }
    }

    if (showOptions) {
      document.addEventListener('click', handleOutsideClick)
    }
    return () => document.removeEventListener('click', handleOutsideClick)
  }, [showOptions])

  return (
    <div className="calendar-button-container relative w-full">
      <button
        onClick={() => setShowOptions(!showOptions)}
        type="button"
        className={`w-full border mt-1 inline-flex items-center justify-center disabled:pointer-events-none disabled:opacity-50 rounded-md text-sm font-semibold shadow px-4 py-2 ${className}`}
      >
        Add to Calendar
      </button>

      {showOptions && (
        <div className="absolute mt-2 w-full bg-white border rounded-md shadow-lg z-10 min-w-[150px]">
          <button
            onClick={() => handleOptionClick('google')}
            className="w-full text-left px-4 py-2 hover:bg-gray-100 text-sm transition-colors"
          >
            Google Calendar
          </button>
          <button
            onClick={() => handleOptionClick('ical')}
            className="w-full text-left px-4 py-2 hover:bg-gray-100 text-sm transition-colors"
          >
            iCloud/Apple Calendar
          </button>
        </div>
      )}
    </div>
  )
}

export default AddToCalendarButton
