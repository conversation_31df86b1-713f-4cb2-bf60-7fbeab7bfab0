'use client'
import React, { useState } from 'react'
import WeeklyCalendar from '../WeeklyCalendar/WeeklyCalendar'
import WeeklyEventCard from '../WeeklyEventCard/WeeklyEventCard'

const DashboardEvents = () => {
  const [currentWeek, setCurrentWeek] = useState(new Date())

  return (
    <div className="flex flex-col gap-5 w-96">
      <WeeklyCalendar
        currentWeek={currentWeek}
        setCurrentWeek={setCurrentWeek}
      />
      <WeeklyEventCard currentWeek={currentWeek} />
    </div>
  )
}

export default DashboardEvents
