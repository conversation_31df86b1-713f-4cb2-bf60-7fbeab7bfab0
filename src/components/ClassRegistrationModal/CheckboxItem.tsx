import React, { useRef, useState, useEffect, useCallback } from 'react'

interface CheckboxItemProps {
  text: string
  checked: boolean
  onToggle: () => void
  modalRef?: React.RefObject<HTMLElement>
  setThirdHoveredOnce?: any
}

const CheckboxItem: React.FC<CheckboxItemProps> = ({
  text,
  checked,
  onToggle,
  modalRef,
  setThirdHoveredOnce,
}) => {
  const containerRef = useRef<HTMLDivElement | null>(null)
  const measureRef = useRef<HTMLSpanElement | null>(null)
  const tooltipRef = useRef<HTMLDivElement | null>(null)

  const [truncated, setTruncated] = useState<string>(text)
  const [isOverflowing, setIsOverflowing] = useState<boolean>(false)
  const [tooltipPos, setTooltipPos] = useState<{ top: number; left: number }>({
    top: 0,
    left: 0,
  })
  const [showTooltip, setShowTooltip] = useState<boolean>(false)

  const READ_MORE = ' Read more'
  const TOOLTIP_WIDTH = 256
  const TOOLTIP_MARGIN = 10

  const computeTruncate = useCallback(() => {
    if (!containerRef.current || !measureRef.current) return

    const container = containerRef.current
    const measure = measureRef.current
    const width = container.clientWidth
    measure.style.width = `${width}px`
    measure.innerText = text + READ_MORE

    const lineHeight = parseFloat(window.getComputedStyle(measure).lineHeight)
    const maxHeight = lineHeight * 2

    if (measure.offsetHeight <= maxHeight) {
      setTruncated(text)
      setIsOverflowing(false)
      return
    }

    let start = 0
    let end = text.length
    let best = text.length

    while (start <= end) {
      const mid = Math.floor((start + end) / 2)
      measure.innerText = text.slice(0, mid) + '…' + READ_MORE

      if (measure.offsetHeight <= maxHeight) {
        best = mid
        start = mid + 1
      } else {
        end = mid - 1
      }
    }

    setTruncated(text.slice(0, best) + '…')
    setIsOverflowing(true)
  }, [text])

  useEffect(() => {
    if (!containerRef.current) return

    const resizeObserver = new ResizeObserver(computeTruncate)
    resizeObserver.observe(containerRef.current)
    computeTruncate()

    return () => {
      resizeObserver.disconnect()
    }
  }, [computeTruncate])

  const handleMouseEnter = useCallback(
    (e: React.MouseEvent<HTMLSpanElement>) => {
      if (!tooltipRef.current) return

      const target = e.currentTarget
      const rect = target.getBoundingClientRect()
      const tooltipHeight = tooltipRef.current.offsetHeight || 100

      let left = rect.left
      let top = rect.bottom + TOOLTIP_MARGIN

      if (modalRef?.current) {
        const modalRect = modalRef.current.getBoundingClientRect()

        left = rect.left - modalRect.left
        top = rect.bottom - modalRect.top + TOOLTIP_MARGIN

        if (left + TOOLTIP_WIDTH > modalRect.width - TOOLTIP_MARGIN) {
          left = modalRect.width - TOOLTIP_WIDTH - TOOLTIP_MARGIN
        }
        if (left < TOOLTIP_MARGIN) {
          left = TOOLTIP_MARGIN
        }

        if (top + tooltipHeight > modalRect.height - TOOLTIP_MARGIN) {
          top = rect.top - modalRect.top - tooltipHeight - TOOLTIP_MARGIN
        }
        if (top < TOOLTIP_MARGIN) {
          top = TOOLTIP_MARGIN
        }

        left += modalRect.left
        top += modalRect.top
      } else {
        left = Math.max(
          TOOLTIP_MARGIN,
          Math.min(left, window.innerWidth - TOOLTIP_WIDTH - TOOLTIP_MARGIN),
        )
        if (top + tooltipHeight > window.innerHeight - TOOLTIP_MARGIN) {
          top = rect.top - tooltipHeight - TOOLTIP_MARGIN
        }
        top = Math.max(TOOLTIP_MARGIN, top)
      }

      setTooltipPos({ top, left })
      setShowTooltip(true)
      if (setThirdHoveredOnce) {
        setThirdHoveredOnce(true)
      }
    },
    [modalRef],
  )

  const handleMouseLeave = useCallback(() => {
    setShowTooltip(false)
  }, [])

  return (
    <div className="flex items-start gap-2">
      <input
        id={`checkbox-${text}`}
        type="checkbox"
        checked={checked}
        onChange={onToggle}
        className="h-5 w-5 accent-yellow-400 flex-shrink-0 mt-1 cursor-pointer"
      />
      <div
        ref={containerRef}
        className="font-normal text-xs text-color-black-4 leading-5 relative"
      >
        <span>{truncated}</span>
        {isOverflowing && (
          <>
            <span
              className="text-blue-600 underline cursor-pointer"
              onMouseEnter={handleMouseEnter}
              onMouseLeave={handleMouseLeave}
            >
              {READ_MORE}
            </span>
            <div
              ref={tooltipRef}
              className={`fixed p-3 bg-gray-800 text-white text-xs rounded shadow-lg z-[9999] 
                transition-opacity duration-200 ease-in-out
                ${showTooltip ? 'opacity-100' : 'opacity-0 pointer-events-none'}`}
              style={{
                top: tooltipPos.top,
                left: tooltipPos.left,
                width: `${TOOLTIP_WIDTH}px`,
                whiteSpace: 'normal',
                wordBreak: 'break-word',
              }}
            >
              {text}
            </div>
          </>
        )}
        <span
          ref={measureRef}
          className="absolute invisible whitespace-pre-wrap break-words"
          style={{
            visibility: 'hidden',
            position: 'absolute',
            zIndex: -999,
            whiteSpace: 'pre-wrap',
          }}
        />
      </div>
    </div>
  )
}

export default CheckboxItem
