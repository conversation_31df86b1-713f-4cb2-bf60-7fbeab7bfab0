import React from 'react'
import { X } from 'lucide-react'

import CustomButton from '../CustomComponents/CustomButton/CustomButton'
import { RecordType } from './ClassRegistrationModal'

interface PaymentChoiceModalProps {
  onClose: () => void
  onPaySelf: () => void
  onPayOther: () => void
  studentName: string
  type: string
  paySelfLoading?: boolean
  payOtherLoading?: boolean
}

const PaymentChoiceModal: React.FC<PaymentChoiceModalProps> = ({
  onClose,
  onPaySelf,
  onPayOther,
  studentName,
  type,
  paySelfLoading = false,
  payOtherLoading = false
}) => {
  const getDynamicTitle = (type: string) => {
    switch (type) {
      case RecordType.ClassRegistration:
        return 'Class Registration Request'
      case RecordType.FlexiblePayment:
        return 'Class Registration Request (Flexible Payment)'
      case RecordType.MembershipMonthly:
        return 'Membership Purchase Request'
      case RecordType.MembershipAnnual:
        return 'Membership Purchase Request'
      default:
        return 'Class Registration Request'
    }
  }
  
  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black/50 z-50">
      <div className="bg-white rounded-2xl shadow-lg max-w-md w-full p-6 relative">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold">{getDynamicTitle(type)}</h2>
          <button
            onClick={onClose}
            className="p-1 hover:bg-color-grey-5 rounded-full transition-colors"
          >
            <X size={20} className="text-color-grey-1" />
          </button>
        </div>
        <p className="mb-6 text-sm text-color-black-4">
          Choose how you’d like to proceed with the payment
        </p>
        <div className="flex flex-col gap-4">
          <CustomButton
            title="I want to pay for the class"
            onClick={onPaySelf}
            isLoading={paySelfLoading}
            isDisabled={paySelfLoading || payOtherLoading}
            backgroundColor="bg-color-yellow"
            classes="text-color-black font-semibold text-base w-full"
            height={12}
          />
          <div className="flex items-center justify-center text-sm text-color-grey-1 font-semibold">
            OR
          </div>
          <CustomButton
            title={`${studentName} will pay for the class`}
            onClick={onPayOther}
            isLoading={payOtherLoading}
            isDisabled={paySelfLoading || payOtherLoading}
            backgroundColor="bg-white"
            classes="text-color-black font-semibold text-base w-full border border-gray-200 hover:bg-gray-50"
            height={12}
          />
        </div>
        <div className="text-xs text-center text-color-grey-1 mt-6">
          Student will be notified via email to make the payment
        </div>
      </div>
    </div>
  )
}

export default PaymentChoiceModal
