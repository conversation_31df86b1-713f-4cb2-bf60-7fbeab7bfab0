'use client'
import { useUpdateParentTodoMutation } from '@/lib/redux/slices/apiSlice/apiSlice'
import { X } from 'lucide-react'
import React, { useEffect, useState } from 'react'
import { toast } from 'react-toastify'
import CustomButton from '../CustomComponents/CustomButton/CustomButton'
import useModal from '@/hooks/useModal'
import PaymentChoiceModal from './PaymentChoiceModal'
import { useRouter } from 'next/navigation'
import { useDispatch } from 'react-redux'
import { setPaymentToken } from '@/lib/redux'
import CheckboxItem from './CheckboxItem'
import BlackTickIcon from '../Icons/BlackTickIcon'

export enum RecordType {
  ClassRegistration = 'Class - Fully Paid',
  FlexiblePayment = 'Class - Split Payment',
  MembershipMonthly = 'Membership - Monthly',
  MembershipAnnual = 'Membership - Annual',
}

const ClassRegistrationModal = ({
  onClose,
  item,
  setDeletedItems,
}: {
  onClose: () => void
  item: any
  setDeletedItems: React.Dispatch<React.SetStateAction<string[]>>
}) => {
  const router = useRouter()
  const dispatch = useDispatch()
  const [updateParentTodo, { isLoading, isSuccess, isError }] =
    useUpdateParentTodoMutation()
  const [modal, showModal] = useModal()
  const handleApprove = async (orderId: string, paidBy: 'Parent' | 'Child') => {
    const res = await updateParentTodo({
      isTokenRequired: true,
      orderId,
      type: 'Approved',
      paidBy,
    })
  }

  useEffect(() => {
    if (isSuccess) {
      toast.success('Class request approved successfully', { autoClose: 2000 })
      onClose()
    }
  }, [isSuccess])

  const [checkedItems, setCheckedItems] = useState<boolean[]>([])
  const [thirdHoveredOnce, setThirdHoveredOnce] = useState<boolean>(false)
  const [showPaymentModal, setShowPaymentModal] = useState<boolean>(false)
  const [paySelfLoading, setPaySelfLoading] = useState<boolean>(false)
  const [payOtherLoading, setPayOtherLoading] = useState<boolean>(false)

  const toggleCheckbox = (index: number) => {
    setCheckedItems((prev) => {
      const newState = [...prev]
      newState[index] = !newState[index]
      return newState
    })
  }

  const handleProccedApproval = async (item: any) => {
    if (Number(item?.TotalAmount) === 0) {
      try {
        await handleApprove(item?.Id, 'Parent')
        setDeletedItems((prev) => [...prev, item?.Id])
      } catch (error) {
        toast.error('Failed to approve request. Please try again.')
      }
    } else if (item?.Account__r?.Age__c < 13) {
      try {
        dispatch(setPaymentToken(item?.Checkout_JWT_Token__c))
        await handleApprove(item?.Id, 'Parent')
        setDeletedItems((prev) => [...prev, item?.Id])
        router.push(`/checkout?token=${item?.Checkout_JWT_Token__c}`)
      } catch (error) {
        toast.error('Failed to approve request. Please try again.')
      }
    } else {
      setShowPaymentModal(true)
    }
  }

  const requiredChecked =
    (checkedItems[0] && checkedItems[1] && thirdHoveredOnce) ||
    (checkedItems[0] && checkedItems[1] && checkedItems[2])

  const inputItems = [
    'Conduct: Profanity is not allowed during the Teen Fellowship, Teen classes, or Teen events. Freedom of expression is encouraged with appropriate language in your personal presentation and creative works. Use your best judgement when conducting yourself, keeping in mind mutual respect, punctuality, and an open mind. The Muse and its programs do not discriminate based on gender, race, religion, ethnicity, sexual orientation, or socio-economic status. We expect our students to demonstrate this basic respect as well.  Dress Code: Students and teachers are expected to wear appropriate clothing to class, camps, readings, and events. “Appropriate” clothing falls into the category of the dress codes of public schools. We encourage free expression, but with professionalism & tact for a focused learning environment.',
    'I agree as one of the conditions of my participation in the Teen Writers Fellowship or any other events associated with The Muse, to specifically release The Muse Writers Center, its students, teachers, and employees from any claims or liabilities of any kind whatsoever, arising from or related to my participation in classes & events. If I accept transportation assistance facilitated by The Muse Writers Center (such as an Uber, Lyft, or Taxi service), I agree the same terms apply during transit to and from The Muse Writers Center.',
    `I agree to grant to The Muse Writers Center and its authorized representatives permission to record photos of my participation and audio or video footage of my experience of The Muse classes, programs, and events. I further agree that any of the material photographed or recorded may be used in any form, as part of the nonprofit's future publications, social media content, or printed materials. I agree that such use shall be without payment of fees, royalties, special credit or other compensation.
    Note: If you prefer not to allow your child's picture to be used on the website, <NAME_EMAIL> to request an exception.`,
  ]

  const getDynamicTitle = (item: any) => {
    switch (item?.Type) {
      case RecordType.ClassRegistration:
        return 'Class Registration Request'
      case RecordType.FlexiblePayment:
        return 'Class Registration Request (Flexible Payment)'
      case RecordType.MembershipMonthly:
        return 'Membership Purchase Request'
      case RecordType.MembershipAnnual:
        return 'Membership Purchase Request'
      default:
        return 'Class Registration Request'
    }
  }

  const getDynamicDescription = (item: any) => {
    switch (item?.Type) {
      case RecordType.ClassRegistration:
        return `${item?.Account__r?.Name} has requested an approval to attend the following classes:`
      case RecordType.FlexiblePayment:
        return `${item?.Account__r?.Name} has requested an approval to attend the following classes(with flexible payment):`
      case RecordType.MembershipMonthly:
        return `${item?.Account__r?.Name} has requested an approval to purchase a monthly membership at The Muse:`
      case RecordType.MembershipAnnual:
        return `${item?.Account__r?.Name} has requested an approval to purchase a annual membership at The Muse:`
      default:
        return `${item?.Account__r?.Name} has requested an approval to purchase a membership at The Muse:`
    }
  }

  const getDynamicBillingCycle = (item: any) => {
    switch (item?.Type) {
      case RecordType.MembershipMonthly:
        return 'Monthly'
      case RecordType.MembershipAnnual:
        return 'Annual'
      default:
        return 'Monthly'
    }
  }

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black/50 z-50 p-3">
      <div className="bg-white rounded-2xl shadow-lg max-w-xl w-full p-6">
        {/* Header */}
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold">{getDynamicTitle(item)}</h2>
          <button
            onClick={onClose}
            className="p-1 hover:bg-color-grey-5 rounded-full transition-colors"
          >
            <X size={20} className="text-color-grey-1" />
          </button>
        </div>

        {/* Description */}
        <p className="font-normal text-sm text-color-black-4 mb-6">
          {getDynamicDescription(item)}
        </p>

        {/* Class List */}
        {
          (item?.Type === RecordType.ClassRegistration || item?.Type === RecordType.FlexiblePayment) && (
            <div className="space-y-2 mb-6">
              {item?.orderItems?.map((order: any, index: number) => {
                return (
                  <div className="flex justify-between" key={index}>
                    <span className="font-bold text-base tracking-normal align-middle">
                      {order?.Product2?.Name}
                    </span>
                    <span className="font-normal text-base text-color-grey-15 tracking-normal text-right align-middle">
                      ${order?.TotalPrice+order?.Discount__c}
                    </span>
                  </div>
                )
              })}
            </div>
          )}

        {/* Membership Benefits Section - Only show for membership */}
        {(item?.Type === RecordType.MembershipMonthly || item?.Type === RecordType.MembershipAnnual) && (
          <div className="bg-color-yellow-4 border border-color-yellow-5 rounded-2xl p-4 mb-6">
            <h3 className="font-bold text-lg mb-3 text-color-black">
              Requested Membership Level: {item?.orderItems?.[0]?.Product2?.Name}
            </h3>
            <p className="font-normal text-sm text-color-black-4 mb-4">
              Benefits included with this membership:
            </p>
            <div className="space-y-3">
              {item?.benefits?.map((benefit: any, index: number) => (
                <div key={index} className="flex items-start gap-3">
                  <div className="flex-shrink-0">
                    <BlackTickIcon />
                  </div>
                  <span className="font-normal text-base text-color-black">
                    {benefit.Description__c}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}

        <hr className="my-6" />

        {/* Totals */}
        {(item?.Type === RecordType.ClassRegistration || item?.Type === RecordType.FlexiblePayment) && (
          <>
            <div className="flex justify-between text-color-grey-15">
              <span className="font-normal text-base">Cart Total</span>
              <span>${item?.TotalAmount+item?.Discount__c}</span>
            </div>
            <div className="flex justify-between text-gray-500 mt-2">
              <span>Coupon Discount</span>
              <span>- ${item?.Discount__c || 0}</span>
            </div>
            <div className={`flex justify-between text-base mt-2 ${item?.Type === RecordType.FlexiblePayment ? 'text-gray-500' : 'font-bold'}`}>
              <span>Total Due</span>
              <span>${item?.TotalAmount}</span>
            </div>
            {
              item?.Type === RecordType.FlexiblePayment && (
                <>
                  <div className="flex justify-between font-bold text-base mt-2">
                    <span>Split into 3 monthly payments of</span>
                    <span>${((item?.TotalAmount) / 3).toFixed(2)}</span>
                  </div>
                </>
              )
            }
          </>
        )}

        {
          (item?.Type === RecordType.MembershipMonthly || item?.Type === RecordType.MembershipAnnual) && (
            <>
              <div className="flex justify-between text-gray-500 mt-2">
                <span>Billing Cycle</span>
                <span>{getDynamicBillingCycle(item)}</span>
              </div>
              <div className="flex justify-between font-bold text-base mt-2">
                <span>Due Amount</span>
                <span>${item?.TotalAmount}</span>
              </div>
            </>
          )
        }


        <div className="mt-5 space-y-3 text-sm">
          {inputItems?.map((text, index) => (
            <CheckboxItem
              key={index}
              text={text}
              checked={checkedItems[index] || false}
              onToggle={() => toggleCheckbox(index)}
              setThirdHoveredOnce={
                index === 2 ? setThirdHoveredOnce : undefined
              }
            />
          ))}
        </div>

        <div className="flex justify-end mt-6 gap-6">
          <CustomButton
            title={
              item?.Account__r?.Age__c < 13
                ? 'Proceed to Checkout'
                : 'Approve & Proceed'
            }
            onClick={() => handleProccedApproval(item)}
            isLoading={isLoading}
            height={10}
            width="222px"
            backgroundColor="bg-color-yellow"
            classes="text-color-black"
            isDisabled={!requiredChecked}
          />
        </div>
      </div>
      {modal}
      {showPaymentModal && (
        <PaymentChoiceModal
          onClose={() => {
            setShowPaymentModal(false)
            setPaySelfLoading(false)
            setPayOtherLoading(false)
          }}
          onPaySelf={async () => {
            try {
              setPaySelfLoading(true)
              dispatch(setPaymentToken(item?.Checkout_JWT_Token__c))
              await handleApprove(item?.Id, 'Parent')
              setDeletedItems((prev) => [...prev, item?.Id])
              router.push(`/checkout?token=${item?.Checkout_JWT_Token__c}`)
              setShowPaymentModal(false)
            } catch (error) {
              toast.error('Failed to approve request. Please try again.')
            } finally {
              setPaySelfLoading(false)
            }
          }}
          onPayOther={async () => {
            try {
              setPayOtherLoading(true)
              await handleApprove(item?.Id, 'Child')
              setDeletedItems((prev) => [...prev, item?.Id])
              setShowPaymentModal(false)
            } catch (error) {
              toast.error('Failed to approve request. Please try again.')
            } finally {
              setPayOtherLoading(false)
            }
          }}
          studentName={item?.Account__r?.Name || 'Student'}
          type={item?.Type}
          paySelfLoading={paySelfLoading}
          payOtherLoading={payOtherLoading}
        />
      )}
    </div>
  )
}

export default ClassRegistrationModal
