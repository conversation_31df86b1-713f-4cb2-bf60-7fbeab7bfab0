'use client'
import React from 'react'
import ClassCard from '../ClassCard/ClassCard'
import Link from 'next/link'

interface PreviousClassesProps {
  previousClasses: any
  user: any
  title: string
  description: string
  showViewAll?: boolean
}

const PreviousClasses: React.FC<PreviousClassesProps> = ({
  previousClasses,
  user,
  title,
  description,
  showViewAll = true,
}) => {
  return (
    <div className="w-full">
      <div className="flex justify-between items-center mb-6 gap-3">
        <div className="flex flex-col gap-2">
          <div className="font-medium text-2xl leading-9 align-middle text-color-black">
            {title}
          </div>
          <div className="font-normal text-sm leading-6 text-color-grey-13">
            {description}
          </div>
        </div>
        {showViewAll && (
          <Link
            href={{
              pathname: '/classes',
              query: { classType: 'Previous', teacher: user?.data?.Id },
            }}
            className="text-sm font-normal leading-[2] tracking-normal text-right text-color-grey-1"
          >
            <span className="w-max inline-flex">View All</span>
          </Link>
        )}
      </div>

      {previousClasses?.data?.data?.length > 0 ? (
        <div className="grid md:grid-cols-3 gap-6">
          {(showViewAll
            ? previousClasses?.data?.data?.slice(0, 3)
            : previousClasses?.data?.data
          )?.map((item: any) => (
            <Link href={`/classes/${item?.Id}`} key={item.Id}>
              <ClassCard
                data={item}
                showRating={false}
                showDetails={true}
                shadow="sm"
              />
            </Link>
          ))}
        </div>
      ) : (
        <div className="flex items-center font-bold justify-center mb-10 text-color-grey-1">
          No classes to show
        </div>
      )}
    </div>
  )
}

export default PreviousClasses
