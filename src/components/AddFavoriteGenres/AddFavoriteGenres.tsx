import React, {
    useState,
    useEffect,
    ChangeEvent,
    KeyboardEvent,
  } from "react";
  
  interface Suggestion {
    label: string;
    value: string;
  }
  
  interface FavoriteGenresProps {
    initialChips: string[]; 
    suggestions: Suggestion[]; 
    onChange: (updated: string[]) => void; 
    debounceDelay?: number;
  }
  
  const FavoriteGenres: React.FC<FavoriteGenresProps> = ({
    initialChips,
    suggestions,
    onChange,
    debounceDelay = 300,
  }) => {
    const [chips, setChips] = useState<string[]>(initialChips || []);
    const [inputValue, setInputValue] = useState<string>("");
    const [debouncedInput, setDebouncedInput] = useState<string>("");
    const [filteredSuggestions, setFilteredSuggestions] = useState<Suggestion[]>([]);
  
    useEffect(() => {
      setChips(initialChips || []);
    }, [initialChips]);
  
    
    useEffect(() => {
      const handler = setTimeout(() => {
        setDebouncedInput(inputValue);
      }, debounceDelay);
  
      return () => {
        clearTimeout(handler);
      };
    }, [inputValue, debounceDelay]);
  
   
    useEffect(() => {
      if (debouncedInput.length >= 2) {
        const filtered = suggestions.filter(
          (item) =>
            item.label.toLowerCase().includes(debouncedInput.toLowerCase()) &&
            !chips.includes(item.value) 
        );
        setFilteredSuggestions(filtered);
      } else {
        setFilteredSuggestions([]);
      }
    }, [debouncedInput, suggestions, chips]);
  
   
    const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
      setInputValue(e.target.value);
    };
  
   
    const addChip = (chipValue: string) => {
      const trimmed = chipValue.trim();
      if (trimmed && !chips.includes(trimmed)) {
        const updatedChips = [...chips, trimmed];
        setChips(updatedChips);
        onChange(updatedChips);
      }
      setInputValue("");
      setFilteredSuggestions([]);
    };
  
   
    const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
      if (e.key === "Enter" || e.key === ",") {
        e.preventDefault();
        if (inputValue.trim()) {
          addChip(inputValue.trim());
        }
      }
    };
  
   
    const removeChip = (chipToRemove: string) => {
      const updated = chips.filter((chip) => chip !== chipToRemove);
      setChips(updated);
      onChange(updated);
    };
  
   
    const getLabelForValue = (value: string): string => {
      return suggestions.find((s) => s.value === value)?.label || value;
    };
  
    return (
      <div className="space-y-1">
        <label className="block text-sm font-medium">
          Favorite Genres
        </label>
  
       
        <div className="border border-gray-300 rounded-lg p-2 flex flex-wrap gap-2">
          {chips.map((chip) => (
            <div
              key={chip}
              className="flex items-center bg-blue-50 text-blue-600 px-3 py-1 rounded-full text-sm"
            >
              {getLabelForValue(chip)}
              <button
                type="button"
                onClick={() => removeChip(chip)}
                className="ml-2 text-blue-400 hover:text-blue-700"
              >
                ×
              </button>
            </div>
          ))}
  
          <input
            type="text"
            value={inputValue}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            placeholder="Type to search..."
            className="flex-1 min-w-[120px] outline-none text-sm px-1"
          />
        </div>
  
       
        {filteredSuggestions.length > 0 && (
          <ul className="border border-gray-300 rounded-lg mt-2 bg-white shadow-lg max-h-40 overflow-auto">
            {filteredSuggestions.map((suggestion) => (
              <li
                key={suggestion.value}
                onClick={() => addChip(suggestion.value)}
                className="px-4 py-2 cursor-pointer hover:bg-blue-100 text-gray-700"
              >
                {suggestion.label}
              </li>
            ))}
          </ul>
        )}
      </div>
    );
  };
  
  export default FavoriteGenres;
  