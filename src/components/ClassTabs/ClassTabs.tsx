import React from 'react'
import YellowTick from '../Icons/YellowTick'
import IEIcon from '../Icons/IEIcon'
import TeacherMarkAttendance from '../TeacherMarkAttendance/TeacherMarkAttendance'

interface ClassTabsProps {
  tabs: any
  classDetails: any
  userBadges?: any

  pastMeetings?: any
  handleModal?: (
    meetingId: string,
    markedAttendance: boolean,
    countType?: boolean,
  ) => void
  viewType?: string
  hideButtonsInSchedule?: boolean
  attendanceState?: any
}

const ClassTabs = ({
  tabs,
  classDetails,
  userBadges = [],
  pastMeetings,
  handleModal,
  viewType = 'teacher',
  hideButtonsInSchedule = false,
  attendanceState = {},
}: ClassTabsProps) => {
  const [activeTab, setActiveTab] = React.useState(
    viewType == 'teacher' || hideButtonsInSchedule
      ? 'schedule'
      : 'class-details',
  )
  const venueAddress = classDetails?.venue?.address
  const getTabContent = (tab: string) => {
    switch (tab) {
      case 'class-details':
        return (
          <>
            <div>
              <h2 className="text-xl font-semibold mb-3 md:mb-0">
                Description
              </h2>
              {classDetails?.Description__c && (
                <div
                  dangerouslySetInnerHTML={{
                    __html: classDetails?.Description__c,
                  }}
                />
              )}
            </div>
            {classDetails?.venue && (
              <div className="mt-3">
                <h2 className="text-xl font-semibold mb-3 md:mb-0">Location</h2>
                <div>
                  {classDetails?.venue?.name && (
                    <h3 className="font-medium">{classDetails?.venue?.name}</h3>
                  )}
                  {classDetails?.Zoom_Room__c && (
                    <h3 className="text-base">
                      <strong>Room</strong>: {classDetails?.Zoom_Room__c}
                    </h3>
                  )}
                  <h3 className="text-sm">
                    {venueAddress?.street}
                    <br />
                    {venueAddress?.city}, {venueAddress?.state} <br />
                    {venueAddress?.country} {venueAddress?.postalCode}
                  </h3>
                </div>
              </div>
            )}
            {classDetails?.prerequisiteBadges?.length > 0 && (
              <div className="mt-5">
                <h1 className="text-xl font-semibold mb-3">Prerequisites</h1>
                <div className="flex justify-start flex-col">
                  {classDetails?.prerequisiteBadges?.map(
                    (badge: any, index: number) => (
                      <div key={index} className="flex items-center gap-2">
                        {userBadges?.find(
                          (userBadge: any) => userBadge?.id == badge?.id,
                        ) ? (
                          <YellowTick />
                        ) : (
                          <IEIcon />
                        )}
                        <span className="text-sm">{badge?.badgeName}</span>
                      </div>
                    ),
                  )}
                </div>
              </div>
            )}
          </>
        )
      case 'rewards':
        return (
          <>
            <div>
              <h1 className="text-xl font-semibold mb-3">Rewards</h1>
              <p className="text-sm">
                By completing classes, you can get rewarded with badges that
                help you take advanced classes. unlock the following badges by
                taking this class.
              </p>
            </div>
            {classDetails?.awardedBadges?.length > 0 && (
              <div className="mt-5">
                <h1 className="text-xl font-semibold mb-3">Included Badges</h1>
                <div className="flex justify-start flex-col">
                  {classDetails?.awardedBadges?.map(
                    (badge: any, index: number) => (
                      <div key={index} className="flex items-center gap-2">
                        <YellowTick />
                        <span className="text-sm">{badge?.badgeName}</span>
                      </div>
                    ),
                  )}
                </div>
              </div>
            )}
          </>
        )
      case 'schedule':
        return (
          <TeacherMarkAttendance
            classDetails={classDetails}
            pastMeetings={pastMeetings}
            handleModal={handleModal}
            hideButtonsInSchedule={hideButtonsInSchedule}
            attendanceState={attendanceState}
          />
        )
      default:
        return null
    }
  }

  return (
    <>
      <div className="flex flex-col items-center gap-5">
        <div className="flex flex-wrap items-center w-full justify-start border-b">
          {tabs.map((tab: any) => (
            <div
              key={tab?.key}
              className={`flex items-center gap-1 py-3 px-10 ${activeTab == tab?.value ? 'border-b-4 border-color-yellow' : 'text-color-grey-2'} cursor-pointer`}
              onClick={() => setActiveTab(tab?.value)}
            >
              <span className="font-semibold text-base">{tab?.key}</span>
            </div>
          ))}
        </div>
        <div className="w-full">{getTabContent(activeTab)}</div>
      </div>
    </>
  )
}

export default ClassTabs
