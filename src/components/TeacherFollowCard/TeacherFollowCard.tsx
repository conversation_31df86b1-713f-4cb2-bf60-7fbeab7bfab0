'use client'
import Image from 'next/image'
import Link from 'next/link'
import React from 'react'
import { useSelector } from 'react-redux'
import { selectFollowedMap } from '@/lib/redux/slices/followSlice'
import FollowFollowedButton from '../FollowFollowedButton/FollowFollowedButton'

interface TeacherFollowCardProps {
  teacher: {
    teacher_id: string
    teacher_name: string
    teacher_photo?: string
    teacher_email?: string
    teacher_website?: string
  }
}

const TeacherFollowCard = ({ teacher }: TeacherFollowCardProps) => {
  const followedMap = useSelector(selectFollowedMap)
  const isFollowed = followedMap[teacher.teacher_id]
  // Generate email or website display text
  const getContactInfo = () => {
    if (teacher.teacher_email) {
      return teacher.teacher_email
    }
    if (teacher.teacher_website) {
      return teacher.teacher_website.replace(/^https?:\/\//, '') // Remove protocol for display
    }
    return `teacher${teacher.teacher_id.slice(-4)}@example.com` // Fallback email
  }

  return (
    <div className="flex items-center justify-between py-3 gap-10">
      <div className="flex items-center gap-3">
        <Link href={`/teachers/${teacher.teacher_id}`}>
          <div className="w-12 h-12 rounded-full overflow-hidden cursor-pointer">
            <Image
              width={48}
              height={48}
              src={teacher.teacher_photo ?? '/teacher-dummy.png'}
              alt={teacher.teacher_name}
              className="w-full h-full object-cover"
            />
          </div>
        </Link>
        <div className="flex flex-col">
          <Link
            href={`/teachers/${teacher.teacher_id}`}
            className="text-base font-semibold text-color-black hover:text-color-blue-1 transition-colors"
          >
            {teacher.teacher_name}
          </Link>
          <p className="text-sm text-color-grey-1">{getContactInfo()}</p>
        </div>
      </div>
      <div className="flex-shrink-0">
        <FollowFollowedButton
          isFollow={!!isFollowed}
          teacherIds={[teacher.teacher_id]}
        />
      </div>
    </div>
  )
}

export default TeacherFollowCard
