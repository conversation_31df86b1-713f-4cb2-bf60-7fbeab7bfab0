'use client'
import React, { ReactEventHandler, useEffect } from 'react'
import Grid from '@/components/Icons/Grid'
import CalendarIcon from '../Icons/CalendarIcon'

const renderFilterItem = (Icon: any, label: string) => (
  <div className="flex items-center gap-2 h-12 p-5 rounded-xl text-xs font-semibold border-color-grey-2 border cursor-pointer w-full md:w-max">
    <Icon />
    <span>{label}</span>
  </div>
)
interface GridCalendarButtonProps {
  calendarView?: boolean
  setShowCalenderView: any
}
const GridCalendarButton = ({
  calendarView = false,
  setShowCalenderView,
}: GridCalendarButtonProps) => {
  const handleView = (event: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    const dataValue = (event.currentTarget as HTMLDivElement).getAttribute(
      'data-value',
    )
    const url = new URL(window.location.href)
    url.searchParams.set('view', dataValue == 'false' ? 'calender' : 'grid')
    window.history.pushState({}, '', url.toString())
    setShowCalenderView(dataValue == 'false')
  }
  return (
    <div
      onClick={handleView}
      data-value={calendarView}
      className="w-full md:w-max"
    >
      {calendarView
        ? renderFilterItem(Grid, 'Grid view')
        : renderFilterItem(CalendarIcon, 'Calender view')}
    </div>
  )
}

export default GridCalendarButton
