'use client'

import { use<PERSON><PERSON> } from '@/hooks/useApi'
import { useEffect } from 'react'
import clsx from 'clsx'
import { getTeacherStats } from '@/lib/actions/teacher.actions'
import {
  selectTeacherMuseJourney,
  setTeacherMuseJourney,
  useDispatch,
  useSelector,
} from '@/lib/redux'
import Link from 'next/link'

const StatCard = ({
  count,
  label,
  bgColor,
  textColor,
}: {
  count: string
  label: string
  bgColor: string
  textColor: string
}) => (
  <div
    className={clsx(
      'flex flex-col items-center px-4 py-6 rounded-2xl flex-1 h-full',
      bgColor,
    )}
  >
    <div className={clsx('text-[48px] font-bold leading-[56px]', textColor)}>
      {count}
    </div>
    <div className="text-base font-semibold text-color-black-2/60 mt-1 text-center">
      {label}
    </div>
  </div>
)

const JourneyShimmer = () => (
  <div className="p-5 bg-white rounded-2xl shadow-sm animate-pulse">
    <div className="flex gap-4">
      {Array(4)
        .fill(0)
        .map((_, i) => (
          <div key={i} className="flex-1 h-[120px] rounded-2xl bg-gray-200" />
        ))}
    </div>
  </div>
)

const TeacherJourney = () => {
  const [teacherStats, fetchTeacherStats] = useApi((accessToken: string) =>
    getTeacherStats(accessToken),
  )

  const dispatch = useDispatch()
  const cachedJourney = useSelector(selectTeacherMuseJourney)

  useEffect(() => {
    if (!cachedJourney || Object.keys(cachedJourney).length === 0) {
      fetchTeacherStats()
    }
  }, [])

  useEffect(() => {
    if (teacherStats.isSuccess) {
      dispatch(setTeacherMuseJourney(teacherStats?.data?.data))
    }
  }, [teacherStats])

  const stats =
    cachedJourney && Object.keys(cachedJourney).length > 0
      ? cachedJourney
      : teacherStats?.data?.data

  if (teacherStats?.isFetching) {
    return <JourneyShimmer />
  }

  return (
    <div className="p-5 bg-neutral-white rounded-2xl shadow-sm">
      <h2 className="font-semibold text-base text-color-black mb-4">
        Your Muse Journey
      </h2>
      <div className="grid grid-cols-2 xl:grid-cols-4 gap-4 w-full">
        <Link href="/my-classes/active">
          <StatCard
            label="Ongoing Classes"
            count={stats?.classesInProgress?.toString() || '0'}
            bgColor="bg-color-blue-5/10"
            textColor="text-color-blue-5"
          />
        </Link>
        <Link href="/my-classes/previous">
          <StatCard
            label="Classes Taught"
            count={stats?.classesTaught?.toString() || '0'}
            bgColor="bg-color-green-3/10"
            textColor="text-color-green-3"
          />
        </Link>
        <StatCard
          label="Students Taught"
          count={stats?.studentsTaught?.toString() || '0'}
          bgColor="bg-color-orange/10"
          textColor="text-color-orange"
        />
        <StatCard
          label="Students' Favorite"
          count={stats?.studentsFavorited?.toString() || '0'}
          bgColor="bg-color-purple-2/10"
          textColor="text-color-purple-2"
        />
      </div>
    </div>
  )
}

export default TeacherJourney
