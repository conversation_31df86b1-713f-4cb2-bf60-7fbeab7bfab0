'use client'
import { useIsMobile } from '@/hooks/useIsMobile'
import { addMonths, format } from 'date-fns'
import React, { useRef, useState } from 'react'
import PrevNextButton from '../PrevNextButton/PrevNextButton'
import MonthView from '../CalendarView/MonthView'

const TeacherCalendarView = () => {
  const [currentDate, setCurrentDate] = useState(new Date())
  const calendarRef = useRef<any>(null)
  const isMobile = useIsMobile()

  const handlePrevMonth = () => {
    if (calendarRef.current) {
      const calendarApi = (calendarRef.current as any)?.getApi()
      calendarApi.prev()
    }
    setCurrentDate((prev) => addMonths(prev, -1))
  }
  const handleNextMonth = () => {
    if (calendarRef.current) {
      const calendarApi = (calendarRef.current as any)?.getApi()
      calendarApi.next()
    }
    setCurrentDate((prev) => addMonths(prev, 1))
  }

  const handlePrevNextClick = () => {
    return { onPrev: handlePrevMonth, onNext: handleNextMonth }
  }

  return (
    <div className="w-full p-10">
      <div className="flex justify-between items-center mb-10">
        <h2 className="text-2xl font-medium hidden md:block">
          {format(currentDate, 'MMMM yyyy')}
        </h2>
        <h2 className="font-medium block md:hidden">
          {format(currentDate, 'MMM yyyy')}
        </h2>

        {!isMobile && (
          <div className="flex gap-1">
            <PrevNextButton {...handlePrevNextClick()} notDisable={true} />
          </div>
        )}
      </div>
      <MonthView
        currentMonth={currentDate}
        calendarRef={calendarRef}
        isTeacherView={true}
      />
    </div>
  )
}

export default TeacherCalendarView
