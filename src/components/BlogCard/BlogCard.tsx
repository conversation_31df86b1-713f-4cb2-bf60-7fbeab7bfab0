import Link from 'next/link'
import React from 'react'

const BlogCard = ({ data }: { data: any }) => {
  return (
    <div className="flex flex-col gap-3 pb-5 border-color-grey-2 border-b-2">
      <Link href={`/blogs/${data?.id}`}>
        <h1 className="text-2xl font-bold text-color-black line-clamp-2">
          • {data?.title}
        </h1>
      </Link>
      <div
        className="mt-3 line-clamp-3 font-medium text-color-grey-4"
        style={{ fontSize: '16px' }}
        dangerouslySetInnerHTML={{
          __html: data?.content,
        }}
      />
      <div className="font-semibold text-xs flex items-center gap-2 text-color-grey-3">
        <h1>Author</h1>
        <div className="w-1 h-1 bg-black rounded-full"></div>
        <h3>{data?.author}</h3>
      </div>
    </div>
  )
}

export default BlogCard
