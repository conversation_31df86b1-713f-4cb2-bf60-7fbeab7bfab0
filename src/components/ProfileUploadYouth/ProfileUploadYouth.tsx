'use client'
import { useEffect, useState } from 'react'
import { Dispatch, SetStateAction } from 'react'
import { toast } from 'react-toastify'
import BlueUploadIcon from '../Icons/BlueUploadIcon'

interface IProfileUploadYouthProps {
  setSelectedFile: Dispatch<SetStateAction<File | null>>
  profilePhoto?: string
}

const ProfileUploadYouth: React.FC<IProfileUploadYouthProps> = ({
  setSelectedFile,
  profilePhoto,
}) => {
  const [previewUrl, setPreviewUrl] = useState<string | null>(null)

  useEffect(() => {
    if (profilePhoto) {
      setPreviewUrl(profilePhoto)
    }
  }, [profilePhoto])

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      // Simple validation for images (5MB limit)
      const maxSize = 5 * 1024 * 1024 // 5MB for images
      if (file.size > maxSize) {
        toast.error(
          'Image size must be less than 5MB. Please choose a smaller file.',
          {
            autoClose: 3000,
          },
        )
        e.target.value = ''
        return
      }
      setSelectedFile(file)
      const objectUrl = URL.createObjectURL(file)
      setPreviewUrl(objectUrl)
    }
  }

  useEffect(() => {
    return () => {
      if (previewUrl && previewUrl.startsWith('blob:')) {
        // Only revoke if it's a blob, not an external URL
        URL.revokeObjectURL(previewUrl)
      }
    }
  }, [previewUrl])

  return (
    <div className="flex gap-5">
      {previewUrl ? (
        <div className="relative w-24 h-24">
          <img
            src={previewUrl}
            alt="Profile Preview"
            className="rounded-full object-cover w-full h-full border"
          />
        </div>
      ) : null}
      <div className="flex items-start justify-start gap-2 flex-col col-span-6 md:col-span-8">
        <div className="bg-white rounded-full">
          <input
            type="file"
            accept=".png, .jpg, .jpeg"
            className="hidden"
            id="file-upload"
            onChange={handleFileChange}
          />
          <label
            htmlFor="file-upload"
            className="cursor-pointer flex items-center gap-1 bg-color-blue-4 px-4 py-3 rounded-lg border-2 border-dotted border-color-blue-2 w-full justify-start"
          >
            <BlueUploadIcon />
            <h2 className="font-medium text-color-blue-2 text-sm w-full">
              {previewUrl ? 'Upload Another Photo' : 'Upload Photo'}
            </h2>
          </label>
        </div>
        <span className={`text-color-grey-1 text-xs`}>
          Only .jpg, .jpeg, and .png • files 600x600 and max 5 MB •
          Auto-compressed for optimal upload
        </span>
      </div>
    </div>
  )
}

export default ProfileUploadYouth
