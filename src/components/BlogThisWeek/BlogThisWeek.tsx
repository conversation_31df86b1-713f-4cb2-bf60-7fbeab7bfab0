import Image from 'next/image'
import React from 'react'
import ForwardArrow from '../Icons/ForwardArrow'

const BlogThisWeek = () => {
  return (
    <div className="w-full">
      <h1 className="font-bold text-base">EVENTS THIS WEEK</h1>
      {Array.from({ length: 3 }).map((_, index) => (
        <div key={index} className="flex gap-5 mt-5">
          <Image
            src="/event.jpg"
            width={80}
            height={80}
            alt="blog-this-week"
            className="w-20 h-20 rounded-lg"
          />
          <div className="flex flex-col">
            <h2 className="text-base font-semibold">
              The Scribes: A club for 18-25 year old writers
            </h2>
            <span className="text-color-grey-4 text-xs font-medium">
              {' '}
              October 1
            </span>
          </div>
        </div>
      ))}
      <h2 className="text-base mt-5 font-semibold flex items-center gap-3">
        View all events <ForwardArrow />
      </h2>
    </div>
  )
}

export default BlogThisWeek
