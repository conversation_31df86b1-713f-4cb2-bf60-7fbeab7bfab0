import React from 'react'
interface EventTypeProps {
  title: string
}

const EventType = ({ title }: EventTypeProps) => {
  const getLevelText = (type: string): string => {
    switch (type) {
      case 'Featured':
        return 'text-color-blue-1'
      case 'New':
        return 'text-color-green-4'
      case 'Postponed':
        return 'text-color-red-2'
      default:
        return 'text-color-orange'
    }
  }

  return (
    <h3 className={`text-sm font-semibold mt-3 ${getLevelText(title)}`}>
      {title}
    </h3>
  )
}

export default EventType
