import React, { useState } from 'react'
import CustomButton from '../CustomComponents/CustomButton/CustomButton'

interface AddPaymentProps {
  onSubmit: (data: PaymentFormData) => void
  addCardResponse: any
}

interface PaymentFormData {
  cardNumber: string
  expiration: string
  securityCode: string
}

const AddPaymentMethod: React.FC<AddPaymentProps> = ({
  onSubmit,
  addCardResponse,
}) => {
  const [formData, setFormData] = useState<PaymentFormData>({
    cardNumber: '',
    expiration: '',
    securityCode: '',
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSubmit(formData)
  }

  const formatCardNumber = (value: string) => {
    return value
      .replace(/\s/g, '')
      .replace(/(\d{4})(?=\d)/g, '$1-')
      .trim()
  }

  const handleCardNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/[^\d]/g, '')
    if (value.length <= 16) {
      setFormData({
        ...formData,
        cardNumber: formatCardNumber(value),
      })
    }
  }

  const handleExpirationChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let value = e.target.value.replace(/[^\d]/g, '')
    if (value.length > 2) {
      value = value.slice(0, 2) + '/' + value.slice(2, 4)
    }
    if (value.length <= 5) {
      setFormData({
        ...formData,
        expiration: value,
      })
    }
  }

  const handleSecurityCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/[^\d]/g, '')
    if (value.length <= 4) {
      setFormData({
        ...formData,
        securityCode: value,
      })
    }
  }

  return (
    <div className="w-full mx-auto p-5 md:p-10 bg-white rounded-2xl  shadow-sm">
      <h1 className="text-2xl font-bold text-center mb-1">Add Card</h1>
      <p className="text-sm font-medium text-center mb-2 text-color-grey-3">
        We currently accept credit cards only
      </p>
      <div className="space-y-4">
        <div>
          <label htmlFor="cardNumber" className="block text-sm font-bold mb-1">
            Card Number{' '}
            <span className="font-medium text-color-grey-1">(Required)</span>
          </label>
          <input
            id="cardNumber"
            type="text"
            value={formData.cardNumber}
            onChange={handleCardNumberChange}
            placeholder="••••  ••••  ••••  ••••"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition"
            required
          />
        </div>

        <div>
          <label htmlFor="expiration" className="block text-sm font-bold mb-1">
            Expiration (MM/YY){' '}
            <span className="font-medium text-color-grey-1">(Required)</span>
          </label>
          <input
            id="expiration"
            type="text"
            value={formData.expiration}
            onChange={handleExpirationChange}
            placeholder="MM/YY"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition"
            required
          />
        </div>

        <div>
          <label
            htmlFor="securityCode"
            className="block text-sm font-bold mb-1"
          >
            Card Security Code{' '}
            <span className="font-medium text-color-grey-1">(Required)</span>
          </label>
          <input
            id="securityCode"
            type="text"
            value={formData.securityCode}
            onChange={handleSecurityCodeChange}
            placeholder="CSC"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition"
            required
          />
        </div>

        <div className="mt-6">
          <CustomButton
            title="Add Payment Method"
            height={50}
            isLoading={addCardResponse.isFetching}
            onClick={handleSubmit}
          />
        </div>
      </div>
    </div>
  )
}

export default AddPaymentMethod
