import React from 'react'
interface EventTypeProps {
  title: string
}

const ClassType = ({ title }: EventTypeProps) => {
  const getLevelText = (type: string): string => {
    switch (type) {
      case 'In-Person':
        return 'text-color-blue-1'
      case 'Online':
        return 'text-color-green-4'
      case 'Hybrid':
        return 'text-color-red-2'
      default:
        return 'text-color-orange'
    }
  }

  return (
    <div
      className={`mb-3 w-max rounded-lg flex items-center gap-3 bg-white absolute bottom-0 left-3 px-3 py-1 bg-opacity-85`}
    >
      <h3 className={`text-sm font-semibold ${getLevelText(title)}`}>
        {title}
      </h3>
    </div>
  )
}

export default ClassType
