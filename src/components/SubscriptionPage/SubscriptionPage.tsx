'use client'
import React, { useMemo, useState } from 'react'
import SubscriptionHeader from '@/components/Subscription/SubscriptionHeader'
import PricingToggle from '@/components/Subscription/PricingToggle'
import SubscriptionCard from '@/components/Subscription/SubscriptionCard'

const SubscriptionPage = ({ products }: { products: any }) => {
  const [billing, setBilling] = useState<'monthly' | 'annual'>('monthly')
  const showAnnual = useMemo(() => {
    let yearlySaving = {
      percentage: 0,
      show: false,
    }
    products.forEach((product: any) => {
      if (Number(product.annualSavings) > 0) {
        yearlySaving.percentage = Math.max(
          yearlySaving.percentage,
          Number(product.annualSavings),
        )
        yearlySaving.show = true
      }
    })
    return yearlySaving
  }, [products])
  return (
    <div className="min-h-screen bg-color-grey-8 font-jakarta">
      <SubscriptionHeader />
      <div className="flex flex-col items-center mt-8">
        <PricingToggle
          billing={billing}
          onChangeBilling={setBilling}
          showAnnual={showAnnual}
        />
        <div className="mt-8 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-5 w-full md:w-3/4">
          {products.map((plan: any) => (
            <SubscriptionCard
              key={plan.Id}
              {...plan}
              price={billing === 'annual' ? plan.annualPrice : plan.price}
              period={billing === 'annual' ? 'year' : 'month'}
              billing={billing}
            />
          ))}
        </div>
      </div>
      <div className="my-8 w-3/4 mx-auto text-center">
        <div className="text-xl mb-2">
          <span className="font-bold">Want to donate more? </span> Click here to{' '}
          <a href="#" className=" underline font-bold">
            <span className="underline">make a donation</span>
          </a>
        </div>
        <p className="text-color-grey-4 mt-6 text-base text-left leading-relaxed">
          Since its start in 2005, The Muse has grown into one of the nation’s
          largest community-based literary centers, serving tens of thousands of
          people in coastal Virginia and beyond, thanks to the support of donors
          like you.
          <br />
          <br />
          In addition to classes that touch on nearly every genre of writing,
          the Muse’s outreach programs serve young writers, military veterans,
          and elderly populations across the region; and now, with expanded
          virtual programming, across the world.
          <br />
          <br />
          The Muse is unique among premier writing centers in that nobody who
          wants to experience our lively, diverse menu of offerings is turned
          away, regardless of ability to pay.
          <br />
          <br />
          While The Muse experience is felt and lived, it is also an
          organization that requires practical and tangible resources to enable
          it.
          <br />
          <br />
          Your support will enable The Muse to deepen and widen its impact in
          coastal Virginia and elsewhere, as well as propel you into a dynamic
          community of cultural supporters.
          <br />
          <br />
          Donor Members benefit from a wide variety of benefits, including
          discounts on Muse classes, as well as discounts to local businesses.
          Make a one-time donation or split your donation into monthly payments.
          Even the smallest donations make a big difference.
        </p>
      </div>
    </div>
  )
}

export default SubscriptionPage
