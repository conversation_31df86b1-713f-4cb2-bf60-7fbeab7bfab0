'use client'
import React from 'react'
import Link from 'next/link'
import clsx from 'clsx'

interface ClassItem {
  id: string
  type: 'Online' | 'In Person' | 'Hybrid'
  title: string
  teacherName: string
  nextStart: string
  category: string
  availableForBooking: boolean
}

interface GenreData {
  genre: string
  classes: ClassItem[]
}

const ClassesAtGlance = ({ classesData }: { classesData: GenreData[] }) => {
  // Get styling based on class type
  const getTypeStyles = (type: string) => {
    switch (type) {
      case 'Online':
        return {
          bgColor: 'bg-color-green-7',
          textColor: 'text-color-green-3',
        }
      case 'In-Person':
        return {
          bgColor: 'bg-color-orange-3',
          textColor: 'text-color-orange',
        }
      case 'Hybrid':
        return {
          bgColor: 'bg-color-blue-8',
          textColor: 'text-color-blue-5',
        }
      default:
        return {
          bgColor: 'bg-gray-100',
          textColor: 'text-gray-600',
        }
    }
  }

  // Distribute cards across columns for masonry layout
  const distributeIntoColumns = (data: GenreData[], columnCount: number) => {
    const columns: GenreData[][] = Array.from({ length: columnCount }, () => [])

    data.forEach((item, index) => {
      const columnIndex = index % columnCount
      columns[columnIndex].push(item)
    })

    return columns
  }
  // For SSR compatibility, default to 3 columns
  const columns = distributeIntoColumns(classesData, 3)

  return (
    <div className="w-full py-8 px-4">
      <div className="max-w-7xl mx-auto">
        {/* Masonry Layout */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 items-start">
          {columns.map((column, columnIndex) => (
            <div key={columnIndex} className="flex flex-col gap-8">
              {column.map((genreItem, genreIndex) => (
                <div
                  key={`${columnIndex}-${genreIndex}`}
                  className="bg-white rounded-2xl"
                >
                  {/* Genre Title */}
                  <div className="bg-black text-white rounded-t-2xl p-6">
                    <h2 className="text-lg font-bold">{genreItem.genre}</h2>
                  </div>

                  {/* Classes List */}
                  <div className="px-6 pb-6">
                    {genreItem.classes.map((classItem, classIndex) => {
                      const typeStyles = getTypeStyles(classItem.type)

                      return (
                        <Link
                          key={classIndex}
                          href={`/classes/${classItem.id}`}
                          className={clsx('block group py-4 border-b-2', {
                            'border-t-2': classIndex === 0,
                          })}
                        >
                          <div className="border-b border-gray-100 pb-4 last:border-b-0 last:pb-0">
                            {/* Class Title */}
                            <h3 className="text-base font-semibold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors duration-200 leading-tight">
                              {classItem.title}
                            </h3>

                            {/* Teacher Name */}
                            <p className="text-sm text-gray-500 mb-3">
                              with {classItem.teacherName}
                            </p>

                            {/* Date and Type Row */}
                            <div className="flex items-center justify-between">
                              <span className="text-sm text-gray-500">
                                {classItem.nextStart}
                              </span>
                              <span
                                className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${typeStyles.bgColor} ${typeStyles.textColor}`}
                              >
                                {classItem.type}
                              </span>
                            </div>
                          </div>
                        </Link>
                      )
                    })}
                  </div>
                </div>
              ))}
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default ClassesAtGlance
