import Facebook from '../Icons/Facebook'
import YoutubeSvg from '../Icons/YoutubeSvg'
import Instagram from '../Icons/Instagram'
import Twitter from '../Icons/Twitter'
import Substack from '../Icons/Substack'
import Globe from '../Icons/Globe'
import SocialLinks from '../SocialAccounts/SocialAccounts'
import { TagSection } from '../TagSection/TagSection'

interface ExperienceAndSocialProps {
  teacher: any
}

const ExperienceAndSocial: React.FC<ExperienceAndSocialProps> = ({
  teacher,
}) => {
  const teacherTypesArray = teacher?.data?.Teacher_Type__c?.split(';')
  const socialLinks = [
    {
      name: 'YouTube',
      icon: <YoutubeSvg />,
      url: teacher?.data?.YouTube__c,
    },
    {
      name: 'Facebook',
      icon: <Facebook />,
      url: teacher?.data?.Facebook__c,
    },
    {
      name: 'Instagram',
      icon: <Instagram />,
      url: teacher?.data?.Instagram__c,
    },
    {
      name: 'X',
      icon: <Twitter />,
      url: teacher?.data?.Twitter__c,
    },
    {
      name: 'Substack',
      icon: <Substack />,
      url: teacher?.data?.Substack__c,
    },
    {
      name: 'Website',
      icon: <Globe />,
      url: teacher?.data?.Website,
    },
  ]

  if (
    (!teacherTypesArray || teacherTypesArray.length === 0) &&
    !socialLinks.some(({ url }) => url)
  ) {
    return null
  }

  return (
    <div className="flex flex-col gap-8">
      <TagSection title="Areas of experience" items={teacherTypesArray} />
      <SocialLinks links={socialLinks} name={teacher?.data?.Name} />
    </div>
  )
}

export default ExperienceAndSocial
