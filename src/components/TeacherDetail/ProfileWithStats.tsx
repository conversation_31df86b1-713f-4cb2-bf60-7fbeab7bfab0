import Image from 'next/image'
interface ProfileWithStatsProps {
  teacher: any
}

const ProfileWithStats: React.FC<ProfileWithStatsProps> = ({ teacher }) => {
  return (
    <div className="flex flex-col md:flex-row items-center gap-6">
      <div className="w-[249px] h-[249px] relative">
        {teacher?.data?.Photo__c ? (
          <Image
            src={teacher?.data?.Photo__c}
            alt="Profile Picture"
            fill
            className="rounded-full object-cover shadow"
          />
        ) : (
          <div className="bg-gray-300 rounded-full w-full h-full"></div>
        )}
      </div>

      <div className="flex flex-col gap-4">
        <StatCard
          icon={<BookOpenBlue />}
          count={teacher?.data?.teacherStats?.classesInProgress || 0}
          label="Ongoing Classes"
          iconBg="bg-color-blue-7"
        />
        <StatCard
          icon={<BookClosedIcon />}
          count={teacher?.data?.teacherStats?.classesTaught || 0}
          label="Classes Taught"
          iconBg="bg-color-green-5"
        />
        <StatCard
          icon={<BookOpenOrange />}
          count={teacher?.data?.teacherStats?.studentsTaught || 0}
          label="Students Taught"
          iconBg="bg-color-orange-2"
        />
      </div>
    </div>
  )
}

export default ProfileWithStats

import BookOpenBlue from '../Icons/BookOpenBlue'
import BookClosedIcon from '../Icons/BookClosedIcon'
import BookOpenOrange from '../Icons/BookOpenOrange'

type StatCardProps = {
  icon: React.ReactNode
  count: string | number
  label: string
  iconBg: string
}

const StatCard: React.FC<StatCardProps> = ({ icon, count, label, iconBg }) => (
  <div className="flex items-center gap-3 bg-white rounded-xl shadow p-4 w-64">
    <div
      className={`w-10 h-10 rounded-full flex items-center justify-center ${iconBg}`}
    >
      {icon}
    </div>
    <div>
      <div className="font-semibold text-lg text-gray-800">{count}</div>
      <div className="text-sm text-gray-500">{label}</div>
    </div>
  </div>
)
