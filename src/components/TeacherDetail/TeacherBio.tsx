'use client'
import { useAuth } from '@/contexts/AuthContext'
import { selectFollowedMap, useSelector } from '@/lib/redux'
import { useMemo } from 'react'
import FollowFollowedButton from '../FollowFollowedButton/FollowFollowedButton'

interface TeacherBioProps {
  teacher: any
}

const TeacherBio: React.FC<TeacherBioProps> = ({ teacher }) => {
  const followedMap = useSelector(selectFollowedMap)
  const { user } = useAuth()
  const teacherIds = useMemo(() => {
    const id = teacher?.data?.Id
    return id && id !== user?.id ? [id] : []
  }, [teacher?.data?.Id, user?.id])

  return (
    <div className="space-y-4 w-full">
      <div className="flex items-center gap-5">
        <h1 className="font-bold text-3xl tracking-normal leading-[48px] text-color-black">
          {teacher?.data.Name}
        </h1>
        {teacherIds && teacherIds.length > 0 && (
          <FollowFollowedButton
            teacherIds={teacherIds}
            isFollow={teacherIds.some((id: string) => followedMap[id])}
          />
        )}
      </div>

      <p className="text-sm font-normal tracking-normal text-color-black">
        {teacher?.data?.Description}
      </p>
    </div>
  )
}

export default TeacherBio
