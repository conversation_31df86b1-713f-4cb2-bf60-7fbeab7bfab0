import Image from 'next/image'
import Link from 'next/link'
interface PublishedWorksProps {
  teacher: any
}

const PublishedWorks: React.FC<PublishedWorksProps> = ({ teacher }) => {
  return (
    <div className="space-y-4 w-full">
      <h2 className="font-semibold text-2xl leading-9 text-color-black">
        Published Works
      </h2>
      <div className="flex flex-wrap gap-4">
        {teacher?.data?.publishedWorks?.length > 0 ? (
          teacher.data.publishedWorks.map((book: any, index: number) => (
            <div key={index} className="w-[91px]">
              <a href={book?.Link__c} target="_blank" rel="noopener noreferrer">
                <div className="relative h-[138px] rounded-xl overflow-hidden shadow-sm">
                  {book?.Thumbnail_URL__c ? (
                    <Image
                      src={book.Thumbnail_URL__c}
                      alt={book.Title__c}
                      fill
                      className="rounded-md object-cover"
                    />
                  ) : (
                    <Image
                      src="/published-works.png"
                      alt={book.Title__c}
                      fill
                      className="rounded-md object-cover"
                    />
                  )}
                </div>
              </a>
              <p className="mt-2 font-normal text-sm leading-6 text-color-grey-13 truncate text-center">
                {book.Title__c}
              </p>
            </div>
          ))
        ) : (
          <span className="text-sm text-color-grey-13">No published works</span>
        )}
      </div>
    </div>
  )
}

export default PublishedWorks
