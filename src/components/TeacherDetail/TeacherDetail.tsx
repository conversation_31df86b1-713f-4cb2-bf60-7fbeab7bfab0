import PreviousClasses from '../PreviousClasses/PreviousClasses'
import UpcomingClasses from '../UpcomingClasses/UpcomingClasses'
import ExperienceAndSocial from './ExperienceAndSocial'
import ProfileWithStats from './ProfileWithStats'
import PublishedWorks from './PublishedWorks'
import TeachCTA from './TeachCTA'
import TeacherBio from './TeacherBio'

interface TeacherDetailProps {
  teacher: any
  teacherMeetings: any
  previousClasses: any
}

const TeacherDetail: React.FC<TeacherDetailProps> = ({
  teacher,
  teacherMeetings,
  previousClasses,
}) => {
  return (
    <div className="w-full flex flex-col items-center justify-center">
      <div className="w-full max-w-6xl p-6 flex flex-col xl:flex-row gap-10">
        <div className="flex-1 flex flex-col items-center md:items-start gap-6">
          <ProfileWithStats teacher={teacher} />
          <TeacherBio teacher={teacher} />
          <PublishedWorks teacher={teacher} />
        </div>

        <div className="xl:w-[568px] flex flex-col gap-8">
          <ExperienceAndSocial teacher={teacher} />
          <UpcomingClasses
            userMeetings={teacherMeetings}
            user={teacher}
            title={`Study with ${teacher?.data?.Name}`}
            description={`Check out upcoming classes by ${teacher?.data?.Name}`}
          />
        </div>
      </div>
      <div className="p-6 w-full max-w-6xl">
        <PreviousClasses
          previousClasses={previousClasses}
          user={teacher}
          title={`Previous classes by ${teacher?.data?.Name}`}
          description={`Explore ${teacher?.data?.Name} previous classes`}
        />
      </div>
      <div className="p-6 w-full max-w-6xl">
        <TeachCTA />
      </div>
    </div>
  )
}

export default TeacherDetail
