import { X } from 'lucide-react'
import AddMoneyToWallet from '../AddMoneyToWallet/AddMoneyToWallet'

const AddMoneyModal = ({
  onClose,
  availableBalance,
  youthId,
}: {
  onClose: () => void
  availableBalance: number
  youthId: string
}) => {
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white rounded-2xl shadow-xl p-6 w-full max-w-md">
        <div className="flex justify-end items-center mb-4">
          <button
            onClick={onClose}
            className="p-1 hover:bg-color-grey-5 rounded-full transition-colors"
          >
            <X size={20} className="text-color-grey-1" />
          </button>
        </div>
        <AddMoneyToWallet
          balance={availableBalance}
          setIsAddWalletClicked={false}
          isFromParent={true}
          youthId={youthId}
        />
      </div>
    </div>
  )
}
export default AddMoneyModal
