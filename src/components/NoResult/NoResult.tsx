import Link from 'next/link'
import React, { ReactNode } from 'react'

const NoResult = ({
  icon,
  title = '',
  desc,
  btnText = '',
  showBtn = true,
  btnLink,
  btnClick,
  background,
  width = 'w-9/12',
}: {
  icon: ReactNode
  desc: string
  showBtn?: boolean
  title?: string
  btnText?: string
  btnLink?: string
  btnClick?: () => void
  background?: string
  width?: string
}) => {
  return (
    <div
      className={`w-full md:${width} bg-${background ?? 'white'} p-6 rounded-2xl shadow-sm flex items-center justify-center flex-col gap-4`}
    >
      {icon}
      {title != '' && (
        <h1 className="font-bold text-2xl text-center">{title}</h1>
      )}
      <div className="flex justify-center items-center text-center">
        <span className="font-medium text-sm">{desc}</span>
      </div>
      {showBtn && (
        <>
          {btnLink ? (
            <Link
              href={btnLink}
              className="h-12 flex items-center justify-center w-full md:w-7/12 bg-color-yellow font-semibold text-sm rounded-xl relative overflow-hidden group"
            >
              {btnText}
              <span className="absolute top-0 left-[-75%] w-[200%] h-full bg-white opacity-20 transition-transform duration-500 transform -skew-x-12 group-hover:translate-x-full"></span>
            </Link>
          ) : (
            <div
              onClick={btnClick}
              className="h-12 cursor-pointer flex items-center justify-center w-full md:w-7/12 bg-color-yellow font-semibold text-sm rounded-md relative overflow-hidden group"
            >
              {btnText}
              <span className="absolute top-0 -left-3/4 w-[200%] h-full bg-white opacity-20 transition-transform duration-500 transform -skew-x-12 group-hover:translate-x-full"></span>
            </div>
          )}
        </>
      )}
    </div>
  )
}

export default NoResult
