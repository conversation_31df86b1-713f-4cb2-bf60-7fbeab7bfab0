'use client'
import { MagicLinkSignIn } from '@stackframe/stack'
import React, { useEffect, useState } from 'react'
import { useCheckProfileCompletion } from '@/hooks/useCheckProfileCompletion'
import MobileLogin from '../MobileLogin/MobileLogin'
const LoginTab = ({ tab }: { tab: string }) => {
  switch (tab) {
    case 'email':
      return <MagicLinkSignIn />
    case 'mobile':
      return <MobileLogin />
    default:
      return <MagicLinkSignIn />
  }
}
const LoginComponent = () => {
  const [tab, setTab] = useState<string>('email')
  const checkProfileCompletion = useCheckProfileCompletion()

  useEffect(() => {
    checkProfileCompletion()
  }, [checkProfileCompletion])

  const handleTabs = (e: React.MouseEvent<HTMLButtonElement>) => {
    const target = e.currentTarget as HTMLButtonElement
    const attributeValue = target.getAttribute('data-value')
    if (attributeValue) {
      setTab(attributeValue)
    }
  }
  return (
    <div>
      <div className="w-full bg-color-grey h-9 rounded-md flex gap-1 items-center justify-between mb-4">
        <button
          className={`w-full ml-1 h-[80%] rounded-sm text-sm font-semibold ${tab === 'email' ? 'bg-color-black text-color-grey-2' : 'text-gray'}`}
          data-value="email"
          onClick={handleTabs}
        >
          Email
        </button>
        <button
          className={`w-full mr-1 h-[80%] rounded-sm text-sm font-semibold ${tab === 'mobile' ? 'bg-color-black text-color-grey-2' : 'text-gray'}`}
          data-value="mobile"
          onClick={handleTabs}
        >
          Mobile
        </button>
      </div>
      {LoginTab({ tab })}
    </div>
  )
}

export default LoginComponent
