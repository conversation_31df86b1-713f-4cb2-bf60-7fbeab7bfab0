'use client'
import React, { useEffect, useState, useRef } from 'react'
import DatePickerMonth from '../DatePickerMonth/DatePickerMonth'
import MonthlyEventList from '../MonthlyEventList/MonthlyEventList'
import dayjs, { Dayjs } from 'dayjs'
import { useApi } from '@/hooks/useApi'
import {
  getAllTeacherMeetings,
  getTeacherClassesByDate,
} from '@/lib/actions/teacher.actions'
import {
  selectEventsByMonth,
  setMonthlyEvents,
  useDispatch,
  useSelector,
} from '@/lib/redux'
import ResourcesCard from '../ResourcesCard/ResourcesCard'
import { useAuth } from '@/contexts/AuthContext'
import UpcomingClassesDashboard from '../UpcomingClassesDashboard/UpcomingClassesDashboard'

const TeacherDashboardEvents = () => {
  const [selectedDate, setSelectedDate] = useState<Dayjs>(dayjs())
  const [currentMonth, setCurrentMonth] = useState<Dayjs>(dayjs())
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const dispatch = useDispatch()
  const debounceRef = useRef<NodeJS.Timeout | null>(null)
  const user = useAuth()

  const [classResponseMonth, fetchClassDataMonth] = useApi(
    (accessToken: string, startDate: string, endDate: string) =>
      getAllTeacherMeetings(accessToken, startDate, endDate),
  )

  const cachedMonthlyEvents = useSelector(selectEventsByMonth)

  useEffect(() => {
    if (classResponseMonth.isSuccess) {
      const data = classResponseMonth?.data?.data || []
      if (data.length > 0) {
        const responseMonthKey = dayjs(data[0].startDate).format('YYYY-MM')
        const dates = data.map((event: any) => dayjs(event.startDate).date())
        dispatch(setMonthlyEvents({ month: responseMonthKey, events: dates }))
      }

      setIsLoading(false)
    }
  }, [classResponseMonth])

  useEffect(() => {
    if (debounceRef.current) {
      clearTimeout(debounceRef.current)
    }

    const currentKey = currentMonth.format('YYYY-MM')

    if (!cachedMonthlyEvents[currentKey]) {
      setIsLoading(true)
    }

    debounceRef.current = setTimeout(() => {
      const nextMonth = currentMonth.add(1, 'month')
      const nextKey = nextMonth.format('YYYY-MM')

      const currentStart = currentMonth.startOf('month').format('YYYY-MM-DD')
      const currentEnd = currentMonth.endOf('month').format('YYYY-MM-DD')

      const nextStart = nextMonth.startOf('month').format('YYYY-MM-DD')
      const nextEnd = nextMonth.endOf('month').format('YYYY-MM-DD')

      if (!cachedMonthlyEvents[currentKey]) {
        setIsLoading(true)
        fetchClassDataMonth(currentStart, currentEnd)
      } else {
        setIsLoading(false)
      }

      if (!cachedMonthlyEvents[nextKey]) {
        fetchClassDataMonth(nextStart, nextEnd)
      }
    }, 300)

    return () => {
      if (debounceRef.current) clearTimeout(debounceRef.current)
    }
  }, [currentMonth])

  return (
    <div className="flex flex-col gap-5">
      <ResourcesCard
        title="Teacher"
        description="Access Class Guidelines, Zoom Info, and Forms"
      />
      <UpcomingClassesDashboard role={user?.user?.role} />
      <DatePickerMonth
        currentMonth={currentMonth}
        setCurrentMonth={setCurrentMonth}
        selectedDate={selectedDate}
        setSelectedDate={setSelectedDate}
        highlightedDates={cachedMonthlyEvents[currentMonth.format('YYYY-MM')]}
        isLoading={isLoading}
      />
      <MonthlyEventList
        selectedDate={selectedDate}
        setSelectedDate={setSelectedDate}
      />
    </div>
  )
}

export default TeacherDashboardEvents
