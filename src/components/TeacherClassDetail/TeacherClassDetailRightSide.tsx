import React, { useMemo, useState } from 'react'
import CustomButton from '../CustomComponents/CustomButton/CustomButton'
import useModal from '@/hooks/useModal'
import NotifyStudentModal from '../NotifyStudentModal/NotifyStudentModal'
import ProfileCard from '../ProfileCard/ProfileCard'
import CustomDropDown from '../CustomComponents/CustomDropDown/CustomDropDown'
import CopyIcon from '../Icons/CopyIcon'
import GreenTick from '../Icons/GreenTick'
import { CLASS_TYPES } from '@/types/enum'
import Link from 'next/link'

interface TeacherClassDetailRightSideProps {
  classType: CLASS_TYPES | null
  totalEnrolled?: number
  totalAttended?: number
  notMarkedAttendanceMeeting: any
  handleModal: (
    meetingId: string,
    markedAttendance: boolean,
    countType?: boolean,
  ) => void
  attendees: Array<any>
  mailingLists: Array<any>
}

const TeacherClassDetailRightSide = ({
  classType,
  totalEnrolled,
  totalAttended,
  notMarkedAttendanceMeeting,
  handleModal,
  attendees,
  mailingLists,
}: TeacherClassDetailRightSideProps) => {
  const [modal, showModal] = useModal()
  const [copiedState, setCopiedState] = useState<boolean>(false)

  const showNotificationModal = (
    mailingListId: string,
    studentCount: number,
    parentCount: number,
  ) => {
    showModal({
      title: '',
      contentFn: (onClose: any) => (
        <NotifyStudentModal
          mailingListId={mailingListId}
          mailingLists={mailingLists}
          onClose={onClose}
          classType={classType}
          studentCount={studentCount}
          parentCount={parentCount}
        />
      ),
      closeOnClickOutside: true,
      size: 'md',
      showCloseButton: false,
    })
  }

  const studentEmails = useMemo(() => {
    const emails = attendees
      .filter((student) => student.email)
      .map((student: any) => student.email)
    return {
      email: emails.join(', '),
      count: emails.length,
    }
  }, [attendees])

  const parentEmails = useMemo(() => {
    const emails = attendees
      .filter((student) => student.parentEmail)
      .map((student: any) => student.parentEmail)
    return {
      email: emails.join(', '),
      count: emails.length,
    }
  }, [attendees])

  const handleCopyEmails = (type: 'student' | 'parent') => {
    if (type === 'student') {
      navigator.clipboard.writeText(studentEmails.email)
    } else if (type === 'parent') {
      navigator.clipboard.writeText(parentEmails.email)
    }
    setCopiedState(true)
    setTimeout(() => {
      setCopiedState(false)
    }, 2000)
  }

  const copyOptions = [
    {
      label: 'Copy student emails',
      value: 'student',
    },
    {
      label: 'Copy parent emails',
      value: 'parent',
    },
  ]

  const specialAccommodation = useMemo(() => {
    return attendees.filter((student) => student.allergies)
  }, [attendees])

  return (
    <div className="space-y-4">
      <div className="bg-white rounded-2xl p-7 space-y-6">
        <CustomButton
          title="Mark Attendance"
          isLoading={false}
          onClick={() =>
            handleModal &&
            handleModal(
              notMarkedAttendanceMeeting?.Id,
              notMarkedAttendanceMeeting?.Marked_Attendance__c,
              notMarkedAttendanceMeeting?.Headcount_based_attendance__c,
            )
          }
          height={10}
          isDisabled={!notMarkedAttendanceMeeting}
        />
        <CustomButton
          title="Notify Students"
          isLoading={false}
          backgroundColor=""
          onClick={() =>
            showNotificationModal(
              notMarkedAttendanceMeeting?.Id,
              studentEmails.count,
              parentEmails.count,
            )
          }
          height={10}
          isDisabled={false}
          classes="border"
        />
        <div className="flex flex-col gap-7">
          <div className="flex flex-col flex-wrap sm:flex-row justify-between items-start sm:items-center gap-3">
            <div className="flex flex-col">
              <h2 className="text-base font-bold">Registrations</h2>
              <h2 className="text-sm text-color-grey-1">
                {(totalAttended ?? attendees.length) +
                  '/' +
                  (totalEnrolled ?? 10)}{' '}
                Enrolled
              </h2>
            </div>
            <div className="flex items-center gap-3 w-full sm:w-auto">
              {classType === CLASS_TYPES.YOUTH ? (
                <CustomDropDown
                  icon={copiedState ? <GreenTick /> : <CopyIcon />}
                  title="Copy emails"
                  options={copyOptions}
                  handleOptionClick={(value: string) =>
                    handleCopyEmails(value as 'student' | 'parent')
                  }
                  height={10}
                  width="w-full sm:w-max"
                  text="sm"
                />
              ) : (
                <button
                  onClick={() => handleCopyEmails('student')}
                  className="flex items-center justify-center sm:justify-between w-full sm:w-auto relative gap-2 h-10 p-2 rounded-xl border-color-grey-2 border cursor-pointer"
                >
                  {copiedState ? <GreenTick /> : <CopyIcon />}
                  <span className="text-sm font-semibold">
                    Copy student emails
                  </span>
                </button>
              )}
            </div>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {classType === CLASS_TYPES.YOUTH ? (
              <div className="col-span-2 lg:col-span-3">
                {/* Youth Class 2-Column Layout */}
                <div className="grid grid-cols-2 gap-8">
                  {/* Student Column */}
                  <div className="flex flex-col gap-4">
                    <h3 className="text-sm font-semibold text-gray-700 pb-2 border-b border-gray-100">
                      Student
                    </h3>
                    <div className="flex flex-col gap-3">
                      {attendees.map((student: any) => (
                        <Link
                          key={student.id}
                          href={`/account/${student.id}`}
                          className="flex items-center gap-3 p-2 rounded-lg hover:bg-gray-50 transition-colors"
                        >
                          <ProfileCard
                            student={student}
                            height={40}
                            width={40}
                            showColumnView={true}
                          />
                        </Link>
                      ))}
                    </div>
                  </div>

                  {/* Parent Column */}
                  <div className="flex flex-col gap-4">
                    <h3 className="text-sm font-semibold text-gray-700 pb-2 border-b border-gray-100 md:text-right">
                      Parent/Guardian
                    </h3>
                    <div className="flex flex-col gap-3">
                      {attendees.map((student: any) => (
                        <div
                          key={`parent-${student.id}`}
                          className="flex items-center md:justify-end gap-3 p-2 rounded-lg hover:bg-gray-50 transition-colors min-h-[56px]"
                        >
                          <span className="text-sm font-medium text-gray-600 md:text-right">
                            {student?.parentName || 'N/A'}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              // Adult Class - Grid Layout
              attendees.map((student: any) => (
                <ProfileCard
                  key={student.id}
                  student={student}
                  height={60}
                  width={60}
                />
              ))
            )}
          </div>
        </div>
        {modal}
      </div>
      <div className="bg-white rounded-2xl p-7 space-y-6">
        <h2 className="text-base font-bold">Special Accomodation</h2>
        <div className="flex flex-col gap-3">
          {specialAccommodation.map((student: any) => (
            <div className="flex items-center justify-between" key={student.id}>
              <Link
                href={`/account/${student.id}`}
                className="flex items-center gap-3 p-2 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <ProfileCard
                  student={student}
                  height={40}
                  width={40}
                  showColumnView={true}
                />
              </Link>
              <span className="font-semibold text-color-grey-1 text-base">
                {student?.allergyInfo}
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default TeacherClassDetailRightSide
