'use client'
import React, { useCallback, useEffect, useRef, useState, useMemo } from 'react'
import Search from '../Icons/Search'
import Genre from '../Icons/Genre'
import Link from 'next/link'
import TeacherCard from '../TeacherCard/TeacherCard'
import { getTeachers } from '@/lib/actions/account.actions'
import TeacherCardSkeleton from '../Skeletons/TeacherCardSkeleton'
import { toast } from 'react-toastify'
import InfiniteScroll from 'react-infinite-scroll-component'
import CustomDropDown from '../CustomComponents/CustomDropDown/CustomDropDown'
import { debounce } from '@/utils/utils'
import { PAGINATION_LIMIT } from '@/utils/constants'

const TeacherListing = ({ genres }: { genres: any }) => {
  const [teachers, setTeachers] = useState<any[]>([])
  const [selectedGenre, setSelectedGenre] = useState<string[]>([])
  const [hasMore, setHasMore] = useState(true)
  const [search, setSearch] = useState('')
  const offset = useRef<number>(0)

  useEffect(() => {
    setTeachers([])
    offset.current = 0
    setHasMore(true)
    fetchTeachers()
  }, [selectedGenre, search])

  const fetchTeachers = useCallback(async () => {
    try {
      const response = await getTeachers(
        search,
        selectedGenre,
        PAGINATION_LIMIT,
        offset.current,
      )
      const moreTeachers = response?.data?.data || []

      if (moreTeachers.length > 0) {
        setTeachers((prev: any) => {
          const updatedTeachers = [...prev, ...moreTeachers]
          offset.current += PAGINATION_LIMIT

          if (response?.data?.total <= updatedTeachers.length) {
            setHasMore(false)
          }

          return updatedTeachers
        })
      } else {
        setHasMore(false)
      }
    } catch (error) {
      toast.error('Something went wrong! ' + error, { autoClose: 1500 })
    }
  }, [search, selectedGenre])

  const handleFilter = useCallback((value: string, filterType: string) => {
    if (filterType === 'Genre') {
      setSelectedGenre((prev) =>
        prev.includes(value)
          ? prev.filter((item) => item !== value)
          : [...prev, value],
      )
    }
  }, [])

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearch(e.target.value)
  }

  const debounceSearch = useMemo(() => debounce(handleSearch, 1000), [])

  return (
    <div>
      <div className="bg-white px-7 pb-7 flex items-center justify-between flex-col gap-3 md:flex-row">
        <div className="relative">
          <input
            type="text"
            placeholder="Search by Teacher’s Name"
            className="h-12 p-5 rounded-xl border text-xs text-color-grey-1 w-full md:w-80"
            onChange={debounceSearch}
          />
          <div className="absolute right-5" style={{ top: 'calc(50% - 7px)' }}>
            <Search width={14.25} height={14.25} color={'#9C9CA4'} />
          </div>
        </div>
        <div className="flex items-center justify-between gap-4 flex-col md:flex-row">
          <CustomDropDown
            icon={<Genre />}
            title={'Genre'}
            options={genres}
            column={2}
            handleOptionClick={handleFilter}
            selectedList={selectedGenre}
            isTitleType={true}
          />
        </div>
      </div>
      <InfiniteScroll
        dataLength={teachers.length}
        next={fetchTeachers}
        hasMore={hasMore}
        loader={
          <div className="w-full grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 p-7">
            <TeacherCardSkeleton />
            <TeacherCardSkeleton />
            <TeacherCardSkeleton />
          </div>
        }
        endMessage={
          <div className="flex items-center font-bold justify-center mb-10 text-color-grey-1">
            No more teachers to show
          </div>
        }
        scrollableTarget="scrollableDiv"
      >
        <div className="p-7 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
          {teachers.map((item: any) => (
            <Link href={`/teachers/${item?.Id}`} key={item?.Id}>
              <TeacherCard data={item} />
            </Link>
          ))}
        </div>
      </InfiniteScroll>
    </div>
  )
}

export default TeacherListing
