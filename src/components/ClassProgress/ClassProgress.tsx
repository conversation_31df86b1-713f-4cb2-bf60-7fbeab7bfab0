import React from 'react'

interface ClassProgressProps {
  completedCount: number
  totalCount: number
  className?: string
}

const ClassProgress: React.FC<ClassProgressProps> = ({
  completedCount,
  totalCount,
  className = '',
}) => {
  const progressPercentage =
    totalCount > 0 ? (completedCount / totalCount) * 100 : 0

  const safeCompletedCount = Math.max(0, Math.min(completedCount, totalCount))
  const safeProgressPercentage = Math.max(0, Math.min(progressPercentage, 100))

  return (
    <div className={`flex items-center justify-between w-full ${className}`}>
      <div className="flex-1 bg-color-grey rounded-full h-3.5 mr-4 overflow-hidden">
        <div
          className="bg-color-yellow h-full rounded-full transition-all duration-300 ease-in-out"
          style={{ width: `${safeProgressPercentage}%` }}
        />
      </div>
      <div className="text-color-grey-1 text-sm font-medium whitespace-nowrap">
        {safeCompletedCount} out of {totalCount} classes completed
      </div>
    </div>
  )
}

export default ClassProgress
