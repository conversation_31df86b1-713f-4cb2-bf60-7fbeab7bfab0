/* Loader.css */

@keyframes l3 {
  to {
    transform: rotate(1turn);
  }
}

.loader {
  width: 40px;
  padding: 6px;
  border-radius: 50%;
  background-color: #FFBB54;
  aspect-ratio: 1;
  background: #FFBB54;
  --_m:
    conic-gradient(#0000 10%, #000),
    linear-gradient(#000 0 0) content-box;
  -webkit-mask: var(--_m);
  mask: var(--_m);
  -webkit-mask-composite: source-out;
  mask-composite: subtract;
  animation: l3 1s infinite linear;
}