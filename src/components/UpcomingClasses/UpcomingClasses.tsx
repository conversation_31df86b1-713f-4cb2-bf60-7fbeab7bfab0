import { formatSalesforceTime } from '@/utils/utils'
import Link from 'next/link'

interface UpcomingClassesProps {
  userMeetings: any
  user: any
  title: string
  description: string
}
const ClockIcon: React.FC = () => (
  <svg
    className="w-4 h-4 mr-2"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
  >
    <circle cx="12" cy="12" r="10" strokeWidth="2" />
    <path d="M12 6v6l4 2" strokeWidth="2" strokeLinecap="round" />
  </svg>
)

const UpcomingClasses: React.FC<UpcomingClassesProps> = ({
  userMeetings,
  user,
  title,
  description,
}) => {
  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-2">
        <h1 className="text-2xl font-semibold leading-9 tracking-normal text-color-black">
          {title}
        </h1>
        <p className="text-sm font-normal leading-6 tracking-normal text-color-grey-13">
          {description}
        </p>
      </div>

      <div className="bg-neutral-white rounded-md">
        {userMeetings?.data?.length > 0 && (
          <div
            className={`flex flex-col ${
              userMeetings.data.length > 3
                ? 'max-h-96 overflow-y-auto pr-2'
                : ''
            }`}
            style={{ scrollbarWidth: 'none' }}
          >
            {userMeetings.data.map((session: any, idx: number) => {
              const formattedDate = new Date(
                session.startDate,
              ).toLocaleDateString('en-US', {
                weekday: 'long',
                month: 'long',
                day: 'numeric',
              })

              return (
                <Link
                  key={idx}
                  href={`/classes/${session.classId}`}
                  className="pt-5 pl-5 pr-5 flex flex-col gap-2 hover:bg-gray-50 transition rounded-md"
                >
                  <div className="flex items-center text-sm text-gray-500">
                    <ClockIcon />
                    <span className="ml-1 mr-2">{formattedDate}</span> |{' '}
                    <span className="ml-2">
                      {formatSalesforceTime(
                        session?.startTime,
                        session?.endTime,
                      )}
                    </span>
                  </div>
                  <h2 className="text-lg font-semibold text-gray-900">
                    {session.classTitle}
                  </h2>
                  <p className="text-sm text-gray-500">
                    {session.classCategory}
                  </p>
                  <div className="border-b mt-2 border-color-grey-10" />
                </Link>
              )
            })}
          </div>
        )}
      </div>
    </div>
  )
}

export default UpcomingClasses
