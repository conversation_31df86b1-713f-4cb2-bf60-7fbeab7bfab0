import Image from 'next/image'
import Link from 'next/link'
import React from 'react'

// Define the type for the event data
interface EventData {
  Id: string
  Title__c: string
  Description__c: string
  Start_Date__c: string
  End_Date__c: string
  Banner_Image__c: string
  Meeting_Location__c: string
  Meetings_Summary__c: string
}

interface ClubCardProps {
  clubData: EventData[]
}

const ClubCard: React.FC<ClubCardProps> = ({ clubData }) => {
  return (
    <>
      {clubData.map((item) => (
        <div
          className="w-full bg-white rounded-2xl px-10 py-5 mt-4"
          key={item?.Id}
        >
          <Link href={`/clubs/${item?.Id}`}>
            <div className="flex items-center justify-between w-full gap-5 py-5">
              {item?.Banner_Image__c && (
                <div className="relative aspect-video rounded-xl h-40 overflow-hidden">
                  <Image
                    src={item?.Banner_Image__c}
                    alt="User avatar"
                    fill
                    className="object-cover"
                    priority
                  />
                </div>
              )}
              <div className="flex flex-col w-full">
                <div className="flex gap-3">
                  {/* {item?.Banner_Image__c && (
                    <Image
                      width={330}
                      height={120}
                      src={item?.Banner_Image__c}
                      alt="Event banner"
                      className="rounded-xl h-20 md:h-52 w-20 md:w-52 object-cover block md:hidden"
                    />
                  )} */}
                  <div>
                    <h1 className="font-extrabold text-lg md:text-2xl">
                      {item?.Title__c}
                    </h1>
                    <h3 className="text-color-grey-3 font-semibold text-xs md:text-sm">
                      {item?.Meeting_Location__c}
                    </h3>
                  </div>
                </div>
                <div className="w-6 bg-color-grey-2 border mt-4 hidden md:block"></div>
                <p className="font-medium mt-2 text-sm">
                  {item?.Meetings_Summary__c}
                </p>
                <h1 className="text-color-blue-1 font-semibold text-sm">
                  View Club Details
                </h1>
              </div>
            </div>
          </Link>
        </div>
      ))}
      {clubData.length === 0 && (
        <div className="w-full flex items-center justify-center h-52">
          <h1 className="text-xl font-medium">No clubs found</h1>
        </div>
      )}
    </>
  )
}

export default ClubCard
