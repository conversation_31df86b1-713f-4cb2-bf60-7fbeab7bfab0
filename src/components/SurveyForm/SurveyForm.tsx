'use client'

import React, { useState } from 'react'
import CustomButton from '../CustomComponents/CustomButton/CustomButton'
import { toast } from 'react-toastify'

interface SurveyFormData {
  session: string
  classDetails: string
  instructor: string
  classDescriptionAccurate: string
  instructorRatings: {
    professional: number
    punctual: number
    communication: number
    knowledgeable: number
    organized: number
    expertInField: number
  }
  classImpact: {
    creativity: number
    confidence: number
    learningOutcome: number
    writingImprovement: number
  }
  instructorNotes: string
  hearAboutClass: string
  futureParticipation: number
  museNotes: string
  zipCode: string
  age: string
  newsletterFrequency: string
  additionalNotes: string
}

const SurveyForm = ({ classData }: { classData: any }) => {
  const [formData, setFormData] = useState<SurveyFormData>({
    session: '',
    classDetails: '',
    instructor: '',
    classDescriptionAccurate: '',
    instructorRatings: {
      professional: 0,
      punctual: 0,
      communication: 0,
      knowledgeable: 0,
      organized: 0,
      expertInField: 0,
    },
    classImpact: {
      creativity: 0,
      confidence: 0,
      learningOutcome: 0,
      writingImprovement: 0,
    },
    instructorNotes: '',
    hearAboutClass: '',
    futureParticipation: 0,
    museNotes: '',
    zipCode: '',
    age: '',
    newsletterFrequency: '',
    additionalNotes: '',
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Basic validation for required fields
    if (
      !formData.session ||
      !formData.classDetails ||
      !formData.instructor ||
      !formData.classDescriptionAccurate ||
      !formData.instructorRatings.professional ||
      !formData.instructorRatings.punctual ||
      !formData.instructorRatings.communication ||
      !formData.instructorRatings.knowledgeable ||
      !formData.instructorRatings.organized ||
      !formData.instructorRatings.expertInField ||
      !formData.classImpact.creativity ||
      !formData.classImpact.confidence ||
      !formData.classImpact.learningOutcome ||
      !formData.classImpact.writingImprovement ||
      !formData.hearAboutClass ||
      !formData.futureParticipation ||
      !formData.zipCode ||
      !formData.age ||
      !formData.newsletterFrequency
    ) {
      toast.error('Please fill out all required fields marked with *.')
      return
    }
  }

  const renderRatingScale = (
    label: string,
    value: number,
    onChange: (val: number) => void,
    required: boolean = false,
  ) => (
    <div className="mb-4">
      <label className="block text-gray-700 mb-2">
        {label} {required && <span className="text-red-500">*</span>}
      </label>
      <div className="flex justify-between bg-color-grey-2 px-4 py-2 rounded">
        {[1, 2, 3, 4, 5].map((rating) => (
          <label key={rating} className="inline-flex items-center">
            <input
              type="radio"
              className="form-radio"
              name={label}
              value={rating}
              checked={value === rating}
              onChange={() => onChange(rating)}
            />
            <span className="ml-2">{rating}</span>
          </label>
        ))}
      </div>
    </div>
  )

  return (
    <div className="container mx-auto max-w-4xl mt-10">
      <form onSubmit={handleSubmit} className="rounded px-8 pt-6 pb-8 mb-4">
        <h1 className="text-2xl font-bold mb-6">(Survey) Muse Class Survey</h1>

        {/* Session Selection */}
        <div className="mb-4">
          <label className="block text-gray-700 font-bold mb-2">
            In which session was your class, workshop, or seminar?{' '}
            <span className="text-red-500">*</span>
          </label>
          <div className="flex">
            {['2025 Session 1 Winter', '2025 Session 2 Spring'].map(
              (session) => (
                <label key={session} className="inline-flex items-center mr-4">
                  <input
                    type="radio"
                    className="form-radio"
                    name="session"
                    value={session}
                    checked={formData.session === session}
                    onChange={() => setFormData({ ...formData, session })}
                  />
                  <span className="ml-2">{session}</span>
                </label>
              ),
            )}
          </div>
        </div>

        {/* Class Details */}
        <div className="mb-4">
          <label className="block text-gray-700 font-bold mb-2">
            What class, workshop, or seminar did you take at The Muse? Include
            day of the week and time. <span className="text-red-500">*</span>
          </label>
          <textarea
            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            rows={4}
            value={classData?.Title__c || formData.classDetails}
            onChange={(e) =>
              setFormData({ ...formData, classDetails: e.target.value })
            }
            readOnly={!!classData}
          />
        </div>

        {/* Instructor */}
        <div className="mb-4">
          <label className="block text-gray-700 font-bold mb-2">
            Who was your instructor? <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            value={formData.instructor}
            onChange={(e) =>
              setFormData({ ...formData, instructor: e.target.value })
            }
          />
        </div>

        {/* Class Description Accuracy */}
        <div className="mb-4">
          <label className="block text-gray-700 font-bold mb-2">
            Was the class description accurate?{' '}
            <span className="text-red-500">*</span>
          </label>
          <div className="flex">
            {['Yes', 'No'].map((option) => (
              <label key={option} className="inline-flex items-center mr-4">
                <input
                  type="radio"
                  className="form-radio"
                  name="classDescriptionAccurate"
                  value={option}
                  checked={formData.classDescriptionAccurate === option}
                  onChange={() =>
                    setFormData({
                      ...formData,
                      classDescriptionAccurate: option,
                    })
                  }
                />
                <span className="ml-2">{option}</span>
              </label>
            ))}
          </div>
        </div>

        {/* Instructor Ratings */}
        <div className="mb-6">
          <h2 className="text-xl font-semibold mb-4">
            How did your instructor do?
          </h2>
          {renderRatingScale(
            'My instructor was professional',
            formData.instructorRatings.professional,
            (val) =>
              setFormData({
                ...formData,
                instructorRatings: {
                  ...formData.instructorRatings,
                  professional: val,
                },
              }),
            true,
          )}
          {renderRatingScale(
            'My instructor was punctual',
            formData.instructorRatings.punctual,
            (val) =>
              setFormData({
                ...formData,
                instructorRatings: {
                  ...formData.instructorRatings,
                  punctual: val,
                },
              }),
            true,
          )}
          {renderRatingScale(
            'My instructor was a good communicator',
            formData.instructorRatings.communication,
            (val) =>
              setFormData({
                ...formData,
                instructorRatings: {
                  ...formData.instructorRatings,
                  communication: val,
                },
              }),
            true,
          )}
          {renderRatingScale(
            'My instructor was very knowledgeable',
            formData.instructorRatings.knowledgeable,
            (val) =>
              setFormData({
                ...formData,
                instructorRatings: {
                  ...formData.instructorRatings,
                  knowledgeable: val,
                },
              }),
            true,
          )}
          {renderRatingScale(
            'My instructor was very organized',
            formData.instructorRatings.organized,
            (val) =>
              setFormData({
                ...formData,
                instructorRatings: {
                  ...formData.instructorRatings,
                  organized: val,
                },
              }),
            true,
          )}
          {renderRatingScale(
            'My instructor was an expert in the field',
            formData.instructorRatings.expertInField,
            (val) =>
              setFormData({
                ...formData,
                instructorRatings: {
                  ...formData.instructorRatings,
                  expertInField: val,
                },
              }),
            true,
          )}
        </div>

        {/* Class Impact */}
        <div className="mb-6">
          <h2 className="text-xl font-semibold mb-4">
            What did the class do for you?
          </h2>
          {renderRatingScale(
            'This class sparked my creativity',
            formData.classImpact.creativity,
            (val) =>
              setFormData({
                ...formData,
                classImpact: { ...formData.classImpact, creativity: val },
              }),
            true,
          )}
          {renderRatingScale(
            'This class boosted my confidence',
            formData.classImpact.confidence,
            (val) =>
              setFormData({
                ...formData,
                classImpact: { ...formData.classImpact, confidence: val },
              }),
            true,
          )}
          {renderRatingScale(
            'This class taught me a lot',
            formData.classImpact.learningOutcome,
            (val) =>
              setFormData({
                ...formData,
                classImpact: { ...formData.classImpact, learningOutcome: val },
              }),
            true,
          )}
          {renderRatingScale(
            'This class will make me a better writer',
            formData.classImpact.writingImprovement,
            (val) =>
              setFormData({
                ...formData,
                classImpact: {
                  ...formData.classImpact,
                  writingImprovement: val,
                },
              }),
            true,
          )}
        </div>

        {/* Instructor Notes */}
        <div className="mb-4">
          <label className="block text-gray-700 font-bold mb-2">
            Notes or Comments for your Instructor
          </label>
          <textarea
            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            rows={4}
            value={formData.instructorNotes}
            onChange={(e) =>
              setFormData({ ...formData, instructorNotes: e.target.value })
            }
            placeholder="Feedback for your instructor on the class. Compliments and constructive criticism welcome."
          />
        </div>

        {/* How Did You Hear About Class */}
        <div className="mb-4">
          <label className="block text-gray-700 font-bold mb-2">
            Where did you hear about this class?{' '}
            <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            value={formData.hearAboutClass}
            onChange={(e) =>
              setFormData({ ...formData, hearAboutClass: e.target.value })
            }
          />
        </div>

        {/* Future Participation */}
        <div className="mb-4">
          <label className="block text-gray-700 font-bold mb-2">
            On a scale from 1 to 5, how likely are you to participate in another
            class or event at The Muse? <span className="text-red-500">*</span>
          </label>
          <div className="flex justify-between">
            {[1, 2, 3, 4, 5].map((rating) => (
              <label key={rating} className="inline-flex items-center">
                <input
                  type="radio"
                  className="form-radio"
                  name="futureParticipation"
                  value={rating}
                  checked={formData.futureParticipation === rating}
                  onChange={() =>
                    setFormData({ ...formData, futureParticipation: rating })
                  }
                />
                <span className="ml-2">{rating}</span>
              </label>
            ))}
          </div>
          <p className="text-sm text-gray-600 mt-2">
            1-Not at all likely, 2-Not very likely, 3-Maybe, 4-Probably,
            5-Definitely!
          </p>
        </div>

        {/* Muse Notes */}
        <div className="mb-4">
          <label className="block text-gray-700 font-bold mb-2">
            Notes or Comments for the Muse
          </label>
          <textarea
            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            rows={4}
            value={formData.museNotes}
            onChange={(e) =>
              setFormData({ ...formData, museNotes: e.target.value })
            }
            placeholder="Feedback for the Muse. What do you like about the Muse? What can we do better? What class or event would you like us to offer?"
          />
        </div>

        {/* Demographic Information */}
        <div className="mb-4">
          <label className="block text-gray-700 font-bold mb-2">
            Your zip code <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            value={formData.zipCode}
            onChange={(e) =>
              setFormData({ ...formData, zipCode: e.target.value })
            }
          />
        </div>

        <div className="mb-4">
          <label className="block text-gray-700 font-bold mb-2">
            Your age <span className="text-red-500">*</span>
          </label>
          <div className="grid grid-cols-2 gap-2">
            {[
              'Under 12',
              '12-17',
              '18-24',
              '25-39',
              '40-54',
              '55-66',
              '67-84',
              '85 + better',
            ].map((ageGroup) => (
              <label key={ageGroup} className="inline-flex items-center">
                <input
                  type="radio"
                  className="form-radio"
                  name="age"
                  value={ageGroup}
                  checked={formData.age === ageGroup}
                  onChange={() => setFormData({ ...formData, age: ageGroup })}
                />
                <span className="ml-2">{ageGroup}</span>
              </label>
            ))}
          </div>
        </div>

        {/* Newsletter */}
        <div className="mb-4">
          <label className="block text-gray-700 font-bold mb-2">
            Do you read the Muse Newsletter?{' '}
            <span className="text-red-500">*</span>
          </label>
          {[
            'Yes, always!',
            'No',
            'Sometimes',
            'Rarely',
            "What's the Muse Newsletter?",
          ].map((option) => (
            <label key={option} className="block">
              <input
                type="radio"
                className="form-radio mr-2"
                name="newsletterFrequency"
                value={option}
                checked={formData.newsletterFrequency === option}
                onChange={() =>
                  setFormData({ ...formData, newsletterFrequency: option })
                }
              />
              {option}
            </label>
          ))}
        </div>

        {/* Additional Notes */}
        <div className="mb-4">
          <label className="block text-gray-700 font-bold mb-2">
            Additional Notes
          </label>
          <textarea
            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            rows={4}
            value={formData.additionalNotes}
            onChange={(e) =>
              setFormData({ ...formData, additionalNotes: e.target.value })
            }
            placeholder="Comments, questions, concerns, general musings, favorite quotes, opinion on pancakes vs. waffles... we love hearing your thoughts!"
          />
        </div>

        {/* Submit Button */}
        <div className="flex items-center justify-center">
          <CustomButton
            isLoading={false}
            title="Submit"
            onClick={() => {}}
            height={12}
          />
        </div>
      </form>
    </div>
  )
}

export default SurveyForm
