'use client'

import Link from 'next/link'
import { Clock } from 'lucide-react'
import { formatSalesforceTime } from '@/utils/utils'
import { ROLES } from '@/types/enum'

const ClassEventCard = ({
  event,
  index,
  isParentDashboard = false,
  isDashboard = false,
  role,
}: {
  event: any
  index: number
  isParentDashboard?: boolean
  isDashboard?: boolean
  role?: any
}) => {
  const dateStr = event?.startDate
  const date = new Date(dateStr)

  const formattedDate = date.toLocaleDateString('en-US', {
    month: 'long',
    day: 'numeric',
    year: 'numeric',
  })
  return (
    <Link href={`/classes/${event.classId}`}>
      <div className="flex flex-col gap-1 text-sm text-gray-600">
        <div className="flex items-center gap-2">
          <Clock className="w-4 h-4 mt-0.5" />
          <span className="text-sm font-normal text-color-grey-1">
            {formatSalesforceTime(event.startTime, event.endTime)}
          </span>
          {isDashboard && role !== ROLES.Parent && (
            <span className="text-sm font-normal text-color-grey-1 ml-3">
              {formattedDate}
            </span>
          )}
        </div>
        <div className="text-[18px] font-bold mt-1">{event.classTitle}</div>
        <div className="flex flex-wrap gap-3 text-[14px] text-color-black mt-2">
          {event?.studentName &&
            (isParentDashboard || role == ROLES.Parent) && (
              <span className="flex items-center justify-center bg-color-grey-17 rounded-2xl p-2">
                {' '}
                {event?.studentName}
              </span>
            )}
          {event?.classType && (
            <span className="flex items-center justify-center bg-color-grey-17 rounded-2xl p-2">
              {' '}
              {event?.classType}
            </span>
          )}
          {event?.classCategory && (
            <span className="flex items-center justify-center bg-color-grey-17 rounded-2xl p-2">
              {' '}
              {event?.classCategory}
            </span>
          )}
        </div>
        <div className="mt-2 border-t border-[3px] border-color-grey-10" />
      </div>
    </Link>
  )
}

export default ClassEventCard
