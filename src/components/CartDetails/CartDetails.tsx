'use client'
import CartProduct from '@/components/CartItem/CartProduct'
import { getCart } from '@/lib/actions/cart.actions'
import React, { useEffect, useState } from 'react'
import { useApi } from '@/hooks/useApi'
import CartPurchase from '../CartPurchase/CartPurchase'
import RightColumnHoc from '../RightColumnHoc/RightColumnHoc'
import CartItemSkeleton from '../Skeletons/CartItemSkeleton'
import { setCartCount, useDispatch } from '@/lib/redux'

const CartDetails = ({
  userData,
  parentChildren,
}: {
  userData: any
  parentChildren: any[]
}) => {
  const [cartData, setCartData] = useState<any>([])
  const [totalAmount, setTotalAmount] = useState<number>(0)
  const [cartResponse, fetchCart] = useApi((token: string) =>
    getCart(token, userData?.id),
  )
  const dispatch = useDispatch()

  useEffect(() => {
    ;(async () => {
      await fetchCart()
    })()
  }, [])

  useEffect(() => {
    if (cartResponse.isSuccess) {
      setCartData(cartResponse.data?.data?.cartItems || [])
      dispatch(setCartCount(cartResponse.data?.data?.cartItems?.length || 0))         
    }
  }, [cartResponse])

  return (
    <div className="flex flex-col md:grid  md:grid-cols-10 gap-6 md:p-6">
      <div className="bg-white w-full rounded-2xl col-span-7 h-max">
        <div className="grid grid-cols-[70%,15%,15%] py-2 md:py-5 px-4 md:px-10 font-bold text-lg">
          <h1>Products</h1>
          <h2 className="hidden md:flex w-full justify-center">Unit Price</h2>
          <h2 className="hidden md:flex w-full justify-center">Sub Total</h2>
        </div>
        <div className="border-b-2 border-color-grey-2"></div>
        {cartResponse.isFetching && (
          <div>
            {[...Array(3)].map((_, index) => (
              <CartItemSkeleton key={index} showBorder={index < 2} />
            ))}
          </div>
        )}
        {!cartResponse.isFetching &&
          cartData?.map((item: any, index: number) => (
            <CartProduct
              item={item}
              key={item?.Id}
              setTotalAmount={setTotalAmount}
              setCartData={setCartData}
            />
          ))}
        {!cartResponse.isFetching && cartData.length == 0 && (
          <div className="h-96 flex justify-center items-center">
            <h2 className="font-medium text-4 text-color-grey-1">
              No item found
            </h2>
          </div>
        )}
      </div>
      <div className="col-span-3">
        <RightColumnHoc>
          <CartPurchase
            setCartData={setCartData}
            totalAmount={totalAmount}
            cartData={cartData}
            parentChildren={parentChildren}
            setTotalAmount={setTotalAmount}
          />
        </RightColumnHoc>
      </div>
    </div>
  )
}

export default CartDetails
