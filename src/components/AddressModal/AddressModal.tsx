import React from 'react'

interface AddressModalProps {
  room?: string
  venueName?: string
  address?: {
    street?: string
    city?: string
    stateCode?: string
    country?: string
  }
  onClose: () => void
}

const AddressModal: React.FC<AddressModalProps> = ({
  room,
  venueName,
  address,
  onClose,
}) => {
  return (
    <div className="py-5 px-6 w-full max-w-lg bg-white rounded-2xl shadow-lg border border-color-grey-2">
      <h2 className="text-xl font-bold mb-6 text-color-black">
        Class Location Details
      </h2>

      <div className="overflow-hidden rounded-lg border border-color-grey-2">
        <table className="w-full">
          <tbody>
            <tr className="border-b border-color-grey-2">
              <td className="px-4 py-3 font-semibold text-color-grey-1 bg-color-grey-10 w-1/3">
                Room
              </td>
              <td className="px-4 py-3 text-color-black">{room || 'N/A'}</td>
            </tr>
            <tr className="border-b border-color-grey-2">
              <td className="px-4 py-3 font-semibold text-color-grey-1 bg-color-grey-10 w-1/3">
                Venue
              </td>
              <td className="px-4 py-3 text-color-black">
                {venueName || 'N/A'}
              </td>
            </tr>
            <tr>
              <td className="px-4 py-3 font-semibold text-color-grey-1 bg-color-grey-10 w-1/3 align-top">
                Address
              </td>
              <td className="px-4 py-3 text-color-black">
                {address?.street && (
                  <div className="mb-1">{address.street}</div>
                )}
                <div>
                  {[address?.city, address?.stateCode, address?.country]
                    .filter(Boolean)
                    .join(', ')}
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <div className="flex justify-end mt-6">
        <button
          className="px-6 py-2 bg-color-black text-white rounded-xl hover:bg-color-grey-3 transition-colors font-semibold"
          onClick={onClose}
        >
          Close
        </button>
      </div>
    </div>
  )
}

export default AddressModal
