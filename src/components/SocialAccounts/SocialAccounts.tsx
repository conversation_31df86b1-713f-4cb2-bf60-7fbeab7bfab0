'use client'

import Link from 'next/link'

interface SocialLink {
  name: string
  icon: React.ReactNode
  url?: string
}

interface SocialLinksProps {
  links: SocialLink[]
  name?: string
}

const SocialLinks: React.FC<SocialLinksProps> = ({ links, name }) => {
  const hasLinks = links?.some(({ url }) => url)

  if (!hasLinks) return null

  return (
    <div className="flex flex-col gap-3">
      {name && (
        <h2 className="text-2xl font-semibold leading-9 tracking-normal text-color-black">
          Connect with {name}
        </h2>
      )}
      <div className="flex gap-3 flex-wrap">
        {links?.map(({ name, icon, url }) =>
          url ? (
            <Link
              key={name}
              href={url}
              target="_blank"
              rel="noopener noreferrer"
              title={name}
              className="w-[44px] h-[44px] rounded-lg flex items-center justify-center shadow inline-flex"
              passHref
            >
              {icon}
            </Link>
          ) : null,
        )}
      </div>
    </div>
  )
}

export default SocialLinks
