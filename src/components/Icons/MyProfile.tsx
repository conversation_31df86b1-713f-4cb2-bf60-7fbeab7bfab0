const MyProfile = ({ isActive = false }: { isActive: boolean }) => {
  return (
    <svg
      width="18"
      height="18"
      viewBox="0 0 18 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M9.09 9.58437C9.0375 9.57687 8.97 9.57687 8.91 9.58437C7.59 9.53937 6.54 8.45938 6.54 7.13188C6.54 5.77438 7.635 4.67188 9 4.67188C10.3575 4.67188 11.46 5.77438 11.46 7.13188C11.4525 8.45938 10.41 9.53937 9.09 9.58437Z"
        stroke={isActive ? '#141522' : '#9C9CA4'}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M14.055 14.5349C12.72 15.7574 10.95 16.4999 9 16.4999C7.05 16.4999 5.28 15.7574 3.945 14.5349C4.02 13.8299 4.47 13.1399 5.2725 12.5999C7.3275 11.2349 10.6875 11.2349 12.7275 12.5999C13.53 13.1399 13.98 13.8299 14.055 14.5349Z"
        stroke={isActive ? '#141522' : '#9C9CA4'}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M9 16.5C13.1421 16.5 16.5 13.1421 16.5 9C16.5 4.85786 13.1421 1.5 9 1.5C4.85786 1.5 1.5 4.85786 1.5 9C1.5 13.1421 4.85786 16.5 9 16.5Z"
        stroke={isActive ? '#141522' : '#9C9CA4'}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export default MyProfile
