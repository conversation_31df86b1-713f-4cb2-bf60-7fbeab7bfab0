const InstagramSVG = () => {
  return (
    <svg
      width="22"
      height="22"
      viewBox="0 0 22 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_789_8558)">
        <path
          d="M11 14.2812C12.8122 14.2812 14.2812 12.8122 14.2812 11C14.2812 9.18782 12.8122 7.71875 11 7.71875C9.18782 7.71875 7.71875 9.18782 7.71875 11C7.71875 12.8122 9.18782 14.2812 11 14.2812Z"
          stroke="#141522"
          strokeWidth="1.3125"
          strokeMiterlimit="10"
        />
        <path
          d="M14.9375 3.125H7.0625C4.88788 3.125 3.125 4.88788 3.125 7.0625V14.9375C3.125 17.1121 4.88788 18.875 7.0625 18.875H14.9375C17.1121 18.875 18.875 17.1121 18.875 14.9375V7.0625C18.875 4.88788 17.1121 3.125 14.9375 3.125Z"
          stroke="#141522"
          strokeWidth="1.3125"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M15.2656 7.71875C15.8093 7.71875 16.25 7.27803 16.25 6.73438C16.25 6.19072 15.8093 5.75 15.2656 5.75C14.722 5.75 14.2812 6.19072 14.2812 6.73438C14.2812 7.27803 14.722 7.71875 15.2656 7.71875Z"
          fill="#141522"
        />
      </g>
      <defs>
        <clipPath id="clip0_789_8558">
          <rect
            width="21"
            height="21"
            fill="white"
            transform="translate(0.5 0.5)"
          />
        </clipPath>
      </defs>
    </svg>
  )
}

export default InstagramSVG
