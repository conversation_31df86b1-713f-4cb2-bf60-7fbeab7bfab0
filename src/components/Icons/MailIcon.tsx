const MailIcon = () => {
  return (
    <svg
      width="86"
      height="63"
      viewBox="0 0 86 63"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g filter="url(#filter0_dddd_3223_40289)">
        <rect
          width="72"
          height="48"
          rx="4"
          transform="matrix(1 0 0 -1 7 50.4609)"
          fill="url(#paint0_linear_3223_40289)"
        />
        <rect
          x="-0.25"
          y="0.25"
          width="72.5"
          height="48.5"
          rx="4.25"
          transform="matrix(1 0 0 -1 7 50.9609)"
          stroke="url(#paint1_linear_3223_40289)"
          strokeWidth="0.5"
        />
      </g>
      <mask
        id="mask0_3223_40289"
        style={{ maskType: 'alpha' }}
        maskUnits="userSpaceOnUse"
        x="7"
        y="2"
        width="72"
        height="49"
      >
        <rect x="7" y="2.46484" width="72" height="48" rx="4" fill="black" />
      </mask>
      <g mask="url(#mask0_3223_40289)">
        <g opacity="0.4">
          <g filter="url(#filter1_f_3223_40289)">
            <path
              d="M8.49805 50.4609L77.498 50.4609L47.2756 23.3045C45.7635 21.9458 45.0074 21.2665 44.1526 21.0088C43.3996 20.7818 42.5965 20.7818 41.8435 21.0088C40.9887 21.2665 40.2326 21.9458 38.7205 23.3045L8.49805 50.4609Z"
              fill="#334155"
              fillOpacity="0.4"
            />
          </g>
          <g filter="url(#filter2_f_3223_40289)">
            <path
              d="M12.498 50.4609L73.498 50.4609L47.7621 21.7717C46.1128 19.9331 45.2882 19.0139 44.3163 18.6746C43.4627 18.3767 42.5334 18.3767 41.6798 18.6746C40.7079 19.0139 39.8833 19.9331 38.234 21.7717L12.498 50.4609Z"
              fill="#334155"
              fillOpacity="0.2"
            />
          </g>
          <g filter="url(#filter3_f_3223_40289)">
            <path
              d="M8.49805 50.4609L77.498 50.4609L47.6789 18.481C46.0519 16.7361 45.2383 15.8636 44.2872 15.5398C43.4513 15.2552 42.5448 15.2552 41.7089 15.5398C40.7577 15.8636 39.9442 16.7361 38.3172 18.481L8.49805 50.4609Z"
              fill="#334155"
              fillOpacity="0.1"
            />
          </g>
        </g>
        <g filter="url(#filter4_i_3223_40289)">
          <path
            d="M6.99805 50.4609L78.998 50.4609L47.1742 23.057C45.6925 21.7811 44.9517 21.1431 44.1202 20.9001C43.3874 20.6859 42.6087 20.6859 41.8759 20.9001C41.0444 21.1431 40.3035 21.7811 38.8219 23.057L6.99805 50.4609Z"
            fill="url(#paint2_linear_3223_40289)"
          />
        </g>
        <path
          d="M7.74805 49.4609L38.8253 22.7918C40.306 21.5211 41.0464 20.8857 41.8768 20.644C42.6086 20.4309 43.3861 20.4313 44.1177 20.6452C44.9478 20.888 45.6874 21.5242 47.1667 22.7965L78.748 49.9609"
          stroke="#475569"
          strokeOpacity="0.06"
          strokeWidth="0.5"
        />
        <g style={{ mixBlendMode: 'multiply' }} opacity="0.7">
          <g filter="url(#filter5_f_3223_40289)">
            <path
              d="M8.49805 2.47266L77.498 2.47266L47.2756 29.6291C45.7635 30.9878 45.0074 31.6671 44.1526 31.9248C43.3996 32.1518 42.5965 32.1518 41.8435 31.9248C40.9887 31.6671 40.2326 30.9878 38.7205 29.6291L8.49805 2.47266Z"
              fill="#334155"
              fillOpacity="0.4"
            />
          </g>
          <g filter="url(#filter6_f_3223_40289)">
            <path
              d="M12.498 2.47266L73.498 2.47266L47.7621 31.1619C46.1128 33.0004 45.2882 33.9197 44.3163 34.259C43.4627 34.5569 42.5334 34.5569 41.6798 34.259C40.7079 33.9197 39.8833 33.0004 38.234 31.1619L12.498 2.47266Z"
              fill="#334155"
              fillOpacity="0.2"
            />
          </g>
          <g filter="url(#filter7_f_3223_40289)">
            <path
              d="M8.49805 2.47266L77.498 2.47266L47.6789 34.4526C46.0519 36.1975 45.2383 37.07 44.2872 37.3938C43.4513 37.6784 42.5448 37.6784 41.7089 37.3938C40.7577 37.07 39.9442 36.1975 38.3172 34.4526L8.49805 2.47266Z"
              fill="#334155"
              fillOpacity="0.1"
            />
          </g>
          <g filter="url(#filter8_f_3223_40289)">
            <path
              d="M10.998 2.47266L74.998 2.47266L45.2121 35.051C44.0227 36.3519 41.9734 36.3519 40.784 35.051L10.998 2.47266Z"
              fill="#334155"
              fillOpacity="0.05"
            />
          </g>
          <g filter="url(#filter9_f_3223_40289)">
            <path
              d="M7.99805 1.97266L77.998 1.97266L45.0394 32.5771C43.8884 33.6459 42.1077 33.6459 40.9567 32.5771L7.99805 1.97266Z"
              fill="#334155"
              fillOpacity="0.1"
            />
          </g>
        </g>
        <path
          d="M7.74805 3.46484L38.8253 30.134C40.306 31.4047 41.0464 32.04 41.8768 32.2818C42.6086 32.4949 43.3861 32.4945 44.1177 32.2805C44.9478 32.0378 45.6874 31.4016 47.1667 30.1292L78.748 2.96484"
          stroke="#475569"
          strokeOpacity="0.06"
          strokeWidth="0.5"
        />
        <g filter="url(#filter10_i_3223_40289)">
          <path
            d="M6.99805 2.46875L78.998 2.46875L47.1742 29.8726C45.6925 31.1485 44.9517 31.7864 44.1202 32.0295C43.3874 32.2436 42.6087 32.2436 41.8759 32.0295C41.0444 31.7864 40.3036 31.1485 38.8219 29.8726L6.99805 2.46875Z"
            fill="url(#paint3_linear_3223_40289)"
          />
        </g>
        <g filter="url(#filter11_f_3223_40289)">
          <rect
            x="7"
            y="2.46484"
            width="72"
            height="48"
            stroke="url(#paint4_linear_3223_40289)"
            strokeWidth="8"
          />
        </g>
        <rect
          x="7.25"
          y="2.71484"
          width="71.5"
          height="47.5"
          rx="3.75"
          stroke="white"
          strokeOpacity="0.4"
          strokeWidth="0.5"
        />
      </g>
      <defs>
        <filter
          id="filter0_dddd_3223_40289"
          x="0.5"
          y="0.960937"
          width="85"
          height="71"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="1" />
          <feGaussianBlur stdDeviation="1" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0.117647 0 0 0 0 0.160784 0 0 0 0 0.231373 0 0 0 0.08 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_3223_40289"
          />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="4" />
          <feGaussianBlur stdDeviation="2" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0.117647 0 0 0 0 0.160784 0 0 0 0 0.231373 0 0 0 0.07 0"
          />
          <feBlend
            mode="normal"
            in2="effect1_dropShadow_3223_40289"
            result="effect2_dropShadow_3223_40289"
          />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="8.5" />
          <feGaussianBlur stdDeviation="2.5" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0.117647 0 0 0 0 0.160784 0 0 0 0 0.231373 0 0 0 0.04 0"
          />
          <feBlend
            mode="normal"
            in2="effect2_dropShadow_3223_40289"
            result="effect3_dropShadow_3223_40289"
          />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="15" />
          <feGaussianBlur stdDeviation="3" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0.117647 0 0 0 0 0.160784 0 0 0 0 0.231373 0 0 0 0.01 0"
          />
          <feBlend
            mode="normal"
            in2="effect3_dropShadow_3223_40289"
            result="effect4_dropShadow_3223_40289"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect4_dropShadow_3223_40289"
            result="shape"
          />
        </filter>
        <filter
          id="filter1_f_3223_40289"
          x="6.49805"
          y="18.8398"
          width="73"
          height="33.6211"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="BackgroundImageFix"
            result="shape"
          />
          <feGaussianBlur
            stdDeviation="1"
            result="effect1_foregroundBlur_3223_40289"
          />
        </filter>
        <filter
          id="filter2_f_3223_40289"
          x="6.49805"
          y="12.4492"
          width="73"
          height="44.0117"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="BackgroundImageFix"
            result="shape"
          />
          <feGaussianBlur
            stdDeviation="3"
            result="effect1_foregroundBlur_3223_40289"
          />
        </filter>
        <filter
          id="filter3_f_3223_40289"
          x="-3.50195"
          y="3.32812"
          width="93"
          height="59.1328"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="BackgroundImageFix"
            result="shape"
          />
          <feGaussianBlur
            stdDeviation="6"
            result="effect1_foregroundBlur_3223_40289"
          />
        </filter>
        <filter
          id="filter4_i_3223_40289"
          x="6.99805"
          y="20.7383"
          width="72"
          height="29.7227"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="BackgroundImageFix"
            result="shape"
          />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="0.5" />
          <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.6 0"
          />
          <feBlend
            mode="normal"
            in2="shape"
            result="effect1_innerShadow_3223_40289"
          />
        </filter>
        <filter
          id="filter5_f_3223_40289"
          x="6.49805"
          y="0.472656"
          width="73"
          height="33.6211"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="BackgroundImageFix"
            result="shape"
          />
          <feGaussianBlur
            stdDeviation="1"
            result="effect1_foregroundBlur_3223_40289"
          />
        </filter>
        <filter
          id="filter6_f_3223_40289"
          x="6.49805"
          y="-3.52734"
          width="73"
          height="44.0117"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="BackgroundImageFix"
            result="shape"
          />
          <feGaussianBlur
            stdDeviation="3"
            result="effect1_foregroundBlur_3223_40289"
          />
        </filter>
        <filter
          id="filter7_f_3223_40289"
          x="-3.50195"
          y="-9.52734"
          width="93"
          height="59.1328"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="BackgroundImageFix"
            result="shape"
          />
          <feGaussianBlur
            stdDeviation="6"
            result="effect1_foregroundBlur_3223_40289"
          />
        </filter>
        <filter
          id="filter8_f_3223_40289"
          x="5.99805"
          y="-2.52734"
          width="74"
          height="43.5547"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="BackgroundImageFix"
            result="shape"
          />
          <feGaussianBlur
            stdDeviation="2.5"
            result="effect1_foregroundBlur_3223_40289"
          />
        </filter>
        <filter
          id="filter9_f_3223_40289"
          x="5.99805"
          y="-0.027344"
          width="74"
          height="35.4063"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="BackgroundImageFix"
            result="shape"
          />
          <feGaussianBlur
            stdDeviation="1"
            result="effect1_foregroundBlur_3223_40289"
          />
        </filter>
        <filter
          id="filter10_i_3223_40289"
          x="6.99805"
          y="2.46875"
          width="72"
          height="29.7227"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="BackgroundImageFix"
            result="shape"
          />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="-0.5" />
          <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"
          />
          <feBlend
            mode="normal"
            in2="shape"
            result="effect1_innerShadow_3223_40289"
          />
        </filter>
        <filter
          id="filter11_f_3223_40289"
          x="-1"
          y="-5.53516"
          width="88"
          height="64"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="BackgroundImageFix"
            result="shape"
          />
          <feGaussianBlur
            stdDeviation="2"
            result="effect1_foregroundBlur_3223_40289"
          />
        </filter>
        <linearGradient
          id="paint0_linear_3223_40289"
          x1="36"
          y1="0"
          x2="36"
          y2="48"
          gradientUnits="userSpaceOnUse"
        >
          <stop offset="0.130208" stopColor="#EEF0F1" />
          <stop offset="0.848958" stopColor="#F9FAFA" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_3223_40289"
          x1="36"
          y1="0"
          x2="36"
          y2="48"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#475569" stopOpacity="0.06" />
          <stop offset="1" stopColor="#475569" stopOpacity="0.04" />
        </linearGradient>
        <linearGradient
          id="paint2_linear_3223_40289"
          x1="42.998"
          y1="19.4608"
          x2="42.998"
          y2="50.4608"
          gradientUnits="userSpaceOnUse"
        >
          <stop offset="0.524194" stopColor="#F4F5F6" />
          <stop offset="1" stopColor="#EEF0F1" />
        </linearGradient>
        <linearGradient
          id="paint3_linear_3223_40289"
          x1="42.998"
          y1="2.46875"
          x2="42.998"
          y2="33.4688"
          gradientUnits="userSpaceOnUse"
        >
          <stop offset="0.25" stopColor="#FAFAFB" />
          <stop offset="1" stopColor="#F4F5F6" />
        </linearGradient>
        <linearGradient
          id="paint4_linear_3223_40289"
          x1="43"
          y1="2.91344"
          x2="43"
          y2="50.4648"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#F9FAFA" />
          <stop offset="0.693396" stopColor="#EEF0F1" />
        </linearGradient>
      </defs>
    </svg>
  )
}

export default MailIcon
