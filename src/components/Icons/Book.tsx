const Book = ({ isActive = false }: { isActive: boolean }) => {
  return (
    <svg
      width="18"
      height="19"
      viewBox="0 0 18 19"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M9 16.9481C8.775 16.9481 8.55 16.8956 8.3625 16.7906C6.96 16.0256 4.4925 15.2156 2.9475 15.0131L2.73 14.9831C1.7475 14.8631 0.9375 13.9406 0.9375 12.9431V3.88307C0.9375 3.29057 1.17 2.75057 1.5975 2.36057C2.025 1.97057 2.58 1.78307 3.165 1.83557C4.815 1.96307 7.305 2.78807 8.715 3.67307L8.895 3.77807C8.9475 3.80807 9.06 3.80807 9.105 3.78557L9.225 3.71057C10.635 2.82557 13.125 1.98557 14.7825 1.84307C14.7975 1.84307 14.8575 1.84307 14.8725 1.84307C15.42 1.79057 15.9825 1.98557 16.4025 2.37557C16.83 2.76557 17.0625 3.30557 17.0625 3.89807V12.9506C17.0625 13.9556 16.2525 14.8706 15.2625 14.9906L15.015 15.0206C13.47 15.2231 10.995 16.0406 9.6225 16.7981C9.4425 16.9031 9.225 16.9481 9 16.9481ZM2.985 2.95307C2.745 2.95307 2.5275 3.03557 2.355 3.19307C2.1675 3.36557 2.0625 3.61307 2.0625 3.88307V12.9431C2.0625 13.3856 2.445 13.8131 2.8725 13.8731L3.0975 13.9031C4.785 14.1281 7.3725 14.9756 8.8725 15.7931C8.94 15.8231 9.0375 15.8306 9.075 15.8156C10.575 14.9831 13.1775 14.1281 14.8725 13.9031L15.1275 13.8731C15.555 13.8206 15.9375 13.3856 15.9375 12.9431V3.89057C15.9375 3.61307 15.8325 3.37307 15.645 3.19307C15.45 3.02057 15.2025 2.93807 14.925 2.95307C14.91 2.95307 14.85 2.95307 14.835 2.95307C13.4025 3.08057 11.0925 3.85307 9.8325 4.64057L9.7125 4.72307C9.3 4.97807 8.715 4.97807 8.3175 4.73057L8.1375 4.62557C6.855 3.83807 4.545 3.07307 3.075 2.95307C3.045 2.95307 3.015 2.95307 2.985 2.95307Z"
        fill={isActive ? '#141522' : '#9C9CA4'}
      />
      <path
        d="M9 16.3181C8.6925 16.3181 8.4375 16.0631 8.4375 15.7556V4.50562C8.4375 4.19812 8.6925 3.94312 9 3.94312C9.3075 3.94312 9.5625 4.19812 9.5625 4.50562V15.7556C9.5625 16.0706 9.3075 16.3181 9 16.3181Z"
        fill={isActive ? '#141522' : '#9C9CA4'}
      />
      <path
        d="M5.8125 7.31812H4.125C3.8175 7.31812 3.5625 7.06312 3.5625 6.75562C3.5625 6.44812 3.8175 6.19312 4.125 6.19312H5.8125C6.12 6.19312 6.375 6.44812 6.375 6.75562C6.375 7.06312 6.12 7.31812 5.8125 7.31812Z"
        fill={isActive ? '#141522' : '#9C9CA4'}
      />
      <path
        d="M6.375 9.56812H4.125C3.8175 9.56812 3.5625 9.31312 3.5625 9.00562C3.5625 8.69812 3.8175 8.44312 4.125 8.44312H6.375C6.6825 8.44312 6.9375 8.69812 6.9375 9.00562C6.9375 9.31312 6.6825 9.56812 6.375 9.56812Z"
        fill={isActive ? '#141522' : '#9C9CA4'}
      />
    </svg>
  )
}

export default Book
