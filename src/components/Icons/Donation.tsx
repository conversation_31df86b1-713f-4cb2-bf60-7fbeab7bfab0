const Donation = ({ isActive = false }: { isActive: boolean }) => {
  return (
    <svg
      width="18"
      height="19"
      viewBox="0 0 18 19"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M12.75 15.7632H5.25C3 15.7632 1.5 14.6382 1.5 12.0132V6.76318C1.5 4.13818 3 3.01318 5.25 3.01318H12.75C15 3.01318 16.5 4.13818 16.5 6.76318V12.0132C16.5 14.6382 15 15.7632 12.75 15.7632Z"
        stroke={isActive ? '#141522' : '#9C9CA4'}
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M9 11.6382C10.2426 11.6382 11.25 10.6308 11.25 9.38818C11.25 8.14554 10.2426 7.13818 9 7.13818C7.75736 7.13818 6.75 8.14554 6.75 9.38818C6.75 10.6308 7.75736 11.6382 9 11.6382Z"
        stroke={isActive ? '#141522' : '#9C9CA4'}
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M1.5 7.13818H2.25C4.5 7.13818 5.25 6.38818 5.25 4.13818V3.38818"
        stroke={isActive ? '#141522' : '#9C9CA4'}
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M16.5 7.13818H15.75C13.5 7.13818 12.75 6.38818 12.75 4.13818V3.38818"
        stroke={isActive ? '#141522' : '#9C9CA4'}
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M1.5 11.6382H2.25C4.5 11.6382 5.25 12.3882 5.25 14.6382V15.3882"
        stroke={isActive ? '#141522' : '#9C9CA4'}
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M16.5 11.6382H15.75C13.5 11.6382 12.75 12.3882 12.75 14.6382V15.3882"
        stroke={isActive ? '#141522' : '#9C9CA4'}
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export default Donation
