const Bag = ({ isActive = false }: { isActive: boolean }) => {
  return (
    <svg
      width="18"
      height="19"
      viewBox="0 0 18 19"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M6.00011 16.8882H12.0001C15.0151 16.8882 15.5551 15.6807 15.7126 14.2107L16.2751 8.21068C16.4776 6.38068 15.9526 4.88818 12.7501 4.88818H5.25011C2.04761 4.88818 1.52261 6.38068 1.72511 8.21068L2.28761 14.2107C2.44511 15.6807 2.98511 16.8882 6.00011 16.8882Z"
        stroke={isActive ? '#141522' : '#9C9CA4'}
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M6 4.88818V4.28818C6 2.96068 6 1.88818 8.4 1.88818H9.6C12 1.88818 12 2.96068 12 4.28818V4.88818"
        stroke={isActive ? '#141522' : '#9C9CA4'}
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M10.5 10.1382V10.8882C10.5 10.8957 10.5 10.8957 10.5 10.9032C10.5 11.7207 10.4925 12.3882 9 12.3882C7.515 12.3882 7.5 11.7282 7.5 10.9107V10.1382C7.5 9.38818 7.5 9.38818 8.25 9.38818H9.75C10.5 9.38818 10.5 9.38818 10.5 10.1382Z"
        stroke={isActive ? '#141522' : '#9C9CA4'}
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M16.2375 8.63818C14.505 9.89818 12.525 10.6482 10.5 10.9032"
        stroke={isActive ? '#141522' : '#9C9CA4'}
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M1.96484 8.84058C3.65234 9.99558 5.55734 10.6931 7.49984 10.9106"
        stroke={isActive ? '#141522' : '#9C9CA4'}
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export default Bag
