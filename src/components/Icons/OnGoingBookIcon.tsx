const OnGoingBookIcon = () => {
  return (
    <svg
      width="25"
      height="25"
      viewBox="0 0 25 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_2361_28376)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M12.1124 6.96434C12.1124 6.96434 9.25418 2.67707 3.53781 1.9314C2.59898 1.80894 1.82666 2.589 1.82666 3.53577V15.5358C1.82666 16.4826 2.59898 17.2375 3.53781 17.3601C9.25418 18.1056 12.1124 22.3929 12.1124 22.3929C12.1124 22.3929 14.9706 18.1056 20.6869 17.3601C21.6258 17.2375 22.3981 16.4826 22.3981 15.5358V3.53577C22.3981 2.589 21.6258 1.80894 20.6869 1.9314C14.9706 2.67707 12.1124 6.96434 12.1124 6.96434Z"
          fill="#FFBB54"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M3.39937 2.99402C3.27589 2.97791 3.16614 3.01566 3.07237 3.10569C2.97244 3.20162 2.89823 3.35418 2.89823 3.53596V15.536C2.89823 15.8595 3.18247 16.2333 3.67653 16.2977C6.78043 16.7026 9.10492 18.0696 10.6459 19.3258C10.7837 19.4381 10.9154 19.5497 11.0411 19.6599V7.31723C10.97 7.22651 10.8782 7.11357 10.7658 6.9834C10.442 6.60858 9.95023 6.09484 9.29188 5.55813C7.97464 4.48428 6.01183 3.3348 3.39937 2.99402ZM13.1839 7.31723V19.6599C13.3096 19.5497 13.4413 19.4381 13.5792 19.3258C15.1201 18.0696 17.4446 16.7026 20.5485 16.2977C21.0426 16.2333 21.3268 15.8595 21.3268 15.536V3.53596C21.3268 3.35418 21.2526 3.20162 21.1526 3.10569C21.0589 3.01566 20.9491 2.97791 20.8257 2.99402C18.2131 3.3348 16.2504 4.48428 14.9331 5.55813C14.2748 6.09484 13.783 6.60858 13.4592 6.9834C13.3468 7.11357 13.255 7.22651 13.1839 7.31723ZM12.9944 23.0016C12.9976 22.997 13.0008 22.9922 13.004 22.9874L13.0209 22.9634C13.0373 22.9401 13.0646 22.9022 13.1026 22.8516C13.1787 22.7506 13.2976 22.5991 13.4592 22.4119C13.783 22.0371 14.2748 21.5234 14.9331 20.9866C16.2504 19.9128 18.2131 18.7634 20.8257 18.4226C22.2093 18.2421 23.4697 17.106 23.4697 15.536V3.53596C23.4697 2.04066 22.2223 0.650824 20.5485 0.869162C17.4446 1.27405 15.1201 2.64103 13.5792 3.89724C12.9723 4.392 12.4836 4.87209 12.1125 5.27469C11.7414 4.87209 11.2528 4.392 10.6459 3.89724C9.10492 2.64103 6.78043 1.27405 3.67653 0.869162C2.00272 0.650824 0.755371 2.04066 0.755371 3.53596V15.536C0.755371 17.106 2.01577 18.2421 3.39937 18.4226C6.01183 18.7634 7.97464 19.9128 9.29188 20.9866C9.95023 21.5234 10.442 22.0371 10.7658 22.4119C10.9274 22.5991 11.0463 22.7506 11.1224 22.8516C11.1605 22.9022 11.1877 22.9401 11.2042 22.9634L11.2207 22.987C11.2239 22.9918 11.2274 22.997 11.2307 23.0016C11.2988 23.1003 11.3814 23.185 11.4742 23.2538C11.5669 23.3225 11.6718 23.377 11.786 23.4135C11.7913 23.4153 11.7967 23.417 11.802 23.4185C11.9997 23.4783 12.2152 23.4814 12.423 23.4185C12.4284 23.417 12.4337 23.4153 12.439 23.4135C12.5533 23.377 12.6581 23.3225 12.7508 23.2538C12.8436 23.185 12.9262 23.1003 12.9944 23.0016Z"
          fill="#141522"
        />
      </g>
      <defs>
        <clipPath id="clip0_2361_28376">
          <rect
            width="24"
            height="24"
            fill="white"
            transform="translate(0.112305 0.107422)"
          />
        </clipPath>
      </defs>
    </svg>
  )
}

export default OnGoingBookIcon
