import React from 'react'

const Substack = () => (
  <svg
    width="44"
    height="44"
    viewBox="0 0 44 44"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
  >
    <rect width="44" height="44" rx="10" fill="url(#pattern0_439_3750)" />
    <defs>
      <pattern
        id="pattern0_439_3750"
        patternContentUnits="objectBoundingBox"
        width="1"
        height="1"
      >
        <use xlinkHref="#image0_439_3750" transform="scale(0.00195312)" />
      </pattern>
      <image
        id="image0_439_3750"
        width="512"
        height="512"
        preserveAspectRatio="none"
        xlinkHref="data:image/png;base64,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"
      />
    </defs>
  </svg>
)

export default Substack
