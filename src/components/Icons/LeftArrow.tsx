const LeftArrow = ({
  dark = false,
  arrowColor,
}: {
  dark?: boolean
  arrowColor?: string
}) => {
  return (
    <svg
      width="20"
      height="21"
      viewBox="0 0 20 21"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M12.4995 17.1001L7.06621 11.6668C6.42454 11.0251 6.42454 9.97515 7.06621 9.33348L12.4995 3.90015"
        stroke={arrowColor ? arrowColor : dark ? '#141522' : '#9C9CA4'}
        strokeWidth="1.18719"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export default LeftArrow
