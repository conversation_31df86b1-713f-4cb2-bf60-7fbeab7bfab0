const BookOpenBlue = () => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M7.9 2.89077C6.48867 2.21694 4.51602 1.88686 1.875 1.87514C1.62593 1.87176 1.38174 1.94439 1.175 2.08335C1.00531 2.19805 0.866403 2.35268 0.770498 2.53366C0.674593 2.71464 0.62463 2.91642 0.625002 3.12124V14.297C0.625002 15.0525 1.1625 15.6224 1.875 15.6224C4.65117 15.6224 7.43594 15.8818 9.10391 17.4583C9.12673 17.48 9.15541 17.4945 9.18639 17.5C9.21737 17.5055 9.24928 17.5018 9.27816 17.4893C9.30704 17.4768 9.33162 17.4561 9.34883 17.4298C9.36605 17.4034 9.37515 17.3726 9.375 17.3412V4.1728C9.37504 4.08396 9.35603 3.99614 9.31923 3.91528C9.28244 3.83441 9.22873 3.76238 9.16172 3.70405C8.77965 3.37742 8.35525 3.10386 7.9 2.89077ZM18.825 2.08217C18.6182 1.94356 18.374 1.87134 18.125 1.87514C15.484 1.88686 13.5113 2.21538 12.1 2.89077C11.6448 3.10347 11.2203 3.37649 10.8379 3.70249C10.771 3.76091 10.7174 3.83296 10.6807 3.91381C10.644 3.99466 10.625 4.08244 10.625 4.17124V17.3404C10.625 17.3706 10.6339 17.4002 10.6506 17.4254C10.6673 17.4506 10.6911 17.4703 10.719 17.482C10.7469 17.4937 10.7776 17.4969 10.8073 17.4913C10.837 17.4856 10.8643 17.4713 10.8859 17.4501C11.8887 16.454 13.6484 15.6212 18.1266 15.6216C18.4581 15.6216 18.776 15.4899 19.0104 15.2555C19.2449 15.0211 19.3766 14.7031 19.3766 14.3716V3.12163C19.377 2.9164 19.3269 2.71422 19.2307 2.53294C19.1346 2.35165 18.9952 2.19684 18.825 2.08217Z"
        fill="#74AFF2"
      />
    </svg>
  )
}

export default BookOpenBlue
