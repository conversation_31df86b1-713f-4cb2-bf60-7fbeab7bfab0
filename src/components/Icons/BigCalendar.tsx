const BigCalendar = () => {
  return (
    <svg
      width="168"
      height="168"
      viewBox="0 0 168 168"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect width="168" height="168" fill="white" />
      <path
        d="M22 60H146V133.52C146 137.699 146 139.788 145.239 141.406C144.446 143.091 143.091 144.446 141.406 145.239C139.788 146 137.699 146 133.52 146H34.48C30.3012 146 28.2118 146 26.5938 145.239C24.9093 144.446 23.554 143.091 22.7614 141.406C22 139.788 22 137.699 22 133.52V60Z"
        fill="#F5B6F9"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M112.615 28.3633H133.52C137.699 28.3633 139.788 28.3633 141.406 29.1247C143.091 29.9173 144.446 31.2726 145.239 32.957C146 34.5751 146 36.6645 146 40.8433V60.3633H22V37.7233C22 34.5892 22 33.0221 22.571 31.8086C23.1655 30.5453 24.182 29.5288 25.4453 28.9343C26.6588 28.3633 28.2259 28.3633 31.36 28.3633H57.7692V39.8101C57.7692 42.3057 59.9097 44.3633 62.5501 44.3633H64.9114C67.5607 44.3633 69.6923 42.3248 69.6923 39.8101V28.3633H100.692V39.8101C100.692 42.3057 102.833 44.3633 105.473 44.3633H107.835C110.484 44.3633 112.615 42.3248 112.615 39.8101V28.3633Z"
        fill="#943699"
      />
      <path
        d="M67.1579 20.0106C67.1579 18.034 65.5555 16.4316 63.5789 16.4316C61.6023 16.4316 60 18.034 60 20.0106V39.0984C60 41.0751 61.6023 42.6774 63.5789 42.6774C65.5555 42.6774 67.1579 41.0751 67.1579 39.0984V20.0106Z"
        fill="#402151"
      />
      <path
        d="M110.158 20.0106C110.158 18.034 108.556 16.4316 106.579 16.4316C104.602 16.4316 103 18.034 103 20.0106V39.0984C103 41.0751 104.602 42.6774 106.579 42.6774C108.556 42.6774 110.158 41.0751 110.158 39.0984V20.0106Z"
        fill="#402151"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M39.0273 78.7181C39.0273 78.123 39.0273 77.8255 39.0902 77.5807C39.2716 76.8741 39.8234 76.3224 40.53 76.141C40.7747 76.0781 41.0722 76.0781 41.6673 76.0781H48.3172C48.9123 76.0781 49.2098 76.0781 49.4545 76.141C50.1611 76.3224 50.7129 76.8741 50.8943 77.5807C50.9572 77.8255 50.9572 78.123 50.9572 78.7181V85.368C50.9572 85.9631 50.9572 86.2607 50.8943 86.5054C50.7129 87.212 50.1611 87.7638 49.4545 87.9452C49.2098 88.008 48.9123 88.008 48.3172 88.008H41.6673C41.0722 88.008 40.7747 88.008 40.53 87.9452C39.8234 87.7638 39.2716 87.212 39.0902 86.5054C39.0273 86.2607 39.0273 85.9631 39.0273 85.368V78.7181Z"
        fill="#C067C5"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M72.4316 75.2686C72.4316 74.3759 72.4316 73.9296 72.5259 73.5625C72.798 72.5026 73.6257 71.675 74.6856 71.4028C75.0527 71.3086 75.499 71.3086 76.3916 71.3086H92.3313C93.2239 71.3086 93.6703 71.3086 94.0374 71.4028C95.0973 71.675 95.9249 72.5026 96.197 73.5625C96.2913 73.9296 96.2913 74.3759 96.2913 75.2686V91.2084C96.2913 92.101 96.2913 92.5474 96.197 92.9145C95.9249 93.9744 95.0973 94.802 94.0374 95.0741C93.6703 95.1684 93.2239 95.1684 92.3313 95.1684H76.3916C75.499 95.1684 75.0527 95.1684 74.6856 95.0741C73.6257 94.802 72.798 93.9744 72.5259 92.9145C72.4316 92.5474 72.4316 92.101 72.4316 91.2084V75.2686Z"
        fill="#C067C5"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M108.221 75.2686C108.221 74.3759 108.221 73.9296 108.315 73.5625C108.587 72.5026 109.415 71.675 110.475 71.4028C110.842 71.3086 111.288 71.3086 112.181 71.3086H128.12C129.013 71.3086 129.459 71.3086 129.826 71.4028C130.886 71.675 131.714 72.5026 131.986 73.5625C132.08 73.9296 132.08 74.3759 132.08 75.2686V91.2084C132.08 92.101 132.08 92.5474 131.986 92.9145C131.714 93.9744 130.886 94.802 129.826 95.0741C129.459 95.1684 129.013 95.1684 128.12 95.1684H112.181C111.288 95.1684 110.842 95.1684 110.475 95.0741C109.415 94.802 108.587 93.9744 108.315 92.9145C108.221 92.5474 108.221 92.101 108.221 91.2084V75.2686Z"
        fill="#C067C5"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M34.2578 111.058C34.2578 110.165 34.2578 109.719 34.3521 109.352C34.6242 108.292 35.4518 107.464 36.5117 107.192C36.8788 107.098 37.3252 107.098 38.2178 107.098H54.1575C55.0501 107.098 55.4964 107.098 55.8635 107.192C56.9234 107.464 57.7511 108.292 58.0232 109.352C58.1175 109.719 58.1175 110.165 58.1175 111.058V126.997C58.1175 127.89 58.1175 128.336 58.0232 128.704C57.7511 129.763 56.9234 130.591 55.8635 130.863C55.4964 130.957 55.0501 130.957 54.1575 130.957H38.2178C37.3252 130.957 36.8788 130.957 36.5117 130.863C35.4518 130.591 34.6242 129.763 34.3521 128.704C34.2578 128.336 34.2578 127.89 34.2578 126.997V111.058Z"
        fill="#C067C5"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M72.4316 111.058C72.4316 110.165 72.4316 109.719 72.5259 109.352C72.798 108.292 73.6257 107.464 74.6856 107.192C75.0527 107.098 75.499 107.098 76.3916 107.098H92.3313C93.2239 107.098 93.6703 107.098 94.0374 107.192C95.0973 107.464 95.9249 108.292 96.197 109.352C96.2913 109.719 96.2913 110.165 96.2913 111.058V126.997C96.2913 127.89 96.2913 128.336 96.197 128.704C95.9249 129.763 95.0973 130.591 94.0374 130.863C93.6703 130.957 93.2239 130.957 92.3313 130.957H76.3916C75.499 130.957 75.0527 130.957 74.6856 130.863C73.6257 130.591 72.798 129.763 72.5259 128.704C72.4316 128.336 72.4316 127.89 72.4316 126.997V111.058Z"
        fill="#C067C5"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M108.221 111.058C108.221 110.165 108.221 109.719 108.315 109.352C108.587 108.292 109.415 107.464 110.475 107.192C110.842 107.098 111.288 107.098 112.181 107.098H128.12C129.013 107.098 129.459 107.098 129.826 107.192C130.886 107.464 131.714 108.292 131.986 109.352C132.08 109.719 132.08 110.165 132.08 111.058V126.997C132.08 127.89 132.08 128.336 131.986 128.704C131.714 129.763 130.886 130.591 129.826 130.863C129.459 130.957 129.013 130.957 128.12 130.957H112.181C111.288 130.957 110.842 130.957 110.475 130.863C109.415 130.591 108.587 129.763 108.315 128.704C108.221 128.336 108.221 127.89 108.221 126.997V111.058Z"
        fill="#C067C5"
      />
      <path
        d="M37.064 97.168C28.7003 86.5589 32.6091 60.604 51.9982 76.1466C67.9491 88.9331 41.7481 100.122 40.0915 85.2726C39.8835 83.4079 43.5853 79.4797 44.0711 81.292"
        stroke="#402151"
        strokeWidth="2.38597"
        strokeLinecap="round"
      />
    </svg>
  )
}

export default BigCalendar
