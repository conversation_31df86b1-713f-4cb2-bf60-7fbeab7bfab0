const Help = ({ isActive }: { isActive?: boolean }) => {
  return (
    <svg
      width="18"
      height="18"
      viewBox="0 0 18 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M12.75 13.8223H9.75L6.41249 16.0423C5.91749 16.3723 5.25 16.0198 5.25 15.4198V13.8223C3 13.8223 1.5 12.3223 1.5 10.0723V5.57227C1.5 3.32227 3 1.82227 5.25 1.82227H12.75C15 1.82227 16.5 3.32227 16.5 5.57227V10.0723C16.5 12.3223 15 13.8223 12.75 13.8223Z"
        stroke={isActive ? '#141522' : '#9C9CA4'}
        strokeWidth="1.5"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8.99986 8.51953V8.36206C8.99986 7.85206 9.31488 7.58205 9.62988 7.36455C9.93738 7.15455 10.2448 6.88456 10.2448 6.38956C10.2448 5.69956 9.68986 5.14453 8.99986 5.14453C8.30986 5.14453 7.75488 5.69956 7.75488 6.38956"
        stroke={isActive ? '#141522' : '#9C9CA4'}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8.99662 10.3125H9.00337"
        stroke={isActive ? '#141522' : '#9C9CA4'}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export default Help
