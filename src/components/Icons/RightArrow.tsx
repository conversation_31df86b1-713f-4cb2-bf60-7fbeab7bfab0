const RightArrow = ({
  dark = false,
  arrowColor = '#04A4F4',
}: {
  dark?: boolean
  arrowColor?: string
}) => {
  return (
    <svg
      width="20"
      height="21"
      viewBox="0 0 20 21"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M7.42578 17.1001L12.8591 11.6668C13.5008 11.0251 13.5008 9.97515 12.8591 9.33348L7.42578 3.90015"
        stroke={arrowColor ? arrowColor : dark ? '#141522' : '#9C9CA4'}
        strokeWidth="1.18719"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export default RightArrow
