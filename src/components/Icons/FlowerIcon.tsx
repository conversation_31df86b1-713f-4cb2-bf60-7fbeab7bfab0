const FlowerIcon = () => {
  return (
    <svg
      width="80"
      height="80"
      viewBox="0 0 80 80"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M37.4037 68.7619H37.3998L21.665 68.758C21.146 68.758 20.7285 68.3365 20.7285 67.8214C20.7285 67.3024 21.146 66.8848 21.665 66.8848L37.3997 66.8887H37.4036C40.8728 66.8887 44.2641 65.9209 47.2143 64.0907L66.9255 51.8487C67.4875 51.4312 67.7997 50.959 67.8816 50.4126C67.9714 49.8195 67.7294 49.2536 67.2299 48.9063C66.1724 48.1687 64.2172 48.4614 62.0006 49.6868C61.8406 49.7765 60.4592 50.6507 58.1684 52.0907C55.9987 53.4565 52.7206 55.5248 48.3694 58.2604C47.9324 58.5375 47.3548 58.4048 47.0777 57.9678C46.8006 57.5307 46.9333 56.9531 47.3704 56.676C51.7216 53.9404 54.9997 51.876 57.1655 50.5102C60.0885 48.6682 60.9392 48.1336 61.0836 48.0517C62.5899 47.2165 63.9948 46.7443 65.2553 46.6507C66.4377 46.5609 67.4875 46.8107 68.2914 47.3687C69.3724 48.1219 69.9226 49.398 69.7236 50.6936C69.5636 51.7512 68.9821 52.6565 67.9987 53.3746C67.9792 53.3902 67.9558 53.4058 67.9363 53.4175L48.2055 65.6829C44.9589 67.6965 41.2242 68.7619 37.4037 68.7619Z"
        fill="#943699"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M44.6349 61.0833H35.2495C34.7305 61.0833 34.3129 60.6657 34.3129 60.1467C34.3129 59.6276 34.7305 59.2101 35.2495 59.2101H44.6349C45.9031 59.2101 46.9412 58.2189 46.9997 56.9545C47.031 56.3106 46.7968 55.6979 46.348 55.2257C45.8836 54.7379 45.2592 54.4725 44.588 54.4725H32.9822C32.9783 54.4725 32.9783 54.4725 32.9744 54.4725C32.1744 54.4647 30.3012 54.4491 28.3539 54.8784C26.2036 55.3545 24.4749 56.2481 23.2183 57.5398C22.9334 57.8325 22.6641 58.1486 22.4222 58.4803C22.1178 58.8979 21.5324 58.9915 21.1149 58.6872C20.6973 58.3828 20.6036 57.7974 20.908 57.3798C21.2046 56.974 21.5285 56.5876 21.8758 56.2325C23.3939 54.6754 25.4349 53.6062 27.948 53.052C29.8563 52.6306 31.6436 52.5915 32.9861 52.6032H44.5841C45.7549 52.6032 46.8905 53.0911 47.6983 53.9379C48.5061 54.7847 48.9197 55.8852 48.8651 57.0442C48.7638 59.3076 46.9023 61.0833 44.6349 61.0833Z"
        fill="#943699"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M21.6654 70.09H11.2615C10.7425 70.09 10.321 69.6685 10.321 69.1495C10.321 68.6305 10.7386 68.2129 11.2576 68.2129C11.2849 68.2129 11.3084 68.2129 11.3357 68.2168H20.7328V56.0919H11.3357C11.3084 56.0958 11.2849 56.0958 11.2576 56.0958C10.7386 56.0958 10.321 55.6783 10.321 55.1592C10.321 54.6402 10.7425 54.2188 11.2615 54.2188H21.6615C22.1806 54.2188 22.602 54.6402 22.602 55.1592V69.1534C22.6059 69.6685 22.1806 70.09 21.6654 70.09Z"
        fill="#943699"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M39.2497 50.4023C38.7307 50.4023 38.3131 49.9847 38.3131 49.4657V23.2725C38.3131 22.7535 38.7307 22.3359 39.2497 22.3359C39.7687 22.3359 40.1863 22.7535 40.1863 23.2725V49.4657C40.1864 49.9808 39.7648 50.4023 39.2497 50.4023Z"
        fill="#008167"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M39.2497 42.3793C38.7307 42.3793 38.3131 41.9618 38.3131 41.4428C38.3131 38.8984 39.4565 36.5101 41.716 34.3442C43.3043 32.8223 45.4429 31.3979 48.2604 29.9852C48.7209 29.755 49.2868 29.9423 49.517 30.4028C49.7473 30.8672 49.5599 31.4291 49.0995 31.6593C46.4536 32.9862 44.4595 34.3052 43.0116 35.6945C42.1141 36.5569 41.4234 37.4506 40.9551 38.3559C40.4438 39.3472 40.1863 40.3813 40.1863 41.4389C40.1864 41.9618 39.7648 42.3793 39.2497 42.3793Z"
        fill="#008167"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M47.1913 37.0043C45.7513 37.0043 44.1709 36.4658 42.247 35.6268C41.9113 35.4785 41.6889 35.1507 41.685 34.7838C41.6538 32.8209 41.9543 31.1038 42.5787 29.6755C43.164 28.337 44.0382 27.2443 45.1738 26.4209C48.5377 23.9897 53.5172 24.4424 57.0567 25.6755C57.3923 25.7926 57.6304 26.0853 57.6772 26.4365C57.724 26.7877 57.5679 27.1351 57.2753 27.3341C55.3006 28.6765 54.4343 30.3194 54.0635 31.018C54.005 31.1312 53.9543 31.2248 53.9113 31.299C53.8957 31.3302 53.7865 31.5175 53.7123 31.6463C53.6343 31.7829 53.5601 31.9116 53.5445 31.939C51.8196 34.8931 50.3172 36.3565 48.526 36.8326C48.0889 36.9536 47.6479 37.0043 47.1913 37.0043ZM43.5621 34.1516C45.4431 34.936 46.8753 35.338 48.0421 35.0258C48.6587 34.8619 49.2245 34.5068 49.8255 33.9019C50.4772 33.2463 51.164 32.298 51.925 30.9946C51.9406 30.9673 52.0109 30.8463 52.0811 30.7214C52.2099 30.4912 52.2762 30.3819 52.2957 30.3468C52.3192 30.3116 52.3582 30.2297 52.405 30.1477C52.7094 29.5702 53.4001 28.2629 54.766 26.9634C53.5328 26.6746 52.2801 26.5302 51.1172 26.5497C49.1699 26.577 47.4918 27.0609 46.2704 27.9429C44.5377 29.1955 43.6284 31.2834 43.5621 34.1516Z"
        fill="#008167"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M39.2497 29.1962C39.117 29.1962 38.9843 29.1689 38.8594 29.1104C36.9902 28.2519 35.5931 27.1748 34.7033 25.9104C33.7433 24.5406 33.396 22.9992 33.677 21.325C33.9346 19.7797 34.7346 18.1445 36.0575 16.4626C36.8458 15.4597 37.4507 14.3631 37.8526 13.2001C38.1531 12.3338 38.3131 11.604 38.3131 11.085C38.3131 10.5699 38.7346 10.1484 39.2497 10.1484C39.7648 10.1484 40.1863 10.5699 40.1863 11.085V11.0889C40.1863 11.5728 40.3228 12.2401 40.5804 13.0167C40.9863 14.246 41.6146 15.4011 42.4458 16.4626C44.518 19.1006 45.2946 21.5748 44.7521 23.8226C44.4829 24.9387 43.8858 25.9689 42.9804 26.8782C42.1336 27.7289 41.0097 28.4821 39.6399 29.1104C39.5152 29.1689 39.3825 29.1962 39.2497 29.1962ZM39.2459 14.7689C38.8011 15.7758 38.2274 16.7319 37.525 17.6216C36.4011 19.0538 35.7259 20.404 35.5191 21.6333C35.3201 22.8236 35.5542 23.8694 36.2333 24.8372C36.8694 25.7426 37.8801 26.5426 39.2459 27.2255C40.2294 26.7338 41.0372 26.1758 41.6498 25.5631C42.3094 24.8997 42.7386 24.166 42.9298 23.3894C43.3318 21.727 42.6723 19.7875 40.9708 17.6255C40.2682 16.7279 39.6908 15.7719 39.2459 14.7689Z"
        fill="#008167"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M39.2495 48.9621C38.7305 48.9621 38.3129 48.5445 38.3129 48.0255C38.3129 46.9679 38.0554 45.9299 37.5441 44.9426C37.0758 44.0372 36.3851 43.1396 35.4875 42.2811C34.0397 40.8918 32.0456 39.5728 29.3997 38.246C28.9354 38.0157 28.7519 37.4499 28.9822 36.9894C29.2124 36.525 29.7783 36.3416 30.2388 36.5718C33.0563 37.9845 35.1988 39.4089 36.7832 40.9309C39.0427 43.0967 40.1861 45.485 40.1861 48.0294C40.1862 48.5445 39.7646 48.9621 39.2495 48.9621Z"
        fill="#008167"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M31.3045 43.5942C30.8518 43.5942 30.4108 43.5395 29.9738 43.4264C28.1825 42.9503 26.6801 41.4869 24.9513 38.5327C24.9357 38.5054 24.8655 38.3805 24.7874 38.2439C24.7133 38.1191 24.604 37.9239 24.5884 37.8966C24.5416 37.8186 24.4947 37.7288 24.4362 37.6156C24.0655 36.9132 23.1991 35.2703 21.2245 33.9317C20.9318 33.7327 20.7757 33.3854 20.8225 33.0342C20.8694 32.683 21.1074 32.3903 21.443 32.2732C24.9825 31.04 29.9621 30.5873 33.326 33.0186C34.4616 33.8381 35.3357 34.9347 35.9211 36.2732C36.5455 37.7015 36.846 39.4186 36.8147 41.3815C36.8069 41.7483 36.5884 42.08 36.2528 42.2244C34.325 43.0556 32.7445 43.5942 31.3045 43.5942ZM23.7338 33.5493C25.0996 34.8488 25.7903 36.16 26.0947 36.7337C26.1377 36.8156 26.1806 36.8976 26.204 36.9327C26.2235 36.9678 26.2899 37.081 26.4225 37.3113C26.4928 37.4361 26.563 37.5532 26.5786 37.5805C27.3396 38.8839 28.0264 39.8322 28.6781 40.4878C29.2791 41.0888 29.845 41.4478 30.4616 41.6117C31.6323 41.92 33.0606 41.522 34.9416 40.7376C34.8752 37.8693 33.966 35.7815 32.2333 34.5249C31.0118 33.643 29.3338 33.1591 27.3864 33.1317C26.2196 33.12 24.963 33.2644 23.7338 33.5493Z"
        fill="#008167"
      />
    </svg>
  )
}

export default FlowerIcon
