import React from 'react'

const BookWithPenIcon = () => {
  return (
    <svg
      width="36"
      height="36"
      viewBox="0 0 36 36"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_2368_24226)">
        <path
          d="M7.1123 15.1074C14.1123 15.1074 17.779 17.7741 18.1123 19.1074C18.4456 17.7741 22.1123 15.1074 29.1123 15.1074V27.8574C22.1123 27.8574 18.4456 30.524 18.1123 31.8574C17.779 30.524 14.1123 27.8574 7.1123 27.8574V15.1074Z"
          fill="#FBA85A"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M12.1123 5.10742C11.9249 5.10742 11.7412 5.16009 11.5823 5.25942L7.5823 7.75942C7.28992 7.94216 7.1123 8.26264 7.1123 8.60742C7.1123 8.9522 7.28992 9.27268 7.5823 9.45542L11.5823 11.9554C11.7412 12.0548 11.9249 12.1074 12.1123 12.1074H25.6123C27.5453 12.1074 29.1123 10.5404 29.1123 8.60742C29.1123 6.67443 27.5453 5.10742 25.6123 5.10742H12.1123ZM7.1123 13.8574C6.42194 13.8574 5.8623 14.4171 5.8623 15.1074V27.8574C5.8623 28.5478 6.42194 29.1074 7.1123 29.1074C10.4565 29.1074 12.9436 29.7448 14.595 30.4954C15.4234 30.8718 16.023 31.2684 16.4142 31.6106C16.7691 31.9212 16.8749 32.1178 16.896 32.1568L16.8989 32.162L16.9001 32.1638C16.9375 32.3122 17.0014 32.45 17.0865 32.572C17.1408 32.6498 17.2036 32.7208 17.2733 32.784C17.4951 32.985 17.7894 33.1074 18.1123 33.1074C18.4352 33.1074 18.7295 32.985 18.9513 32.784C19.021 32.7208 19.0838 32.6498 19.1381 32.572C19.2232 32.45 19.2871 32.3122 19.3245 32.1638C19.3245 32.1638 19.3261 32.1614 19.3286 32.1568C19.3497 32.1178 19.4555 31.9212 19.8104 31.6106C20.2016 31.2684 20.8012 30.8718 21.6296 30.4954C23.281 29.7448 25.7681 29.1074 29.1123 29.1074C29.8027 29.1074 30.3623 28.5478 30.3623 27.8574V15.1074C30.3623 14.4171 29.8027 13.8574 29.1123 13.8574C25.4565 13.8574 22.6103 14.5534 20.595 15.4695C19.5901 15.9263 18.773 16.4465 18.1642 16.9792L18.1123 17.0251L18.0604 16.9792C17.4516 16.4465 16.6345 15.9263 15.6296 15.4695C13.6143 14.5534 10.7681 13.8574 7.1123 13.8574ZM20.595 28.2194C20.146 28.4236 19.7345 28.6404 19.3623 28.8652V19.3495C19.4173 19.2635 19.5433 19.0944 19.8104 18.8606C20.2016 18.5184 20.8012 18.1219 21.6296 17.7454C23.0682 17.0914 25.1411 16.5235 27.8623 16.388V26.6352C24.8019 26.7732 22.3745 27.4106 20.595 28.2194ZM16.8623 28.8652V19.3495C16.8073 19.2635 16.6813 19.0944 16.4142 18.8606C16.023 18.5184 15.4234 18.1219 14.595 17.7454C13.1564 17.0914 11.0836 16.5235 8.3623 16.388V26.6352C11.4228 26.7732 13.8501 27.4106 15.6296 28.2194C16.0786 28.4236 16.4901 28.6404 16.8623 28.8652Z"
          fill="#141522"
        />
      </g>
      <defs>
        <clipPath id="clip0_2368_24226">
          <rect
            width="28"
            height="28"
            fill="white"
            transform="translate(4.1123 5.10742)"
          />
        </clipPath>
      </defs>
    </svg>
  )
}

export default BookWithPenIcon
