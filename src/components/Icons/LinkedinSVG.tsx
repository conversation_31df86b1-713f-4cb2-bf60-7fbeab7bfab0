const LinkedinSVG = () => {
  return (
    <svg
      width="22"
      height="22"
      viewBox="0 0 22 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_789_8560)">
        <path
          d="M18.2188 3.125H3.78125C3.41881 3.125 3.125 3.41881 3.125 3.78125V18.2188C3.125 18.5812 3.41881 18.875 3.78125 18.875H18.2188C18.5812 18.875 18.875 18.5812 18.875 18.2188V3.78125C18.875 3.41881 18.5812 3.125 18.2188 3.125Z"
          stroke="#141522"
          strokeWidth="1.3125"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M10.3438 9.6875V14.9375"
          stroke="#141522"
          strokeWidth="1.3125"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M7.71875 9.6875V14.9375"
          stroke="#141522"
          strokeWidth="1.3125"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M10.3438 11.9844C10.3438 11.3752 10.5857 10.791 11.0165 10.3602C11.4472 9.92949 12.0315 9.6875 12.6406 9.6875C13.2498 9.6875 13.834 9.92949 14.2648 10.3602C14.6955 10.791 14.9375 11.3752 14.9375 11.9844V14.9375"
          stroke="#141522"
          strokeWidth="1.3125"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M7.71875 8.375C8.26241 8.375 8.70312 7.93428 8.70312 7.39062C8.70312 6.84697 8.26241 6.40625 7.71875 6.40625C7.17509 6.40625 6.73438 6.84697 6.73438 7.39062C6.73438 7.93428 7.17509 8.375 7.71875 8.375Z"
          fill="#141522"
        />
      </g>
      <defs>
        <clipPath id="clip0_789_8560">
          <rect
            width="21"
            height="21"
            fill="white"
            transform="translate(0.5 0.5)"
          />
        </clipPath>
      </defs>
    </svg>
  )
}

export default LinkedinSVG
