'use client'
import { useSelector, useDispatch } from 'react-redux'
import {
  setSideHeaderVisiblity,
  sideHeaderVisiblity,
} from '@/lib/redux/slices/metaSlice'
import Link from 'next/link'
import LogoIcon from '@/assets/icons/LogoIcon'
import { useState } from 'react'

const Hamburger = ({ data = [], role }: { data?: any[]; role?: string }) => {
  const dispatch = useDispatch()
  const isSideHeaderVisible = useSelector(sideHeaderVisiblity)

  const handleSideHeader = () => {
    dispatch(setSideHeaderVisiblity(!isSideHeaderVisible))
  }

  const [activeDropdown, setActiveDropdown] = useState<string | null>(null)

  const handleLinkClick = () => {
    setActiveDropdown(null)
    dispatch(setSideHeaderVisiblity(false))
  }

  return (
    <>
      {isSideHeaderVisible && data.length > 0 ? (
        <div className="fixed top-0 left-0 h-full w-full bg-white shadow-lg z-50 overflow-y-auto">
          {/* Header */}
          <div className="p-4 flex justify-between items-center border-b">
            <Link
              href="/"
              onClick={() => {
                dispatch(setSideHeaderVisiblity(false))
              }}
            >
              <LogoIcon height={50} width={100} />
            </Link>
            <svg
              onClick={handleSideHeader}
              width="30"
              height="30"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              className="cursor-pointer"
            >
              <path
                d="M6 18L18 6M6 6l12 12"
                stroke="#000"
                strokeWidth="2"
                strokeLinecap="round"
              />
            </svg>
          </div>

          {/* Navigation */}
          <ul className="flex flex-col font-medium gap-2 text-lg p-4 text-color-grey-1">
            {data?.map((item: any) => (
              <li key={item?.Id} className="relative cursor-pointer">
                <div
                  className="flex items-center justify-between py-2"
                  onClick={() => {
                    if (item?.children.length > 0) {
                      setActiveDropdown(
                        activeDropdown === item.Id ? null : item.Id,
                      )
                    } else {
                      handleLinkClick()
                    }
                  }}
                >
                  <Link
                    href={item?.Slug__c || '/#'}
                    className={`text-lg font-medium ${
                      item.Id === activeDropdown
                        ? 'font-semibold text-black'
                        : 'text-color-grey-6'
                    }`}
                  >
                    {item?.Name}
                  </Link>
                </div>

                {/* Dropdown children */}
                {item?.children.length > 0 && (
                  <ul
                    className={`
                      ml-4 pl-2 border-l space-y-2
                      ${activeDropdown === item.Id ? 'block' : 'hidden'}
                     
                    `}
                  >
                    {item?.children.map((child: any) => (
                      <li key={child.Id}>
                        <Link
                          href={child.Slug__c || '/#'}
                          className="block py-1 text-color-grey-6 hover:text-black"
                          onClick={handleLinkClick}
                        >
                          {child.Name}
                        </Link>

                        {/* Grandchildren */}
                        {child.children && child.children.length > 0 && (
                          <ul className="ml-4 space-y-1">
                            {child.children.map((grandChild: any) => (
                              <li key={grandChild.Id}>
                                <Link
                                  href={grandChild.Slug__c || '/#'}
                                  className="block text-sm text-color-grey-6 hover:text-black"
                                  onClick={handleLinkClick}
                                >
                                  {grandChild.Name}
                                </Link>
                              </li>
                            ))}
                          </ul>
                        )}
                      </li>
                    ))}
                  </ul>
                )}
              </li>
            ))}
          </ul>

          {/* Footer buttons */}
          <div className="flex inline-flex font-semibold text-lg items-center gap-4 rounded-[40px] home-buttons bg-color-cream-1">
            {role ? (
              <Link
                href={'/dashboard'}
                onClick={() => {
                  // if (isMobile) {
                  dispatch(setSideHeaderVisiblity(false))
                  // }
                }}
              >
                <div className="px-6 py-3 rounded-[40px] cursor-pointer hover:bg-color-yellow-1">
                  <span className="hover:font-bold">Dashboard</span>
                </div>
              </Link>
            ) : (
              <Link
                href={'/sign-in'}
                onClick={() => {
                  // if (isMobile) {
                  dispatch(setSideHeaderVisiblity(false))
                  // }
                }}
              >
                <div className="px-6 py-3 rounded-[40px] cursor-pointer hover:bg-color-yellow-1">
                  <span className="hover:font-bold">Sign in</span>
                </div>
              </Link>
            )}

            <Link
              href={'/donations'}
              onClick={() => {
                // if (isMobile) {
                dispatch(setSideHeaderVisiblity(false))
                // }
              }}
            >
              <div className="px-6 py-3 rounded-[40px] cursor-pointer bg-color-yellow-1 hover:bg-color-yellow-1">
                <span className="hover:font-bold">Donate</span>
              </div>
            </Link>
          </div>
        </div>
      ) : (
        // Hamburger button
        <svg
          width="30"
          height="30"
          viewBox="0 0 30 30"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          onClick={handleSideHeader}
          className="cursor-pointer"
        >
          <path
            d="M3.75 8.75H26.25"
            stroke="#141522"
            strokeWidth="1.5"
            strokeLinecap="round"
          />
          <path
            d="M3.75 15H26.25"
            stroke="#141522"
            strokeWidth="1.5"
            strokeLinecap="round"
          />
          <path
            d="M3.75 21.25H26.25"
            stroke="#141522"
            strokeWidth="1.5"
            strokeLinecap="round"
          />
        </svg>
      )}
    </>
  )
}

export default Hamburger
