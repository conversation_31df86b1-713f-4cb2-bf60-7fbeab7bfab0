'use client'
import { useRouter } from 'next/navigation'

const BackArrow = ({ onClick = null }: { onClick?: any }) => {
  const router = useRouter()
  const handleRoute = () => {
    if (onClick) {
      onClick()
      return
    }
    router.back()
  }
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className="cursor-pointer"
      onClick={handleRoute}
    >
      <path
        d="M9.57 5.92999L3.5 12L9.57 18.07"
        stroke="#141522"
        strokeWidth="1.5"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M20.4999 12H3.66992"
        stroke="#141522"
        strokeWidth="1.5"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export default BackArrow
