const InkPot = () => {
  return (
    <svg
      width="168"
      height="168"
      viewBox="0 0 168 168"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect width="168" height="168" fill="white" />
      <ellipse cx="84" cy="144" rx="53" ry="10" fill="#EAEAEA" />
      <path
        d="M96.5981 56.7842C95.9442 55.9667 95.6231 55.4494 94.8192 55.3759C94.0161 55.3018 93.2435 55.7624 92.8549 56.5468C86.1316 70.12 83.6311 87.0102 85.4075 102.23C82.3942 109.238 77.7486 124.554 77.6904 124.75C73.0594 137.893 88.0915 103.002 89.0981 104.733C102.392 98.8475 112.694 89.4594 119.72 76.8282C120.128 76.0936 120.12 75.1705 119.697 74.4583C118.659 72.7105 114.953 73.9088 113.049 74.0161C116.26 72.3792 120.014 69.7922 124.207 66.3242C124.511 66.072 124.748 65.7314 124.886 65.3406C131.458 46.8783 130.467 28.4281 130.006 23.2125C129.945 22.5135 129.584 21.8932 129.036 21.5398C128.488 21.1865 127.816 21.1444 127.226 21.4233C121.428 24.1641 116.13 27.5492 111.476 31.4832C110.861 32.0038 110.563 32.8602 110.71 33.6895C110.942 35.0058 112.069 39.8528 112.069 39.8528C111.42 38.3692 110.495 35.1305 108.908 34.838C106.291 34.3528 99.5372 44.5358 97.9803 47.0039C97.7668 47.3414 97.6417 47.7363 97.619 48.1445C96.978 59.3593 102.454 64.1105 96.5981 56.7842Z"
        fill="#C067C5"
      />
      <path
        d="M118.767 62.2729C115.104 66.8951 105.914 68.2103 103.237 68.88C107.808 68.7846 117.211 68.2631 118.767 62.2729Z"
        fill="#9A479E"
      />
      <path
        d="M120.078 53.4821C116.326 58.5184 108.862 59.7743 107.344 60.3C111.066 60.1406 118.727 59.4879 120.078 53.4821Z"
        fill="#9A479E"
      />
      <path
        d="M126.167 43.6948C127.221 42.9925 115.63 49.2407 113.89 48.4827C119.766 50.1486 121.123 48.5264 126.167 43.6948Z"
        fill="#9A479E"
      />
      <path
        d="M99.5607 50.2841C99.6892 49.8201 104.639 65.5015 105.083 66.1483C101.133 59.3647 100.631 57.8381 99.5607 50.2841Z"
        fill="#9A479E"
      />
      <path
        d="M106.577 45.4501C106.638 44.3309 107.033 58.512 108.141 59.5883C105.411 52.981 105.48 51.765 106.577 45.4501Z"
        fill="#9A479E"
      />
      <path
        d="M87.6027 103.731C98.069 78.1778 109.005 53.264 125.001 31.1926C123.343 30.775 87.2795 103.795 87.6027 103.731Z"
        fill="#9A479E"
      />
      <path
        d="M113.943 36.3877C114.159 35.0586 113.048 49.1436 114.325 50.6428C110.772 45.2571 111.661 43.2846 113.943 36.3877Z"
        fill="#9A479E"
      />
      <path
        d="M94.4014 114.93H94.2923V112.209C95.8472 111.743 96.963 110.485 96.963 109.003C96.963 107.122 95.1713 105.6 92.963 105.6H75.0418C72.8318 105.6 71.0418 107.122 71.0418 109.003C71.0418 110.485 72.1557 111.743 73.7126 112.209V114.93H73.6017C68.0792 114.93 63.6006 118.739 63.6006 123.438V138.58C63.6006 143.278 68.0792 147.087 73.6017 147.087H94.4014C99.9237 147.087 104.401 143.278 104.401 138.58V123.438C104.402 118.739 99.9237 114.93 94.4014 114.93Z"
        fill="#402151"
      />
      <path
        d="M81.4188 130.505H83.3041C83.4208 130.505 83.5021 130.624 83.4619 130.737L81.0113 137.766C80.9455 137.951 81.1824 138.087 81.3013 137.933L87.8826 128.482C87.97 128.369 87.8914 128.202 87.7507 128.202H85.7898C85.6632 128.202 85.5816 128.063 85.6413 127.948L87.9802 124.249C88.0396 124.134 87.9583 124 87.8317 124H83.6612C83.5549 124 83.46 124.074 83.424 124.176L81.2608 130.273C81.2214 130.386 81.3024 130.505 81.4191 130.505"
        fill="#6F3390"
      />
    </svg>
  )
}

export default InkPot
