const ArrowIcon = ({
  showDownArrow,
  isActive = false,
}: {
  showDownArrow: boolean
  isActive?: boolean
}) => {
  return (
    <svg
      width="18"
      height="18"
      viewBox="0 0 18 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      style={{
        transform: showDownArrow ? 'rotate(180deg)' : 'rotate(0deg)',
        transition: 'transform 0.3s ease',
      }}
    >
      <path
        d="M14.9405 11.2875L10.0505 6.39748C9.47305 5.81998 8.52805 5.81998 7.95055 6.39748L3.06055 11.2875"
        stroke={isActive ? `#141522` : `#9C9CA4`}
        strokeWidth="1.125"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export default ArrowIcon
