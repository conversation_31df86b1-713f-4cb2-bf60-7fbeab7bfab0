const VisaIcon = () => {
  return (
    <div className="border-[2px] px-3 py-2 rounded-md h-[47px] w-[69px] flex items-center justify-center">
      <svg
        width="48"
        height="30"
        viewBox="0 0 48 30"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <rect width="48" height="30" fill="url(#pattern0_2031_29375)" />
        <defs>
          <pattern
            id="pattern0_2031_29375"
            patternContentUnits="objectBoundingBox"
            width="1"
            height="1"
          >
            <use
              xlinkHref="#image0_2031_29375"
              transform="matrix(0.00195312 0 0 0.003125 0 -0.3)"
            />
          </pattern>
          <image
            id="image0_2031_29375"
            width="512"
            height="512"
            xlinkHref="data:image/png;base64,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"
          />
        </defs>
      </svg>
    </div>
  )
}

export default VisaIcon
