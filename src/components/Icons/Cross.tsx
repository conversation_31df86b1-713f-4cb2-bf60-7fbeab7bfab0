'use client'
import { useSelector, useDispatch } from 'react-redux'
import {
  setSideHeaderVisiblity,
  sideHeaderVisiblity,
} from '@/lib/redux/slices/metaSlice'

const Cross = ({
  width = 30,
  height = 30,
  handleClick,
}: {
  width?: number
  height?: number
  handleClick?: () => void
}) => {
  const dispatch = useDispatch()
  const isSideHeaderVisible = useSelector(sideHeaderVisiblity)

  const handleSideHeader = () => {
    dispatch(setSideHeaderVisiblity(!isSideHeaderVisible))
  }

  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 30 30"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      onClick={() => (handleClick ? handleClick() : handleSideHeader())}
      className="cursor-pointer"
    >
      <path
        d="M6.25098 6.25L23.7498 23.7488"
        stroke="#141522"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M6.2502 23.7488L23.749 6.25"
        stroke="#141522"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export default Cross
