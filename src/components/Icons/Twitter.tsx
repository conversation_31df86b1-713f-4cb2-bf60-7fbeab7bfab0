const Twitter = () => {
  return (
    <svg
      width="44"
      height="44"
      viewBox="0 0 44 44"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect width="44" height="44" rx="10" fill="black" />
      <g clipPath="url(#clip0_439_3746)">
        <mask
          id="mask0_439_3746"
          style={{ maskType: 'luminance' }}
          maskUnits="userSpaceOnUse"
          x="9"
          y="9"
          width="26"
          height="26"
        >
          <path d="M9 9H35V35H9V9Z" fill="white" />
        </mask>
        <g mask="url(#mask0_439_3746)">
          <path
            d="M29.475 10.2188H33.4623L24.7523 20.199L35 33.7822H26.9771L20.6889 25.5458L13.5017 33.7822H9.51071L18.8261 23.1036L9 10.2206H17.2271L22.9026 17.7476L29.475 10.2188ZM28.0729 31.3902H30.2829L16.02 12.4863H13.6503L28.0729 31.3902Z"
            fill="white"
          />
        </g>
      </g>
      <defs>
        <clipPath id="clip0_439_3746">
          <rect
            width="26"
            height="26"
            fill="white"
            transform="translate(9 9)"
          />
        </clipPath>
      </defs>
    </svg>
  )
}

export default Twitter
