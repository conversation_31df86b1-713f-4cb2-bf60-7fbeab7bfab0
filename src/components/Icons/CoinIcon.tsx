// Icon.tsx
import React from 'react'

interface IconProps {
  width?: string
  height?: string
  className?: string
}

const CoinIcon: React.FC<IconProps> = ({
  width = '20px',
  height = '20px',
  className = '',
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
      viewBox="0 0 256 256"
      width={width}
      height={height}
      className={className}
    >
      <g transform="translate(1.4065934065934016 1.4065934065934016) scale(2.81 2.81)">
        <path
          d="M 50.103 90 h -8.7 C 22.378 90 6.956 69.853 6.956 45 S 22.378 0 41.403 0 l 7.194 0 L 50.103 90 z"
          fill="rgb(228,175,24)"
        />
        <path
          d="M 44.555 1.431 H 32.839 c -1.989 0.665 -3.912 1.542 -5.745 2.637 h 11.8 C 40.704 2.987 42.593 2.094 44.555 1.431 z"
          fill="rgb(196,146,20)"
        />
        <path
          d="M 33.116 8.454 H 21.315 c -0.971 0.913 -1.906 1.887 -2.798 2.924 h 11.8 C 31.21 10.341 32.145 9.367 33.116 8.454 z"
          fill="rgb(196,146,20)"
        />
        <path
          d="M 26.112 17.225 H 14.311 c -0.569 0.946 -1.113 1.919 -1.623 2.924 h 11.8 C 24.999 19.144 25.543 18.171 26.112 17.225 z"
          fill="rgb(196,146,20)"
        />
        <path
          d="M 21.978 25.996 H 10.178 c -0.342 0.957 -0.657 1.932 -0.948 2.924 h 11.8 C 21.321 27.928 21.637 26.953 21.978 25.996 z"
          fill="rgb(196,146,20)"
        />
        <path
          d="M 19.662 34.767 h -11.8 c -0.172 0.964 -0.323 1.937 -0.446 2.924 h 11.8 C 19.339 36.704 19.491 35.731 19.662 34.767 z"
          fill="rgb(196,146,20)"
        />
        <path
          d="M 18.757 45 c 0 -0.49 0.016 -0.975 0.028 -1.462 h -11.8 C 6.973 44.025 6.956 44.51 6.956 45 s 0.016 0.975 0.028 1.462 h 11.8 C 18.773 45.975 18.757 45.49 18.757 45 z"
          fill="rgb(196,146,20)"
        />
        <path
          d="M 19.216 52.309 h -11.8 c 0.123 0.986 0.274 1.96 0.446 2.924 h 11.8 C 19.491 54.269 19.339 53.296 19.216 52.309 z"
          fill="rgb(196,146,20)"
        />
        <path
          d="M 21.03 61.08 H 9.23 c 0.291 0.992 0.606 1.967 0.948 2.924 h 11.801 C 21.637 63.047 21.321 62.072 21.03 61.08 z"
          fill="rgb(196,146,20)"
        />
        <path
          d="M 24.488 69.851 h -11.8 c 0.511 1.005 1.055 1.978 1.623 2.924 h 11.801 C 25.543 71.829 24.999 70.856 24.488 69.851 z"
          fill="rgb(196,146,20)"
        />
        <path
          d="M 30.318 78.622 h -11.8 c 0.892 1.037 1.826 2.011 2.798 2.924 h 11.801 C 32.145 80.633 31.21 79.659 30.318 78.622 z"
          fill="rgb(196,146,20)"
        />
        <path
          d="M 38.256 85.554 l -11.163 0.378 c 1.856 1.109 3.804 1.994 5.819 2.662 h 11.715 C 42.41 87.851 40.278 86.828 38.256 85.554 z"
          fill="rgb(196,146,20)"
        />
        <ellipse
          cx="48.597"
          cy="45"
          rx="34.447"
          ry="45"
          fill="rgb(255,217,73)"
        />
        <ellipse
          cx="48.592"
          cy="45"
          rx="26.792"
          ry="35"
          fill="rgb(228,175,24)"
        />
        <path
          d="M 63.493 53.266 c -0.072 -6.71 -5.217 -11.576 -11.467 -10.847 l -4.533 0.529 c -3.592 0.419 -6.548 -2.377 -6.589 -6.233 c -0.042 -3.856 2.847 -7.334 6.438 -7.753 l 8.887 -1.037 c 1.332 -0.155 2.399 -1.44 2.383 -2.87 c -0.015 -1.43 -1.107 -2.463 -2.439 -2.307 l -4.209 0.491 l -0.043 -3.986 c -0.015 -1.43 -1.107 -2.463 -2.439 -2.307 c -1.332 0.155 -2.399 1.44 -2.383 2.87 l 0.043 3.994 c -6.183 0.806 -11.132 6.81 -11.06 13.468 c 0.072 6.711 5.217 11.577 11.468 10.847 l 4.533 -0.529 c 3.592 -0.419 6.547 2.378 6.589 6.233 c 0.042 3.856 -2.846 7.333 -6.438 7.753 l -8.887 1.037 c -1.332 0.155 -2.399 1.44 -2.383 2.87 s 1.107 2.463 2.439 2.307 l 4.209 -0.491 l 0.043 3.968 c 0.015 1.43 1.107 2.463 2.439 2.307 c 1.332 -0.155 2.399 -1.44 2.383 -2.87 l -0.043 -3.976 C 58.616 65.929 63.565 59.925 63.493 53.266 z"
          fill="rgb(255,217,73)"
        />
      </g>
    </svg>
  )
}

export default CoinIcon
