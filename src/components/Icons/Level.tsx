import React from 'react'

const Level = ({ level = 4 }: { level?: number }) => {
  switch (level) {
    case 1:
      return (
        <svg
          width="13"
          height="12"
          viewBox="0 0 13 12"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <rect
            y="6.46143"
            width="3"
            height="5.53846"
            rx="1.5"
            fill="#25C78B"
          />
          <rect
            x="5"
            y="3.69238"
            width="3"
            height="8.30769"
            rx="1.5"
            fill="#F5F5F7"
          />
          <rect x="10" width="3" height="12" rx="1.5" fill="#F5F5F7" />
        </svg>
      )
    case 2:
      return (
        <svg
          width="13"
          height="12"
          viewBox="0 0 13 12"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <rect
            y="6.46155"
            width="3"
            height="5.53846"
            rx="1.5"
            fill="#FFBB54"
          />
          <rect
            x="5"
            y="3.69232"
            width="3"
            height="8.30769"
            rx="1.5"
            fill="#FFBB54"
          />
          <rect x="10" width="3" height="12" rx="1.5" fill="#DFDFDF" />
        </svg>
      )
    case 3:
      return (
        <svg
          width="14"
          height="12"
          viewBox="0 0 14 12"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <rect
            x="0.5"
            y="6.46155"
            width="3"
            height="5.53846"
            rx="1.5"
            fill="#DB5962"
          />
          <rect
            x="5.5"
            y="3.69226"
            width="3"
            height="8.30769"
            rx="1.5"
            fill="#DB5962"
          />
          <rect x="10.5" width="3" height="12" rx="1.5" fill="#DB5962" />
        </svg>
      )
    default:
      return (
        <svg
          width="18"
          height="18"
          viewBox="0 0 18 18"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M14 2V15.5"
            stroke="#9C9CA4"
            strokeWidth="1.25"
            strokeLinecap="round"
          />
          <path
            d="M9 6.5V15.5"
            stroke="#9C9CA4"
            strokeWidth="1.25"
            strokeLinecap="round"
          />
          <path
            d="M4 12.5V15.5"
            stroke="#9C9CA4"
            strokeWidth="1.25"
            strokeLinecap="round"
          />
        </svg>
      )
  }
}

export default Level
