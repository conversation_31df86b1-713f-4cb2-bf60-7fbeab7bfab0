'use client'
import React, { useState } from 'react'
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from 'recharts'

type DayData = {
  day: string
  donations: number
}

type WeekData = {
  [key: string]: DayData[]
}

const weekData: WeekData = {
  'This Week': [
    { day: 'S', donations: 1.8 },
    { day: 'M', donations: 2.8 },
    { day: 'T', donations: 2.0 },
    { day: 'W', donations: 3.5 },
    { day: 'T', donations: 2.5 },
    { day: 'F', donations: 3.0 },
    { day: 'S', donations: 2.8 },
  ],
  'Last Week': [
    { day: 'S', donations: 2.0 },
    { day: 'M', donations: 2.5 },
    { day: 'T', donations: 1.8 },
    { day: 'W', donations: 3.2 },
    { day: 'T', donations: 2.7 },
    { day: 'F', donations: 3.1 },
    { day: 'S', donations: 2.6 },
  ],
  '2 Weeks Ago': [
    { day: 'S', donations: 1.5 },
    { day: 'M', donations: 2.2 },
    { day: 'T', donations: 2.8 },
    { day: 'W', donations: 3.0 },
    { day: 'T', donations: 2.3 },
    { day: 'F', donations: 2.9 },
    { day: 'S', donations: 3.3 },
  ],
}

const DonationsChart: React.FC = () => {
  const [selectedWeek, setSelectedWeek] =
    useState<keyof typeof weekData>('This Week')
  const [isDropdownOpen, setIsDropdownOpen] = useState(false)

  const toggleDropdown = () => setIsDropdownOpen(!isDropdownOpen)

  const selectWeek = (week: keyof typeof weekData) => {
    setSelectedWeek(week)
    setIsDropdownOpen(false)
  }

  return (
    <div className="p-4 bg-white text-color-black rounded-2xl w-full h-72">
      <div className="flex justify-between gap-4 items-center mb-4">
        <h2 className="text-base font-semibold">Donations</h2>
        <div className="relative">
          <button
            onClick={toggleDropdown}
            className="flex items-center focus:outline-none"
          >
            <span className="mr-2">{selectedWeek}</span>
            <svg
              className="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 9l-7 7-7-7"
              />
            </svg>
          </button>
          {isDropdownOpen && (
            <div className="absolute text-xs right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10">
              {(Object.keys(weekData) as Array<keyof typeof weekData>).map(
                (week) => (
                  <button
                    key={week}
                    className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    onClick={() => selectWeek(week)}
                  >
                    {week}
                  </button>
                ),
              )}
            </div>
          )}
        </div>
      </div>
      <div
        style={{ position: 'relative', width: '100%', paddingBottom: '200px' }}
      >
        <div
          style={{
            position: 'absolute',
            left: 0,
            right: 0,
            bottom: 0,
            top: 0,
          }}
        >
          <ResponsiveContainer width="100%" height={200}>
            <LineChart data={weekData[selectedWeek]}>
              <CartesianGrid strokeDasharray="3 3" vertical={false} />
              <XAxis dataKey="day" axisLine={false} tickLine={false} />
              <YAxis
                axisLine={false}
                tickLine={false}
                ticks={[0, 1, 2, 3, 4]}
                domain={[0, 4]}
                tickMargin={40}
              />
              <Tooltip />
              <Line
                type="monotone"
                dataKey="donations"
                stroke="#141522"
                strokeWidth={2}
                dot={false}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  )
}

export default DonationsChart
