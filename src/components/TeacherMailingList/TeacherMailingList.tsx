'use client'
import React, { useEffect, useMemo } from 'react'
import NoResult from '../NoResult/NoResult'
import MailIcon from '../Icons/MailIcon'
import CustomButton from '../CustomComponents/CustomButton/CustomButton'
import Cross from '../Icons/Cross'
import { useApi } from '@/hooks/useApi'
import {
  addBulkEmailsToMailingList,
  createMailingList,
  getMailingLists,
} from '@/lib/actions/mailingList.actions'
import { toast } from 'react-toastify'
import GreenTick from '../Icons/GreenTick'
import MailingCard from '../MailingCard/MailingCard'
import MailingListDetailSkeleton from '../Skeletons/MailingListDetailSkeleton'
import Mail from '../Mail/Mail'
import CopyIcon from '../Icons/CopyIcon'
import clsx from 'clsx'
import useModal from '@/hooks/useModal'
import NotifyStudentModal from '../NotifyStudentModal/NotifyStudentModal'
import MailingListSkeleton from '../Skeleton/MailingListSkeleton'

type DashboardListProps = {
  isDashboardList?: boolean
}

const TeacherMailingList = ({ isDashboardList }: DashboardListProps) => {
  const [showAddMailingList, setShowAddMailingList] = React.useState(false)
  const [mailingName, setMailingName] = React.useState<string>('')
  const [createdMailListId, setCreatedMailListId] = React.useState<string>('')
  const [isCreated, setIsCreated] = React.useState(false)
  const [mainListData, setMainListData] = React.useState<any>([])
  const [isListSaved, setIsListSaved] = React.useState(false)
  const [mailingList, setMailingList] = React.useState<string[]>([])
  const [mail, setMail] = React.useState<string>('')
  const [isEdit, setIsEdit] = React.useState(false)
  const [showLoader, setShowLoader] = React.useState(false)
  const [modalMode, setModalMode] = React.useState<'create' | 'view' | 'edit'>(
    'create',
  )
  const [modal, showModal] = useModal()
  const [expandedCard, setExpandedCard] = React.useState<string | null>(null)

  const [copyAllMailingList, setCopyAllMailingList] = React.useState(false)
  const [createMailingResponse, createMailing] = useApi(
    (access: string, name: string) => createMailingList(access, name),
  )
  const [addBultMailingResponse, addBulkMailing] = useApi(
    (access: string, id: string, emails: string[]) =>
      addBulkEmailsToMailingList(access, id, emails),
  )
  const [mailingListResponse, fetchMailingList] = useApi((access: string) =>
    getMailingLists(access),
  )

  useEffect(() => {
    if (mailingListResponse.isSuccess) {
      setMainListData(mailingListResponse?.data?.data?.data || [])
    }
  }, [mailingListResponse])

  useEffect(() => {
    fetchMailingList()
  }, [])

  useEffect(() => {
    if (createMailingResponse.isSuccess) {
      toast.success('Mailing list created successfully', { autoClose: 2000 })
      setIsListSaved(true)
      setCreatedMailListId(createMailingResponse?.data?.data?.id)
      setIsCreated(true)
      setIsEdit(true)
      setMainListData([...mainListData, createMailingResponse?.data?.data])
    }
  }, [createMailingResponse])

  useEffect(() => {
    if (addBultMailingResponse.isSuccess) {
      toast.success(`${mailingName} updated successfully`, { autoClose: 2000 })
      setShowAddMailingList(false)
      setMailingList([])
      setMailingName('')
      setIsEdit(false)
      setIsCreated(false)
      setCreatedMailListId('')
      setIsListSaved(false)
      setModalMode('create')
    }
  }, [addBultMailingResponse])

  useEffect(() => {
    if (expandedCard && createdMailListId) {
      window.dispatchEvent(
        new CustomEvent(`emailsUpdated-${createdMailListId}`),
      )
    }
  }, [expandedCard, createdMailListId])

  const handleAddClick = () => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!mail) {
      toast.error('Please enter an email address')
      return
    }
    if (!emailRegex.test(mail)) {
      toast.error('Please enter a valid email address')
      return
    }
    if (mailingList.includes(mail)) {
      toast.error('This email is already in the list')
      return
    }
    setMailingList([...mailingList, mail])
    setMail('')
  }

  const handleSaveMailingList = () => {
    if (isCreated) {
      addBulkMailing(createdMailListId, mailingList)
    } else {
      const mail = mailingList.map((item: any) =>
        item?.email ? item?.email : item,
      )
      addBulkMailing(createdMailListId, mail)
    }
  }

  const handleCopy = (text: string) => {
    if (!text) return
    navigator.clipboard.writeText(text)
    setCopyAllMailingList(true)
    setTimeout(() => {
      setCopyAllMailingList(false)
    }, 2000)
  }

  const allMailingList = useMemo(() => {
    return mailingList.map((item: any) => item.email).join(', ')
  }, [mailingList])

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleAddClick()
    }
  }

  const resetModal = () => {
    setShowAddMailingList(false)
    setMailingList([])
    setMailingName('')
    setIsListSaved(false)
    setIsEdit(false)
    setIsCreated(false)
    setCreatedMailListId('')
    setModalMode('create')
  }

  const showNotificationModal = (
    mailingListId: string,
    notifySingle?: boolean,
    mail?: string,
  ) => {
    showModal({
      title: '',
      contentFn: (onClose: any) => (
        <NotifyStudentModal
          mailingListId={mailingListId}
          onClose={onClose}
          notifySingle={notifySingle}
          mail={mail}
        />
      ),
      closeOnClickOutside: true,
      size: 'md',
      showCloseButton: false,
    })
  }

  return (
    <div
      className={clsx('w-full flex justify-center rounded-2xl', {
        'p-3 sm:p-5': !isDashboardList,
      })}
    >
      <div
        className={clsx('flex justify-center w-full', {
          'md:w-2/3': !isDashboardList,
          'p-4 sm:p-6 md:p-10': !isDashboardList,
        })}
      >
        {/* Modal */}
        {showAddMailingList && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-2 sm:p-4">
            <div className="bg-white w-full max-w-lg sm:max-w-2xl md:max-w-3xl max-h-[90vh] overflow-y-auto rounded-2xl shadow-2xl">
              {/* Modal Header */}
              <div className="sticky top-0 bg-white border-b border-gray-200 px-4 sm:px-6 py-3 sm:py-4 rounded-t-2xl">
                <div className="flex justify-between items-center">
                  <h1 className="font-bold text-xl sm:text-2xl text-gray-900">
                    {modalMode === 'view'
                      ? 'View Mailing List'
                      : modalMode === 'edit'
                        ? 'Edit Mailing List'
                        : 'Create Mailing List'}
                  </h1>
                  <Cross handleClick={resetModal} />
                </div>
              </div>
              <div className="px-4 sm:px-6 py-4">
                {showLoader ? (
                  <MailingListDetailSkeleton />
                ) : (
                  <div className="space-y-3 sm:space-y-4">
                    {isListSaved && (
                      <div className="bg-green-50 border border-green-200 rounded-xl p-3 sm:p-4 mb-3">
                        {isCreated ? (
                          <div className="flex items-center gap-2 sm:gap-3">
                            <GreenTick />
                            <div>
                              <h3 className="font-semibold text-green-800 text-sm sm:text-base">
                                List Created Successfully!
                              </h3>
                              <p className="text-green-700 text-xs sm:text-sm">
                                {`${mailingName} is ready for students`}
                              </p>
                            </div>
                          </div>
                        ) : (
                          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3">
                            <div className="flex items-center gap-2 sm:gap-3">
                              <div className="w-8 h-8 sm:w-10 sm:h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                <MailIcon />
                              </div>
                              <div>
                                <h3 className="font-semibold text-gray-900 text-sm sm:text-base">
                                  {mailingName}
                                </h3>
                                <p className="text-gray-600 text-xs sm:text-sm">
                                  {mailingList.length} email(s) in this list
                                </p>
                              </div>
                            </div>
                            {mailingList.length > 0 && (
                              <button
                                onClick={() => handleCopy(allMailingList)}
                                className={clsx(
                                  'flex items-center gap-2 px-3 py-2 rounded-lg font-medium text-xs sm:text-sm transition-all duration-200',
                                  copyAllMailingList
                                    ? 'bg-green-100 text-green-700 border border-green-200'
                                    : 'bg-gray-100 hover:bg-gray-200 text-gray-700 border border-gray-200 hover:border-gray-300',
                                )}
                                disabled={copyAllMailingList}
                              >
                                {copyAllMailingList ? (
                                  <>
                                    <GreenTick />
                                    <span>Copied!</span>
                                  </>
                                ) : (
                                  <>
                                    <CopyIcon />
                                    <span>Copy All</span>
                                  </>
                                )}
                              </button>
                            )}
                          </div>
                        )}
                      </div>
                    )}

                    {/* List Name Input */}
                    {!isListSaved && (
                      <div className="space-y-2 sm:space-y-3">
                        <div>
                          <label className="block text-sm font-semibold text-gray-700 mb-1 sm:mb-2">
                            List Name
                          </label>
                          <p className="text-xs text-gray-500 mb-2 sm:mb-3">
                            Give your list a descriptive name
                          </p>
                        </div>
                        <div className="flex flex-col h-24 sm:h-12 sm:flex-row gap-2 sm:gap-3">
                          <input
                            className="flex-1 h-10 sm:h-12 px-3 sm:px-4 text-gray-900 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                            placeholder="e.g., Mathematics Class 2024"
                            type="text"
                            value={mailingName}
                            onChange={(e) => setMailingName(e.target.value)}
                            disabled={isListSaved}
                          />
                          <CustomButton
                            onClick={() => createMailing(mailingName)}
                            title="Create List"
                            isLoading={createMailingResponse.isFetching}
                            backgroundColor="bg-blue-600 hover:bg-blue-700"
                            textColor="text-white"
                            width="w-full sm:w-32"
                            height={12}
                            isDisabled={!mailingName.trim()}
                          />
                        </div>
                      </div>
                    )}

                    {((isListSaved && isEdit) || isCreated) && (
                      <>
                        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2">
                          <label className="block text-sm font-semibold text-gray-700">
                            Add Students
                          </label>
                          {mailingList.length > 0 && (
                            <h4 className="text-sm font-semibold text-gray-700">
                              Emails ({mailingList.length})
                            </h4>
                          )}
                        </div>
                        <div className="flex flex-col h-24 sm:h-12 sm:flex-row gap-2 sm:gap-3">
                          <input
                            value={mail}
                            className="flex-1 h-10 sm:h-12 px-3 sm:px-4 text-gray-900 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                            onChange={(e) => setMail(e.target.value)}
                            onKeyPress={handleKeyPress}
                            type="email"
                            placeholder="<EMAIL>"
                          />
                          <button
                            onClick={handleAddClick}
                            className="bg-blue-600 hover:bg-blue-700 h-10 sm:h-12 rounded-lg text-white px-4 sm:px-6 font-medium text-sm transition-colors disabled:opacity-50"
                            disabled={!mail.trim()}
                          >
                            Add
                          </button>
                        </div>
                      </>
                    )}

                    {mailingList?.map((item: any, index: number) => (
                      <Mail
                        key={item?.id || `${item}-${index}`}
                        mail={item}
                        isEdit={isEdit}
                        setMailingList={setMailingList}
                        showNotificationModal={showNotificationModal}
                      />
                    ))}

                    {mailingList.length > 0 && isEdit && (
                      <div className="flex justify-end pt-3 sm:pt-4 border-t border-gray-100">
                        <CustomButton
                          title={`Save ${mailingList.length} Email${mailingList.length !== 1 ? 's' : ''}`}
                          isLoading={addBultMailingResponse.isFetching}
                          onClick={handleSaveMailingList}
                          backgroundColor="bg-green-600 hover:bg-green-700"
                          textColor="text-white"
                          height={12}
                          width="w-full sm:w-auto px-4 sm:px-6"
                        />
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Main Section */}
        <div className="w-full text-sm font-semibold space-y-6">
          {!isDashboardList && (
            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2">
              <div>
                <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">
                  Mailing Lists
                </h1>
                <p className="text-gray-600 mt-1 text-sm sm:text-base">
                  Manage your student communication lists
                </p>
              </div>
            </div>
          )}

          {mailingListResponse.isFetching && (
            <div className="flex items-center justify-center">
              <div className="w-full flex justify-center rounded-2xl">
                <MailingListSkeleton count={4} />
              </div>
            </div>
          )}

          <div className="space-y-4 relative">
            {!mailingListResponse.isFetching &&
              (isDashboardList ? mainListData.slice(0, 3) : mainListData).map(
                (list: any, index: number) => (
                  <MailingCard
                    index={index}
                    data={list}
                    setMainListData={setMainListData}
                    mainListData={mainListData}
                    setShowAddMailingList={setShowAddMailingList}
                    setMailingList={setMailingList}
                    setIsEdit={setIsEdit}
                    setMailingName={setMailingName}
                    setIsListSaved={setIsListSaved}
                    setCreatedMailListId={setCreatedMailListId}
                    setShowLoader={setShowLoader}
                    setModalMode={setModalMode}
                    showNotificationModal={showNotificationModal}
                    expandedCard={expandedCard}
                    setExpandedCard={setExpandedCard}
                    key={list.id}
                  />
                ),
              )}
          </div>

          {mainListData?.length != 0 && (
            <button
              className="flex items-center gap-2 text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-all text-sm font-semibold px-2 py-1 sm:px-3 sm:py-2"
              onClick={() => {
                setModalMode('create')
                setShowAddMailingList(true)
              }}
            >
              <span className="text-lg">+</span>
              <span>Create new mailing list</span>
            </button>
          )}

          {!mailingListResponse.isFetching && mainListData.length === 0 && (
            <div className="flex items-center justify-center">
              <NoResult
                icon={<MailIcon />}
                btnText="Create Mailing List"
                desc="Stay connected with your students effortlessly. You can create unlimited mailing lists!"
                btnClick={() => {
                  setModalMode('create')
                  setShowAddMailingList(true)
                }}
                background="color-grey-5"
              />
            </div>
          )}
        </div>
      </div>
      {modal}
    </div>
  )
}

export default TeacherMailingList
