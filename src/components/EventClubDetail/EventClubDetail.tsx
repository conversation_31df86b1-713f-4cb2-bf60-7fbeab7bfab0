'use client'
import React, { useEffect, useMemo } from 'react'
import Person from '../Icons/Person'
import CalendarIcon from '../Icons/CalendarIcon'
import EyeIcon from '../Icons/EyeIcon'
import { CLASS_DETAILS_TABS } from '@/utils/constants'
import { ROLES } from '@/types/enum'
import { formatDateToAmerican } from '@/utils/utils'
import ClassAttendeesCard from '../ClassAttendeesCard/ClassAttendeesCard'
import TeacherBanner from '../TeacherBanner/TeacherBanner'
import { useRouter } from 'next/navigation'
import { toast } from 'react-toastify'
import ClassTabs from '../ClassTabs/ClassTabs'
import useModal from '@/hooks/useModal'
import AddressModal from '../AddressModal/AddressModal'

interface User {
  id: number
  name: string
  email: string
}

const EventClubDetail = ({
  eventClubData,
  userData,
}: {
  eventClubData: any
  userData: any
}) => {
  const [activeTab, setActiveTab] = React.useState('class-details')
  const users: User[] = eventClubData?.classAttendees || []
  const router = useRouter()
  const [addressModal, showAddressModal] = useModal()
  let tabs: { key: string; value: string }[] = [
    { key: 'About', value: 'class-details' },
  ]

  useEffect(() => {
    let timmer: NodeJS.Timeout
    if (eventClubData?.errors) {
      toast.error(eventClubData?.errors[0])
      timmer = setTimeout(() => {
        router.push('/dashboard')
      }, 1000)
    }
    return () => {
      if (timmer) {
        clearTimeout(timmer)
      }
    }
  }, [eventClubData])

  const classTabs = useMemo(() => {
    if (
      eventClubData?.pastMeetings?.length +
        eventClubData?.upcomingMeetings?.length >
      1
    ) {
      return [...tabs, { key: 'Schedule', value: 'schedule' }]
    }
    return tabs
  }, [eventClubData])

  return (
    <div className="bg-white rounded-2xl p-4 md:p-7 space-y-5">
      {userData?.role === ROLES.Teacher && (
        <div className="flex justify-start w-full text-base font-semibold border-b-2 border-color-black mb-5 overflow-x-auto">
          <div className="w-max flex justify-center gap-5 border-color-black">
            {CLASS_DETAILS_TABS.map((tab, index) => (
              <div
                key={index}
                onClick={() => setActiveTab(tab.key)}
                className={`pb-3 cursor-pointer whitespace-nowrap ${activeTab === tab.key ? 'border-b-2 border-color-black' : ''}`}
              >
                {tab.title}
              </div>
            ))}
          </div>
        </div>
      )}

      {activeTab === 'class-attendees' ? (
        <div className="w-full overflow-x-auto">
          <div className="grid grid-cols-2 md:grid-cols-[1fr,1.5fr,48px] gap-4 mb-4 min-w-[400px]">
            <h2 className="font-extrabold text-base">Name</h2>
            <h2 className="font-extrabold text-base">Email</h2>
            <div className="hidden md:block" />
          </div>
          <div className="space-y-4 min-w-[400px]">
            {users.map((user) => (
              <ClassAttendeesCard key={user.id} user={user} />
            ))}
          </div>
        </div>
      ) : (
        <>
          <h1 className="font-bold text-2xl md:text-3xl break-words">
            {eventClubData?.Title__c}
          </h1>
          <div className="flex items-center justify-between mt-4 flex-wrap gap-3">
            <div className="flex items-center gap-4 flex-wrap">
              {(eventClubData?.associatedTeachers || []).length > 0 && (
                <TeacherBanner
                  teacherData={eventClubData?.associatedTeachers}
                  isClickable={false}
                />
              )}
            </div>
          </div>

          {eventClubData?.Type__c === 'Hybrid' ? (
            <div className="flex flex-col md:flex-row items-stretch md:items-center justify-between gap-5 w-full">
              <div className="bg-color-grey-10 rounded-2xl p-5 space-y-3 w-full flex items-center justify-between">
                <div className="space-y-3">
                  <h2 className="text-sm text-color-grey-12">In Person</h2>
                  <h3 className="text-sm font-bold break-words">
                    {eventClubData?.Room__c ? (
                      <span>{eventClubData.Room__c}</span>
                    ) : null}
                    {eventClubData?.venue?.name ? (
                      <span>
                        {eventClubData?.Room__c ? ', ' : ''}
                        {eventClubData.venue.name}
                      </span>
                    ) : null}
                  </h3>
                </div>
                <button
                  className="ml-2 p-2 rounded hover:bg-gray-200 shrink-0"
                  onClick={() =>
                    showAddressModal({
                      title: '',
                      contentFn: (onClose) => (
                        <AddressModal
                          room={eventClubData?.Room__c}
                          venueName={eventClubData?.venue?.name}
                          address={eventClubData?.venue?.address}
                          onClose={onClose}
                        />
                      ),
                      size: 'md',
                      rounded: '2xl',
                      padding: 0,
                      showCloseButton: false,
                    })
                  }
                  title="Show full address"
                >
                  <EyeIcon />
                </button>
              </div>
              <div className="bg-color-grey-10 rounded-2xl p-5 space-y-3 w-full">
                <h2 className="text-sm text-color-grey-12">Online</h2>
                <h3 className="text-sm font-bold break-words">
                  {eventClubData?.Zoom_Room__c ?? 'N/A'}
                </h3>
              </div>
            </div>
          ) : (
            <div className="bg-color-grey-10 rounded-2xl p-5 space-y-3 flex flex-row md:items-center justify-between gap-3">
              <div>
                <h2 className="text-sm text-color-grey-12">
                  {eventClubData?.Type__c === 'Online' ? 'Online' : 'In Person'}
                </h2>
                <h3 className="text-sm font-bold break-words">
                  {eventClubData?.Type__c === 'Online' ? (
                    (eventClubData?.Zoom_Room__c ?? 'N/A')
                  ) : (
                    <>
                      {eventClubData?.Room__c && (
                        <span>{eventClubData.Room__c}</span>
                      )}
                      {eventClubData?.venue?.name && (
                        <span>
                          {eventClubData?.Room__c ? ', ' : ''}
                          {eventClubData.venue.name}
                        </span>
                      )}
                    </>
                  )}
                </h3>
              </div>
              {eventClubData?.Type__c !== 'Online' && (
                <button
                  className="ml-0 md:ml-2 p-2 rounded hover:bg-gray-200 w-max self-start md:self-auto"
                  onClick={() =>
                    showAddressModal({
                      title: '',
                      contentFn: (onClose) => (
                        <AddressModal
                          room={eventClubData?.Room__c}
                          venueName={eventClubData?.venue?.name}
                          address={eventClubData?.venue?.address}
                          onClose={onClose}
                        />
                      ),
                      size: 'md',
                      rounded: '2xl',
                      padding: 0,
                      showCloseButton: false,
                    })
                  }
                  title="Show full address"
                >
                  <EyeIcon />
                </button>
              )}
            </div>
          )}
          {addressModal}

          <div className="flex flex-col md:flex-row mt-4 text-sm items-start md:items-center justify-start gap-3 md:gap-10">
            <div className="flex items-center gap-1">
              <Person />
              {eventClubData?.Registration_Required__c ? (
                <span className="w-max">
                  {eventClubData?.Total_Seats__c -
                    eventClubData?.Booked_Seats__c}{' '}
                  out of {eventClubData?.Total_Seats__c ?? ''} seats
                </span>
              ) : (
                <span className="w-max">
                  {eventClubData?.Booked_Seats__c ?? ''} Going
                </span>
              )}
            </div>
            {eventClubData?.Registration_Required__c && <div></div>}
            <div className="flex items-center gap-1">
              <CalendarIcon />
              <span className="w-max">
                {formatDateToAmerican(eventClubData?.Start_Date__c)} -{' '}
                {formatDateToAmerican(eventClubData?.End_Date__c)}
              </span>
            </div>
          </div>

          <ClassTabs
            classDetails={eventClubData}
            tabs={classTabs}
            viewType="student"
          />
        </>
      )}
    </div>
  )
}

export default EventClubDetail
