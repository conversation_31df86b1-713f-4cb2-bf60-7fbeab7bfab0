import Image from 'next/image'
import React from 'react'
import Class from '../Icons/Class'

const TeacherCard = ({ data }: { data: any }) => {
  return (
    <div className="w-full h-36 mb-2 p-7 rounded-2xl bg-white shadow-sm">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Image
            width={45}
            height={45}
            src={
              data?.Photo__c
                ? (data?.Photo__c as string).startsWith('http')
                  ? data?.Photo__c
                  : `/teacher-dummy.png`
                : '/teacher-dummy.png'
            }
            alt="User avatar"
            className="rounded-full w-12 h-12 object-cover"
          />
          <div className="">
            <h1 className="font-bold text-sm">{data?.Name ?? 'Katy Perry'}</h1>
            <p className="text-left text-color-grey-1 text-xs">
              {data?.Genre_Teacher__c ?? 'Fiction'}
            </p>
          </div>
        </div>
      </div>
      <div className="mt-4 flex items-center justify-start gap-3">
        <Class />
        <p>{(data?.classes_count ?? 10) + ' Classes'} </p>
      </div>
    </div>
  )
}

export default TeacherCard
