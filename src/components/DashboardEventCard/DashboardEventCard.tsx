import Image from 'next/image'
import React from 'react'

const DashboardEventCard = () => {
  return (
    <div className="p-4 rounded-2xl bg-white w-full">
      <Image
        width={130}
        height={120}
        src={'/event.jpg'}
        alt="Event"
        className="rounded-xl w-full h-36 object-cover"
      />
      <h1
        className="text-sm font-semibold mt-3 flex flex-wrap"
        title="Sound Bath Meditation: Enhance Your Creativity"
      >
        Sound Bath Meditation: Enhance Your Creativity
      </h1>
      <h2 className="font-semibold text-xs text-gray-600 mt-2">
        In Person at The Muse Writers Centre
      </h2>
      <div className="text-gray-600 mt-2">
        <h1 className="text-xs items-center font-medium flex">
          Mon <span className="h-1 w-1 rounded-full mx-1 bg-black"></span>
          November 11
        </h1>
        <h1 className="text-xs font-medium">1:00 pm EST - 3:00 pm EST</h1>
      </div>
      <h1 className="text-xs text-blue-500 font-semibold mt-2 cursor-pointer">
        View Event Details
      </h1>
    </div>
  )
}

export default DashboardEventCard
