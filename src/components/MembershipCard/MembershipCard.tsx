import React, { useEffect, useMemo } from 'react'
import { useApi } from '@/hooks/useApi'
import { purchaseMembership, youthRequestMembership } from '@/lib/actions/membership.actions'
import CustomButton from '../CustomComponents/CustomButton/CustomButton'
import CustomTooltip from '../CustomComponents/CustomTooltip/CustomTooltip'
import { capitalizeFirstLetter } from '@/utils/utils'
import { useDispatch } from 'react-redux'
import { setPaymentToken } from '@/lib/redux'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import { ROLES } from '@/types/enum'
import { toast } from 'react-toastify'

const MembershipCard = ({
  plan,
  handleModal,
  billing,
  isMembershipRequestedResponse=[],
  anyRequestMade,
  setAnyRequestMade,
  requestedPlanId,
  setRequestedPlanId,
}: {
  plan: any
  handleModal: () => void
  billing: any
  isMembershipRequestedResponse?: any
  anyRequestMade?: boolean
  setAnyRequestMade?: (value: boolean) => void
  requestedPlanId?: string | null
  setRequestedPlanId?: (value: string | null) => void
}) => {
  const [purchaseResponse, chooseMembership] = useApi(
    (access: string, productId: string, type: 'Monthly' | 'Annual') =>
      purchaseMembership(access, productId, type),
  )

  const [requestMembershipResponse, requestMembership] = useApi(
    (access: string, productId: string, type: 'Monthly' | 'Annual') =>
      youthRequestMembership(access, productId, type),
  )

  const { user } = useAuth()
  const isCurrent = plan.current
  const isnext = plan?.isNext
  const dispatch = useDispatch()
  const router = useRouter()

  useEffect(() => {
    if (purchaseResponse.isSuccess) {
      dispatch(setPaymentToken(purchaseResponse.data?.data?.token))
      router.push(`/checkout?token=${purchaseResponse.data?.data?.token}`)
    }
  }, [purchaseResponse])

  useEffect(() => {
    if (requestMembershipResponse.isSuccess) {
      setRequestedPlanId?.(plan.id)
      setAnyRequestMade?.(true) 
      toast.success('Membership request sent successfully! Parent approval required.')
    }
  }, [requestMembershipResponse])

  const handleChoosePlan = () => {
    if (user?.role === ROLES.Youth_Student) {
      requestMembership(plan.id, period)
    } else {
      chooseMembership(plan.id, period)
    }
  }

  const period = useMemo(() => {
    return capitalizeFirstLetter(billing)
  }, [billing])
  return (
    <div
      className={`rounded-2xl p-8 ${isnext ? 'bg-black text-white shadow-lg' : 'bg-white border'} ${isnext ? '' : 'shadow-sm'} ${isCurrent ? 'border-2 border-gray-200' : ''}`}
      style={{ marginBottom: '0.5rem' }}
    >
      <div className="grid grid-cols-1 md:grid-cols-12 items-center gap-4">
        {/* Info */}
        <div className="mb-4 md:mb-0 md:col-span-7">
          <div
            className={`font-bold text-2xl mb-1 ${isnext ? 'text-white' : 'text-black'}`}
          >
            {plan.name}
          </div>
          <div
            className={`text-base mb-2 whitespace-pre-line ${isnext ? 'text-white/80' : 'text-gray-500'}`}
          >
            {plan.description}
          </div>
          <a
            href="#"
            className={`text-xs underline ${isnext ? 'text-white/80' : 'text-gray-500'}`}
          >
            View All Benefits
          </a>
        </div>
        {/* Price */}
        <div className="flex justify-end items-center mb-4 md:mb-0 md:col-span-2">
          <div
            className={`font-bold text-2xl ${isnext ? 'text-white' : 'text-black'}`}
          >
            ${plan.price[billing]}/{billing === 'monthly' ? 'mo' : 'yr'}
          </div>
        </div>
        {/* Button */}

        {
          user?.role === ROLES.Youth_Student &&
          <div className="flex flex-col items-center justify-center gap-2 md:col-span-3">
            {isCurrent ? (
              <div className="bg-gray-100 text-gray-400 font-semibold px-6 py-3 rounded-lg cursor-pointer w-40">
                Current Plan
              </div>
              ) : (
                (requestedPlanId === plan.id || (isMembershipRequestedResponse?.length > 0 && isMembershipRequestedResponse[0]?.Product2Id === plan.id)) ?
                <CustomTooltip
                  text="Your membership request is pending parent approval."
                  isVisible={true}
                >
                  <div className="bg-color-yellow text-black font-semibold px-6 py-3 rounded-lg w-40 text-center border border-yellow-300">
                    Pending
                  </div>
                </CustomTooltip>
                :
                <CustomTooltip
                  text={anyRequestMade || (isMembershipRequestedResponse?.length > 0) ? "You can only request one membership at a time." : "Request membership approval from your parent."}
                  isVisible={true}
                >
                  <CustomButton
                    title="Request"
                    onClick={handleChoosePlan}
                    isLoading={requestMembershipResponse.isFetching}
                    height={12}
                    width="w-40"
                    isDisabled={anyRequestMade || (isMembershipRequestedResponse?.length > 0)}
                  />
                </CustomTooltip>
              )}
            {/* {plan.current && (
              <button
                className="text-xs underline text-gray-400 hover:text-black transition"
                onClick={handleModal}
              >
                Manage Plan
              </button>
            )} */}
          </div>
        }
        {
          user?.role !== ROLES.Youth_Student && <div className="flex flex-col items-center justify-center gap-2 md:col-span-3">
            {isCurrent ? (
              <div className="bg-gray-100 text-gray-400 font-semibold px-6 py-3 rounded-lg cursor-pointer w-40">
                Current Plan
              </div>
            ) : (
              <CustomButton
                title="Choose Plan"
                onClick={() => chooseMembership(plan.id, period)}
                isLoading={purchaseResponse.isFetching}
                height={12}
                width="w-40"
              />
            )}
            {plan.current && (
              <button
                className="text-xs underline text-gray-400 hover:text-black transition"
                onClick={handleModal}
              >
                Manage Plan
              </button>
            )}
          </div>
        }
      </div>
    </div>
  )
}

export default MembershipCard
