'use client'
import React, { useState, useEffect } from 'react'
import { setMonth, setYear } from 'date-fns'
import UpArrow from '../Icons/UpArrow'
import LeftArrow from '../Icons/LeftArrow'
import RightArrow from '../Icons/RightArrow'

const MonthSelector = ({
  currentDate,
  setCurrentDate,
}: {
  currentDate: Date
  setCurrentDate: (date: Date) => void
}) => {
  const [isOpen, setIsOpen] = useState(false)
  const [selectedYear, setSelectedYear] = useState(currentDate.getFullYear())
  const [selectedMonth, setSelectedMonth] = useState(
    currentDate.toLocaleString('default', { month: 'short' }),
  )
  const [timer, setTimer] = useState<NodeJS.Timeout>()

  useEffect(() => {
    setSelectedYear(currentDate.getFullYear())
    setSelectedMonth(currentDate.toLocaleString('default', { month: 'short' }))
  }, [currentDate])

  const months = [
    ['Jan', 'Feb', 'Mar', 'Apr'],
    ['May', 'Jun', 'Jul', 'Aug'],
    ['Sep', 'Oct', 'Nov', 'Dec'],
  ]

  const handleMonthSelect = (month: string) => {
    const monthIndex = new Date(`${month} 1, ${selectedYear}`).getMonth()
    const newDate = setMonth(currentDate, monthIndex)
    setCurrentDate(newDate)
    setIsOpen(false)
  }

  const handleYearChange = (increment: number) => {
    const newYear = selectedYear + increment
    const newDate = setYear(currentDate, newYear)
    setSelectedYear(newYear)
    setCurrentDate(newDate)
  }

  useEffect(() => {
    if (isOpen) {
      hideUserBar()
    }
  }, [isOpen])

  const hideUserBar = (delay: number = 1500) => {
    clearTimeout(timer)
    const id = setTimeout(() => {
      setIsOpen(false)
    }, delay)
    setTimer(id)
  }

  return (
    <div className="relative z-30">
      {/* Selector Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="border border-gray-200 px-4 py-2 rounded-lg flex items-center gap-3 hover:bg-gray-50"
      >
        <span className="text-sm font-semibold">{selectedMonth}</span>
        <UpArrow showDownArrow={!isOpen} />
      </button>

      {/* Dropdown Calender */}
      {isOpen && (
        <div
          className="absolute top-full mt-2 right-0 bg-white rounded-lg shadow-lg border border-gray-200 p-4 w-64"
          onPointerEnter={() => {
            if (timer) clearInterval(timer)
          }}
          onPointerLeave={() => hideUserBar()}
        >
          {/* Year Navigation */}
          <div className="flex items-center justify-between mb-6">
            <button
              onClick={(e) => {
                e.preventDefault()
                handleYearChange(-1)
              }}
              className="p-1 hover:bg-gray-100 rounded-full"
            >
              <LeftArrow />
            </button>
            <span className="text-lg font-semibold">{selectedYear}</span>
            <button
              onClick={() => handleYearChange(1)}
              className="p-1 hover:bg-gray-100 rounded-full"
            >
              <RightArrow />
            </button>
          </div>

          {/* Month Grid */}
          <div className="grid gap-4">
            {months.map((row, i) => (
              <div key={i} className="grid grid-cols-4 gap-2">
                {row.map((month) => (
                  <button
                    key={month}
                    onClick={(e) => {
                      e.preventDefault()
                      handleMonthSelect(month)
                    }}
                    className={`p-2 rounded-lg text-sm font-medium
                      ${month === selectedMonth ? 'bg-orange-200 text-black' : 'hover:bg-gray-100'}`}
                  >
                    {month}
                  </button>
                ))}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

export default MonthSelector
