'use client'
import Image from 'next/image'
import React, { useState, useEffect, useRef } from 'react'

const PlayHomeVideo = () => {
  const [showModal, setShowModal] = useState(false)
  const modalRef = useRef(null)

  const openModal = () => {
    setShowModal(true)
    // Add overflow hidden to body when modal opens
    if (typeof document !== 'undefined') {
      document.body.style.overflow = 'hidden'
    }
  }

  const closeModal = () => {
    setShowModal(false)
    // Restore body overflow when modal closes
    if (typeof document !== 'undefined') {
      document.body.style.overflow = ''
    }
  }

  // Close modal when clicking outside the video
  const handleOutsideClick = (e: any) => {
    if (modalRef.current && !(modalRef.current as any).contains(e.target)) {
      closeModal()
    }
  }

  // Add escape key listener to close modal
  useEffect(() => {
    const handleEsc = (e: any) => {
      if (e.key === 'Escape') closeModal()
    }

    if (showModal) {
      window.addEventListener('keydown', handleEsc)
    }

    return () => {
      window.removeEventListener('keydown', handleEsc)
    }
  }, [showModal])

  return (
    <div className="relative">
      <Image
        src={'/v2.png'}
        alt="Muse Center 1"
        width={400}
        height={400}
        className="rounded-3xl bg-color-yellow-1 hover:scale-105 transition duration-300"
      />
      <div
        className="flex items-center justify-center cursor-pointer gap-2 absolute left-4 bottom-14 text-base font-semibold px-6 py-2 bg-black border-4 border-white text-white rounded-3xl w-max"
        onClick={openModal}
      >
        <svg
          width="19"
          height="19"
          viewBox="0 0 19 19"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <g clipPath="url(#clip0_3542_38824)">
            <path
              d="M16.8258 7.43401C17.2008 7.63344 17.5145 7.93115 17.7332 8.29524C17.952 8.65933 18.0676 9.07609 18.0676 9.50084C18.0676 9.9256 17.952 10.3423 17.7332 10.7064C17.5145 11.0705 17.2008 11.3682 16.8258 11.5677L6.82186 17.0077C5.21102 17.8845 3.23242 16.7445 3.23242 14.9416V4.06086C3.23242 2.25716 5.21102 1.11794 6.82186 1.99324L16.8258 7.43401Z"
              fill="white"
            />
          </g>
          <defs>
            <clipPath id="clip0_3542_38824">
              <rect
                width="18.7397"
                height="18.7397"
                fill="white"
                transform="translate(0.109375 0.130371)"
              />
            </clipPath>
          </defs>
        </svg>
        Play Video
      </div>

      {showModal && (
        <div
          className="fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50"
          onClick={handleOutsideClick}
        >
          <div
            ref={modalRef}
            className="relative w-full h-full md:w-4/5 md:h-auto max-w-5xl"
          >
            <button
              onClick={closeModal}
              className="absolute top-4 right-4 md:-top-12 md:-right-4 text-white rounded-full w-12 h-12 flex items-center justify-center text-xl font-bold z-10 shadow-lg"
            >
              ✕
            </button>

            <div className="w-full h-full flex items-center justify-center">
              <iframe
                width="100%"
                height="100%"
                className="aspect-video w-full h-auto"
                src="https://www.youtube.com/embed/9oOnQvJFXH8?autoplay=1&rel=0"
                title="The Muse Writers Center"
                frameBorder="0"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                allowFullScreen
              ></iframe>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default PlayHomeVideo
