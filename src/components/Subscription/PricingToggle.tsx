'use client'
import React, { useState } from 'react'

interface PricingToggleProps {
  size?: 'sm' | 'lg'
  billing?: 'monthly' | 'annual'
  onChangeBilling?: (billing: 'monthly' | 'annual') => void
  showAnnual: {
    percentage: number
    show: boolean
  }
}

const PricingToggle = ({
  size,
  billing,
  onChangeBilling,
  showAnnual,
}: PricingToggleProps) => {
  const [internalBilling, setInternalBilling] = useState<'monthly' | 'annual'>(
    'monthly',
  )
  const isControlled = typeof billing !== 'undefined'
  const selectedBilling = isControlled ? billing : internalBilling

  // Set px and py based on size
  const px = size === 'sm' ? 'px-3' : 'px-6'
  const py = size === 'sm' ? 'py-2' : 'py-2'
  const textSize = size === 'sm' ? 'text-xs' : 'text-lg'
  const textColor = size === 'sm' ? 'text-color-grey-16' : 'text-white'
  const spanTextSize = size === 'sm' ? 'text-xs pr-3' : 'text-base pr-6'
  const bgColor = size === 'sm' ? 'bg-white' : 'bg-color-black'

  const handleClick = (type: 'monthly' | 'annual') => {
    if (!isControlled) {
      setInternalBilling(type)
    }
    if (onChangeBilling) {
      onChangeBilling(type)
    }
  }

  return (
    <div
      className={`flex items-center gap-2 rounded-full p-1 border shadow-inner w-fit ${bgColor}`}
    >
      <button
        className={`${px} ${py} rounded-full font-semibold transition-all duration-200 ${textSize} focus:outline-none ${selectedBilling === 'monthly' ? 'bg-color-yellow text-color-black shadow' : textColor}`}
        onClick={() => handleClick('monthly')}
      >
        Monthly
      </button>
      <button
        className={`${px} ${py} rounded-full font-semibold transition-all duration-200 ${textSize} focus:outline-none ${selectedBilling === 'annual' ? 'bg-color-yellow text-color-black shadow' : textColor}`}
        onClick={() => handleClick('annual')}
      >
        Annual
      </button>
      {showAnnual.show && (
        <span className={`text-color-yellow font-semibold ${spanTextSize}`}>
          Save {showAnnual.percentage}%
        </span>
      )}
    </div>
  )
}
export default PricingToggle
