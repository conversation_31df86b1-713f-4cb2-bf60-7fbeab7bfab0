import React from 'react'
import FeatureItem from './FeatureItem'

interface Feature {
  label: string
  included: boolean
}

const FeatureList: React.FC<{ features: Feature[]; dark?: boolean }> = ({
  features,
  dark,
}) => (
  <ul className="space-y-2">
    {features.map((feature, idx) => (
      <FeatureItem
        key={idx}
        label={feature.label}
        included={feature.included}
        dark={dark}
      />
    ))}
  </ul>
)

export default FeatureList
