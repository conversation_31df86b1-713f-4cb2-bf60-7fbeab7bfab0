import React from 'react'
import FeatureList from './FeatureList'
import SubscribeButton from './SubscribeButton'
import { toast } from 'react-toastify'
import { useRouter } from 'next/navigation'
import { capitalizeFirstLetter } from '@/utils/utils'

interface Feature {
  label: string
  included: boolean
}

interface SubscriptionCardProps {
  Id: string
  name: string
  price: number
  period: string
  annualPrice: number
  annualSavings: string
  cta: string
  features: Feature[]
  highlight?: boolean
  dark?: boolean
  billing: string | null
}

const SubscriptionCard: React.FC<SubscriptionCardProps> = ({
  Id,
  name,
  price,
  period,
  annualPrice,
  annualSavings,
  cta,
  features,
  highlight,
  billing,
  dark,
}) => {
  const router = useRouter()
  const handleSubscription = () => {
    toast.error('Please login first to subscribe.')
    const data = {
      Id,
      billing: capitalizeFirstLetter(billing || 'monthly'),
    }
    localStorage.setItem('subscriptionPlan', JSON.stringify(data))
    router.push('/sign-in')
  }
  billing
  return (
    <div
      className={`flex flex-col justify-between h-full w-full rounded-3xl p-8 shadow-md border transition-transform duration-200
        ${dark ? 'bg-black text-white border-black' : 'bg-white text-neutral-900 border-gray-200'}
      `}
    >
      <div>
        <h2
          className={`text-lg font-bold mb-2 ${dark ? 'text-white' : 'text-neutral-900'}`}
        >
          {name}
        </h2>
        <div className="flex items-end mb-1">
          <span
            className={`text-4xl font-extrabold ${dark ? 'text-white' : 'text-neutral-900'}`}
          >
            ${price}
          </span>
          <span
            className={`ml-1 text-base mb-1 ${dark ? 'text-gray-300' : 'text-gray-500'}`}
          >
            /{period}
          </span>
        </div>
        {Number(annualSavings) > 0 && (
          <>
            {period == 'month' ? (
              <div
                className={`text-xs mb-6 ${dark ? 'text-gray-300' : 'text-gray-500'}`}
              >
                or{' '}
                <span className="font-semibold">
                  save {annualSavings + '%'}
                </span>{' '}
                with{' '}
                <span className="underline cursor-pointer">annual plan</span>{' '}
                for <span className="font-semibold">${annualPrice}</span>
              </div>
            ) : (
              <div className="h-4 mb-6"></div>
            )}
          </>
        )}
        <button
          className={`w-full py-3 rounded-full font-semibold text-base transition-colors duration-200 mb-6
            ${
              dark
                ? 'bg-[#FFB43A] text-black hover:bg-[#ffcb7a]'
                : 'border-2 border-[#FFB43A] text-black hover:bg-[#FFF7E6]'
            }
            ${Number(annualSavings) > 0 ? '' : 'mt-3'}
          `}
          style={{
            boxShadow: dark ? '0 4px 16px 0 rgba(255,180,58,0.10)' : undefined,
          }}
          onClick={handleSubscription}
        >
          {cta}
        </button>
        <FeatureList features={features} dark={dark} />
      </div>
      <div className="mt-8 text-center">
        <a
          href="#"
          className={`text-xs underline ${dark ? 'text-gray-300' : 'text-gray-500'}`}
        >
          View all benefits
        </a>
      </div>
    </div>
  )
}

export default SubscriptionCard
