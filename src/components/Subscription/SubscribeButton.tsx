import React from 'react'

const SubscribeButton: React.FC<{ label: string; highlight?: boolean }> = ({
  label,
  highlight,
}) => (
  <button
    className={`w-full py-3 rounded-3xl font-bold transition-colors duration-200 
      ${highlight ? 'bg-gradient-to-r from-color-yellow to-color-yellow-2 text-black shadow-lg' : 'bg-color-grey-2 text-color-black hover:bg-color-grey-5'}`}
  >
    {label}
  </button>
)

export default SubscribeButton
