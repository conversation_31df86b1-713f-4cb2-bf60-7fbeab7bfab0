import React from 'react'

interface FeatureItemProps {
  label: string
  included: boolean
  dark?: boolean
}

const FeatureItem: React.FC<FeatureItemProps> = ({ label, included, dark }) => (
  <li
    className={`grid grid-cols-12 ${dark ? 'text-white' : 'text-neutral-900'}`}
  >
    <div className="col-span-2 w-5 h-5 pt-1">
      {included ? (
        <svg
          className={`${dark ? 'text-white' : 'text-black'}`}
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            d="M5 13l4 4L19 7"
          />
        </svg>
      ) : (
        <svg
          className={`${dark ? 'text-white' : 'text-black'}`}
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            d="M6 18L18 6M6 6l12 12"
          />
        </svg>
      )}
    </div>
    <span
      className={`col-span-10 ${dark ? 'text-white text-base' : 'text-neutral-900'}`}
    >
      {label}
    </span>
  </li>
)

export default FeatureItem
