import React from 'react'

const faqs = [
  {
    question: 'Can I change my plan later?',
    answer:
      'Yes, you can upgrade or downgrade your plan at any time from your account settings.',
  },
  {
    question: 'Is there a free trial?',
    answer: 'Yes, all paid plans come with a 7-day free trial.',
  },
  {
    question: 'What payment methods are accepted?',
    answer: 'We accept all major credit cards and PayPal.',
  },
]

const FAQSection = () => (
  <section>
    <h3 className="text-2xl font-bold mb-6 text-color-blue-3">
      Frequently Asked Questions
    </h3>
    <ul className="space-y-6">
      {faqs.map((faq, idx) => (
        <li key={idx}>
          <div className="font-semibold text-color-black mb-1">
            {faq.question}
          </div>
          <div className="text-color-grey-4">{faq.answer}</div>
        </li>
      ))}
    </ul>
  </section>
)

export default FAQSection
