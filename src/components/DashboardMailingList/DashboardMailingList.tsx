import Link from 'next/link'
import TeacherMailingList from '../TeacherMailingList/TeacherMailingList'
const DashboardMailingList = () => {
  return (
    <div className="w-full">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-medium leading-[1.5] tracking-normal align-middle text-color-black">
          My Mailing Lists
        </h2>
        <Link
          href="/mailing-lists"
          className="text-sm font-normal leading-[2] tracking-normal text-right text-color-grey-1"
        >
          View All
        </Link>
      </div>
      <TeacherMailingList isDashboardList={true} />
    </div>
  )
}

export default DashboardMailingList
