'use client'
import { useRouter } from 'next/navigation'
import React from 'react'

const BlogTags = () => {
  const route = useRouter()
  const handleBlogTagClick = (tag: string) => {
    route.push(`/blogs?tag=${tag}`)
  }
  return (
    <div className="flex gap-2 mt-8 items-center">
      <span className="text-sm font-medium text-color-grey-4">Posted in:</span>
      {['Announcements', 'In the news!', 'Event Feature'].map((tag) => (
        <span
          onClick={() => handleBlogTagClick(tag)}
          key={tag}
          className="px-3 py-1 cursor-pointer bg-color-grey-2 text-color-grey-4 rounded-full text-sm"
        >
          {tag}
        </span>
      ))}
    </div>
  )
}

export default BlogTags
