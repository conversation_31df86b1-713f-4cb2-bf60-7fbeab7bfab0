import OrangeTick from '@/components/Icons/OrangeTick'
import PageHeader from '@/components/PageHeader/PageHeader'
import Image from 'next/image'
import React from 'react'

const Membership = () => {
  const membershipLevels = [
    { name: 'Youth/Teen\n(under 18)', yearly: 24, monthly: 2 },
    { name: 'Member', yearly: 60, monthly: 5 },
    { name: '<PERSON>', yearly: 180, monthly: 15 },
    { name: 'Ambassador', yearly: 300, monthly: 25 },
    { name: 'Angel', yearly: 540, monthly: 45 },
    { name: "Bronze Director's Circle", yearly: 1200, monthly: 100 },
    { name: "Silver Director's Circle", yearly: 2400, monthly: 200 },
    { name: "Gold Director's Circle", yearly: 5000, monthly: 417 },
  ]

  const benefits = [
    {
      name: 'Invitations to Muse Special Events',
      levels: [0, 1, 2, 3, 4, 5, 6, 7],
    },
    { name: 'Quarterly Donor newsletter', levels: [0, 1, 2, 3, 4, 5, 6, 7] },
    {
      name: 'Muse Library Checkout Privileges*',
      levels: [0, 1, 2, 3, 4, 5, 6, 7],
    },
    {
      name: 'Author Page on The Muse Website',
      levels: [0, 1, 2, 3, 4, 5, 6, 7],
    },
    { name: '5% discount on classes at The Muse', levels: [0, 1] },
    { name: '10% discount at Prince Books', levels: [0, 1, 2, 3, 4, 5, 6, 7] },
    {
      name: '10% discount at The Green Onion',
      levels: [0, 1, 2, 3, 4, 5, 6, 7],
    },
    { name: '10% discount at Plaza del Sol', levels: [0, 1, 2, 3, 4, 5, 6, 7] },
    { name: '10% discount on classes at The Muse', levels: [2, 3, 4, 5] },
    {
      name: '1 free Muse seminar each year for you or a friend',
      levels: [3, 4, 5, 6, 7],
    },
    { name: 'Donor Happy Hour', levels: [4, 5, 6, 7] },
    { name: 'Signed book from a Muse author', levels: [4, 5, 6, 7] },
    { name: 'Recognition on Muse Website', levels: [4, 5, 6, 7] },
    {
      name: '1 free 6-wk. class at The Muse each year OR Adult Camp for you or a friend',
      levels: [5, 6, 7],
    },
    { name: 'Executive Director Updates', levels: [5, 6, 7] },
    {
      name: "Invitation to Author's Talks in private homes",
      levels: [5, 6, 7],
    },
    {
      name: '25% off The Muse classes for you and your significant other',
      levels: [6],
    },
    { name: 'VIP Seating at Selected Muse Events', levels: [6, 7] },
    {
      name: 'Free unlimited classes at The Muse for you and your significant other',
      levels: [7],
    },
    { name: 'Recognition on Muse Donor Wall', levels: [7] },
  ]
  return (
    <div className="w-screen md:w-full">
      <h1 className="text-3xl font-semibold px-4 pt-4 block md:hidden">
        Membership
      </h1>
      <div className="w-full flex justify-center">
        <div className="w-11/12 md:w-9/12 mt-5">
          <div className="bg-white w-full px-10 h-max py-8 rounded-2xl flex items-center">
            <div className="border-r-2 border-color-grey-2 pr-5">
              <h1 className="text-color-blue text-4xl font-bold">
                The Writers Membership
              </h1>
              <p className="text-sm">
                Since its start in 2005, The Muse has grown into one of the
                nation’s largest community-based literary centers, serving tens
                of thousands of people in coastal Virginia and beyond, thanks to
                the support of donors like you.
                <br></br>
                <br></br>
                In addition to classes that touch on nearly every genre of
                writing, the Muse’s outreach programs serve young writers,
                military veterans, and elderly populations across the region;
                and now, with expanded virtual programming, across the world.
                <br></br>
                <br></br>
                The Muse is unique among premier writing centers in that nobody
                who wants to experience our lively, diverse menu of offerings is
                turned away, regardless of ability to pay.
                <br></br>
                <br></br>
                While The Muse experience is felt and lived, it is also an
                organization that requires practical and tangible resources to
                enable it.
                <br></br>
                <br></br>
                <strong>
                  Your support will enable The Muse to deepen and widen its
                  impact in coastal Virginia and elsewhere, as well as propel
                  you into a dynamic community of cultural supporters.
                </strong>
                <br></br>
                <br></br>
                Donor Members benefit from a wide variety of benefits, including
                discounts on Muse classes, as well as discounts to local
                businesses. Make a one-time donation or split your donation into
                monthly payments. Even the smallest donations make a big
                difference.
              </p>
            </div>
            <div className="pl-4 h-full">
              <i>
                “The Muse offers so much to so many, without judgment and with
                open hearts and arms. What a gift you have given to this
                community. Thank you for being dedicated in giving such a
                beautiful gift in this, ofttimes weary world.”
              </i>
              <br></br>
              <i>— Jamshid Samareh</i>
            </div>
          </div>
          <div className="my-5 rounded-2xl">
            <table className="w-full border-collapse text-sm">
              <thead>
                <tr>
                  <th className="p-2 border bg-color-blue text-white rounded-tl-base text-left">
                    Benefit
                  </th>
                  {membershipLevels.map((level, index) => (
                    <th
                      key={index}
                      className={`p-2 border bg-color-blue text-white text-center ${index == membershipLevels.length - 1 ? 'rounded-tr-base' : ''}`}
                    >
                      <div className="whitespace-pre-line">{level.name}</div>
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {benefits.map((benefit, index) => (
                  <tr key={index}>
                    <td className="p-2 border">{benefit.name}</td>
                    {membershipLevels.map((_, levelIndex) => (
                      <td key={levelIndex} className={`p-2 bg-white border`}>
                        {benefit.levels.includes(levelIndex) ? (
                          <OrangeTick />
                        ) : (
                          <span className="text-slate-300">-</span>
                        )}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Membership
