import React, { useEffect, useState } from 'react'
import PrevNextButton from '../PrevNextButton/PrevNextButton'
import Image from 'next/image'
import Person from '../Icons/Person'
import Class from '../Icons/Class'
import Clock from '../Icons/Clock'
import { getClassesByDate } from '@/lib/actions/class.actions'
import { formatISO } from 'date-fns'
import WeeklyEventCardSkeleton from '../Skeletons/WeeklyEventCardSkeleton'
import { toast } from 'react-toastify'
import CustomButton from '../CustomComponents/CustomButton/CustomButton'
import { useApi } from '@/hooks/useApi'
import Link from 'next/link'

const WeeklyEventCard = ({ currentWeek }: { currentWeek: any }) => {
  const [events, setEvents] = useState<any>(new Map())
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [selectedIndex, setSelectedIndex] = useState<number>(0)
  const [classResponse, fetchClassData] = useApi(
    (
      accessToken: string,
      startDate: string,
      endDate: string,
      classCategory: 'All' | 'Adult Class' | 'Youth Class',
      type?: string,
      genre?: string[],
      level?: string[],
    ) =>
      getClassesByDate(
        accessToken,
        startDate,
        endDate,
        classCategory,
        type,
        genre,
        level,
      ),
  )

  useEffect(() => {
    if (classResponse.isSuccess) {
      let newEventMap = new Map()
      classResponse?.data?.data?.forEach((event: any) => {
        let getValue = newEventMap.get(event?.startDate) || []
        newEventMap.set(event?.startDate, [...getValue, { ...event }])
      })
      const formattedStartDate = formatISO(currentWeek, {
        representation: 'date',
      })
      setEvents(newEventMap.get(formattedStartDate))
      setSelectedIndex(0)
    }
  }, [classResponse])

  const getClassData = async (start: any, end: any) => {
    setIsLoading(true)
    try {
      await fetchClassData(start, end, 'All')
    } catch (error) {
      toast.error('Something went wrong ss!' + error, { autoClose: 1500 })
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    const formattedStartDate = formatISO(currentWeek, {
      representation: 'date',
    })
    getClassData(formattedStartDate, formattedStartDate)
  }, [currentWeek])

  const handlePrev = () => {
    const totalLength = events.length
    setSelectedIndex((totalLength + selectedIndex - 1) % totalLength)
  }

  const handleNext = () => {
    const totalLength = events.length
    setSelectedIndex((selectedIndex + 1) % totalLength)
  }
  return (
    <>
      {isLoading ? (
        <WeeklyEventCardSkeleton />
      ) : (
        <div className="w-full bg-white rounded-2xl p-3 flex flex-col">
          {events?.length > 0 ? (
            <>
              <div className="flex items-center justify-between">
                <h3 className="font-medium text-sm text-color-grey-1">
                  {"Today's Events"}
                </h3>
                <PrevNextButton
                  onNext={handleNext}
                  onPrev={handlePrev}
                  notDisable={true}
                />
              </div>
              <div className="relative">
                {events[selectedIndex]?.classBannerImage && (
                  <Link href={`/classes/${events[selectedIndex]?.classId}`}>
                    <Image
                      width={100}
                      height={120}
                      src={events[selectedIndex]?.classBannerImage}
                      alt="User avatar"
                      className="rounded-md h-36 w-full mt-2 object-cover"
                    />
                  </Link>
                )}
              </div>
              <h1 className="font-bold text-lg max-w-96 text-color-black mt-2">
                <Link href={`/classes/${events[selectedIndex]?.classId}`}>
                  {events[selectedIndex]?.classTitle}
                </Link>
              </h1>
              {events[selectedIndex]?.teacher_data && (
                <div className="flex items-center justify-between">
                  <span className="text-sm text-color-grey-1">
                    {events[selectedIndex]?.teacher_data?.Name}
                  </span>
                  {events[selectedIndex]?.teacher_data?.Photo__c && (
                    <Image
                      width={50}
                      height={50}
                      src={events[selectedIndex]?.teacher_data?.Photo__c}
                      alt="User avatar"
                      className="rounded-full w-12 h-12 object-cover"
                    />
                  )}
                </div>
              )}
              <div className="flex items-center justify-between mt-2 text-sm">
                <div className="flex items-center gap-3">
                  <Person />
                  <span>{events[selectedIndex]?.bookedSeats} Attendees</span>
                </div>
                <div className="flex items-center gap-3">
                  <Class />
                  <span>{events[selectedIndex]?.totalSeats} Meetings</span>
                </div>
                <div className="flex items-center gap-3">
                  <Clock />
                  <span>{events[selectedIndex]?.meetingDuration} h</span>
                </div>
              </div>
              <div
                className="text-sm mt-2"
                dangerouslySetInnerHTML={{
                  __html: events[selectedIndex]?.classDescripion,
                }}
              />
              {/* <div className="mt-5">
                <CustomButton
                  title="Register"
                  isLoading={false}
                  onClick={() => {}}
                />
              </div> */}
            </>
          ) : (
            <div className="rounded-2xl flex flex-col w-full items-center justify-center h-96">
              <h2 className="font-medium text-4 text-color-grey-1">No Event</h2>
            </div>
          )}
        </div>
      )}
    </>
  )
}

export default WeeklyEventCard
