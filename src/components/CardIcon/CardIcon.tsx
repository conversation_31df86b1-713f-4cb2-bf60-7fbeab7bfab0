'use client'

import { CARD_TYPES } from '@/types/enum'
import VisaIcon from '../Icons/CardsIcon/VisaIcon'
import MasterCard from '../Icons/CardsIcon/MasterCard'
import AmericanExpress from '../Icons/CardsIcon/AmericanExpress'
import Discovery from '../Icons/CardsIcon/Discovery'
import DinnerClub from '../Icons/CardsIcon/DinnerClub'
import JCB from '../Icons/CardsIcon/JCB'
import UnionPay from '../Icons/CardsIcon/UnionPay'

type CardIconProps = {
  type: string
}

export default function CardIcon({ type }: CardIconProps) {
  switch (type) {
    case CARD_TYPES.VISA:
      return <VisaIcon />
    case CARD_TYPES.MASTERCARD:
      return <MasterCard />
    case CARD_TYPES.AMERICAN_EXPRESS:
      return <AmericanExpress />
    case CARD_TYPES.DISCOVER:
      return <Discovery />
    case CARD_TYPES.DINERS_CLUB:
      return <DinnerClub />
    case CARD_TYPES.JCB:
      return <JCB />
    case CARD_TYPES.UNION_PAY:
      return <UnionPay />
    default:
      return <></>
  }
}
