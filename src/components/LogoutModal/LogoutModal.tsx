'use client'
import React, { useState } from 'react'
import { useRouter } from 'next/navigation'
import { logout } from '@/lib/actions/login.actions'
import CustomButton from '../CustomComponents/CustomButton/CustomButton'
import { useAuth } from '@/contexts/AuthContext'

const LogoutModal = ({ onClose }: { onClose: any }) => {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const { setUser } = useAuth()
  return (
    <div className="flex flex-col items-center gap-3 w-full">
      <svg
        width="49"
        height="48"
        viewBox="0 0 49 48"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <rect
          x="0.5"
          y="-0.000976562"
          width="48"
          height="48"
          rx="24"
          fill="#DB5962"
        />
        <path
          d="M24.5 13.999C18.99 13.999 14.5 18.489 14.5 23.999C14.5 29.509 18.99 33.999 24.5 33.999C30.01 33.999 34.5 29.509 34.5 23.999C34.5 18.489 30.01 13.999 24.5 13.999ZM23.75 19.999C23.75 19.589 24.09 19.249 24.5 19.249C24.91 19.249 25.25 19.589 25.25 19.999V24.999C25.25 25.409 24.91 25.749 24.5 25.749C24.09 25.749 23.75 25.409 23.75 24.999V19.999ZM25.42 28.379C25.37 28.509 25.3 28.609 25.21 28.709C25.11 28.799 25 28.869 24.88 28.919C24.76 28.969 24.63 28.999 24.5 28.999C24.37 28.999 24.24 28.969 24.12 28.919C24 28.869 23.89 28.799 23.79 28.709C23.7 28.609 23.63 28.509 23.58 28.379C23.53 28.259 23.5 28.129 23.5 27.999C23.5 27.869 23.53 27.739 23.58 27.619C23.63 27.499 23.7 27.389 23.79 27.289C23.89 27.199 24 27.129 24.12 27.079C24.36 26.979 24.64 26.979 24.88 27.079C25 27.129 25.11 27.199 25.21 27.289C25.3 27.389 25.37 27.499 25.42 27.619C25.47 27.739 25.5 27.869 25.5 27.999C25.5 28.129 25.47 28.259 25.42 28.379Z"
          fill="white"
        />
      </svg>
      <h1 className="font-bold text-lg">Logout From Account</h1>
      <h2 className="text-color-grey-1 text-sm mb-3">
        {`Are you sure want logout from your Account?`}
      </h2>
      <div className="flex items-center justify-between gap-3 w-full">
        <button
          className="w-full inline-flex items-center h-12 justify-center whitespace-nowrap disabled:pointer-events-none disabled:opacity-50  rounded-md text-sm focus-visible:outline-none focus-visible:ring-1 text-black focus-visible:ring-ring font-semibold shadow px-4 py-2"
          onClick={() => {
            onClose()
          }}
        >
          Cancel
        </button>
        <CustomButton
          title="Logout"
          isLoading={isLoading}
          onClick={async () => {
            setIsLoading(true)
            await logout()
            setUser(null)
            router.replace('/sign-in')
            setIsLoading(false)
          }}
          height={12}
        />
      </div>
    </div>
  )
}

export default LogoutModal
