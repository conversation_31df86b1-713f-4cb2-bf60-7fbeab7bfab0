'use client'
import React from 'react'
import { Swiper, SwiperSlide } from 'swiper/react'
import { Autoplay, Navigation, Pagination } from 'swiper/modules'
import Link from 'next/link'
import ClassCard from '../ClassCard/ClassCard'

// Import Swiper styles
import 'swiper/css'
import 'swiper/css/navigation'
import 'swiper/css/pagination'

interface ClassListItem {
  Id: string
  Banner_Image__c: string
  Genre__c: string
  Title__c: string
  Duration__c: number
  Total_Meetings_Count__c: number
  Available_Seats__c: number
  Booked_Seats__c: number
  associated_teachers: any[]
}

interface ClassListData {
  Class_List_Name: string
  Classes: ClassListItem[]
}

interface ClassListCarouselProps {
  classListData: ClassListData
  limit?: number
  autoplay?: boolean
  arrows?: boolean
  dots?: boolean
  theme?: 'light' | 'dark'
}

const ClassListCarousel = ({
  classListData,
  limit = 10,
  autoplay = true,
  arrows = true,
  dots = true,
  theme = 'light',
}: ClassListCarouselProps) => {
  if (!classListData?.Classes || classListData.Classes.length === 0) {
    return (
      <div className="w-full p-4 text-center text-gray-500">
        <p>No classes found in this list.</p>
      </div>
    )
  }

  // Limit the number of classes displayed
  const displayClasses = classListData.Classes.slice(0, limit)

  const swiperModules = [
    autoplay && Autoplay,
    arrows && Navigation,
    dots && Pagination,
  ].filter((module): module is any => Boolean(module))

  return (
    <div className={`w-full my-8}`}>
      {/* Header */}
      <div className="mb-6 text-start">
        <h2 className="text-3xl font-bold mb-2">
          {classListData.Class_List_Name}
        </h2>
        <p className="text-gray-600">Discover our featured classes</p>
      </div>

      {/* Carousel */}
      <div className="relative">
        <Swiper
          modules={swiperModules}
          spaceBetween={20}
          slidesPerView={1}
          loop={displayClasses.length > 3}
          autoplay={
            autoplay
              ? {
                  delay: 3000,
                  disableOnInteraction: false,
                  pauseOnMouseEnter: true,
                }
              : false
          }
          navigation={arrows}
          pagination={dots ? { clickable: true } : false}
          breakpoints={{
            640: {
              slidesPerView: 2,
              spaceBetween: 20,
            },
            768: {
              slidesPerView: 3,
              spaceBetween: 24,
            },
            1024: {
              slidesPerView: 4,
              spaceBetween: 30,
            },
          }}
          className="pb-12"
        >
          {displayClasses.map((classItem, index) => (
            <SwiperSlide key={`${classItem.Id}-${index}`}>
              <Link href={`/classes/${classItem.Id}`} className="block h-full">
                <ClassCard
                  data={{
                    ...classItem,
                    teacher_data: classItem.associated_teachers?.[0] || null,
                    Price__c: 0, // Price will be fetched separately if needed
                  }}
                  showRating={false}
                  showDetails={true}
                  shadow="md"
                  theme={theme}
                />
              </Link>
            </SwiperSlide>
          ))}
        </Swiper>
      </div>

      {/* Custom navigation arrows for better styling */}
      {arrows && (
        <>
          <style jsx global>{`
            .swiper-button-next,
            .swiper-button-prev {
              color: ${theme === 'dark' ? '#fff' : '#333'};
              background: ${theme === 'dark'
                ? 'rgba(255,255,255,0.1)'
                : 'rgba(255,255,255,0.8)'};
              border-radius: 50%;
              width: 40px;
              height: 40px;
              margin-top: -20px;
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            }
            .swiper-button-next:after,
            .swiper-button-prev:after {
              font-size: 16px;
              font-weight: bold;
            }
            .swiper-button-disabled {
              opacity: 0.3;
            }
          `}</style>
        </>
      )}

      {/* Custom pagination styling */}
      {dots && (
        <style jsx global>{`
          .swiper-pagination-bullet {
            background: ${theme === 'dark' ? '#fff' : '#333'};
            opacity: 0.3;
          }
          .swiper-pagination-bullet-active {
            opacity: 1;
            background: ${theme === 'dark' ? '#fff' : '#333'};
          }
        `}</style>
      )}
    </div>
  )
}

export default ClassListCarousel
