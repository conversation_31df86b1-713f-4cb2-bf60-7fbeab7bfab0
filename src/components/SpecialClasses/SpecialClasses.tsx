import Image from 'next/image'
import React from 'react'
import ClassCard from '../ClassCard/ClassCard'
import Link from 'next/link'

const SpecialClasses = ({
  specialClasses,
  specialPoetryClasses,
}: {
  specialClasses: any
  specialPoetryClasses: any
}) => {
  return (
    <div className="flex flex-col items-center justify-center py-8">
      <section className="grid max-w-4xl mx-auto px-4 py-8 grid-cols-10 gap-10">
        <div className="col-span-7 flex flex-col justify-center">
          <div className="">
            <h2 className="text-brown-800 uppercase font-bold mb-2 tracking-[9px] reverse-string-1">
              THE MUSE WRITERS CENTER
            </h2>
            <h2 className="text-5xl font-bold mb-4">Who We Are</h2>
            <p className="text-color-grey-7 mb-4 text-xl">
              {`From poetry to fiction, memoir to songwriting, our diverse offerings will help you cultivate your craft and flourish as a writer. At The Muse, we believe that creativity thrives when given the right conditions to bloom.`}
            </p>
            <p className="text-color-grey-7 mb-4 text-xl">
              {`This spring, immerse yourself in a season of storytelling, self-discovery, and artistic exploration.`}
            </p>
          </div>
        </div>
        <div className="relative col-span-3 flex justify-end items-end">
          <div className="h-40 w-40 bg-color-yellow-1 rounded-full"></div>
          <Image
            src={'/flower.png'}
            alt="Muse Center 1"
            width={5000}
            height={400}
            className="absolute w-52 h-60 -bottom-[12%] -right-[12%]"
          />
        </div>
      </section>
      <div className="bg-color-cream-2 h-28 w-full flex items-center justify-around overflow-hidden mt-8">
        <div
          className="w-full h-96 bg-repeat bg-contain"
          style={{ backgroundImage: 'url(/flowers-bg.png)' }}
        ></div>

        <h1 className="text-2xl mx-10 font-medium text-center whitespace-nowrap max-w-max">
          Save 35% with code <span className="font-extrabold">NURTURE2025</span>{' '}
          at checkout!
        </h1>

        <div
          className="w-full h-96 bg-repeat bg-contain"
          style={{ backgroundImage: 'url(/flowers-bg.png)' }}
        ></div>
      </div>
      <div className="mx-auto px-4 py-8 mt-8 grid grid-cols-3 gap-5">
        {specialClasses.map((data: any, index: number) => {
          return (
            <Link
              href={`/classes/${data?.Id}`}
              key={data?.Id + 'special' + index}
            >
              <ClassCard
                data={data}
                showRating={false}
                showDetails={true}
                shadow={'sm'}
              />
            </Link>
          )
        })}
      </div>

      <div className="bg-color-cream-2 mt-5 h-28 w-full flex items-center justify-around overflow-hidden">
        <div
          className="w-full h-96 bg-repeat bg-contain"
          style={{ backgroundImage: 'url(/flowers-bg.png)' }}
        ></div>

        <h1 className="text-2xl mx-10 font-medium text-center whitespace-nowrap max-w-max">
          Save 35% with code <span className="font-extrabold">NURTURE2025</span>{' '}
          at checkout!
        </h1>

        <div
          className="w-full h-96 bg-repeat bg-contain"
          style={{ backgroundImage: 'url(/flowers-bg.png)' }}
        ></div>
      </div>
      <section className="grid mt-5 max-w-4xl mx-auto px-4 py-8 grid-cols-10 gap-10">
        <div className="col-span-7 flex flex-col justify-center">
          <div className="">
            <h2 className="text-brown-800 uppercase font-bold mb-2 tracking-[9px] reverse-string-1">
              THE MUSE WRITERS CENTER...
            </h2>
            <h2 className="text-5xl font-bold mb-4">
              April is Poetry Month—Celebrate with the Muse!
            </h2>
            <p className="text-color-grey-7 mb-4 text-xl">
              Save 50%! Use code{' '}
              <span className="font-bold text-black">APRIL2025</span> at check
              out!
            </p>
          </div>
        </div>
        <div className="relative col-span-3 flex justify-end items-end">
          <div className="h-40 w-40 bg-color-yellow-1 rounded-full"></div>
          <Image
            src={'/black-pen.png'}
            alt="Muse Center 1"
            width={5000}
            height={400}
            className="absolute -bottom-[20%] -right-[15%]"
          />
        </div>
      </section>
      <div className="mx-auto px-4 py-8 grid grid-cols-3 gap-5">
        {specialPoetryClasses.map((data: any, index: number) => {
          return (
            <Link href={`/classes/${data?.Id}`} key={data?.Id + 'poetry'}>
              <ClassCard
                key={data?.Id + 'poetry' + index}
                data={data}
                showRating={false}
                showDetails={true}
                shadow={'sm'}
              />
            </Link>
          )
        })}
      </div>
    </div>
  )
}

export default SpecialClasses
