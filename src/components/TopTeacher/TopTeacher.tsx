'use client'
import React, { useEffect } from 'react'
import PrevNextButton from '../PrevNextButton/PrevNextButton'
import TeacherCard from '../TeacherCard/TeacherCard'

const TopTeacher = () => {
  return (
    <div>
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Top Teachers</h1>
        <PrevNextButton notDisable={true} />
      </div>
      <div className="flex-row md:flex items-center justify-between gap-5 mt-4">
        <TeacherCard data={''} />
        <TeacherCard data={''} />
      </div>
    </div>
  )
}

export default TopTeacher
