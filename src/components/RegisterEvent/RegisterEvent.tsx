'use client'
import { useApi } from '@/hooks/useApi'
import { registerForClub } from '@/lib/actions/class.actions'
import React, { useEffect } from 'react'
import CustomButton from '../CustomComponents/CustomButton/CustomButton'
import { toast } from 'react-toastify'

interface RegisterEventProps {
  eventId: string
  isRegistrationRequired: boolean
  link: string
  isDisable: boolean
}

const RegisterEvent = ({
  eventId,
  isRegistrationRequired,
  link,
  isDisable,
}: RegisterEventProps) => {
  const [response, registerEvent] = useApi((access: string, eventId: string) =>
    registerForClub(access, eventId),
  )

  useEffect(() => {
    if (response.isSuccess) {
      toast.success('Successfully registered for the event', {
        autoClose: 3000,
      })
    }
  }, [response])

  const handleRegister = () => {
    if (isRegistrationRequired) {
      registerEvent(eventId)
    } else {
      link && window.open(link, '_blank', 'noopener,noreferrer')
    }
  }

  return (
    <div className="mt-5">
      <CustomButton
        onClick={handleRegister}
        title="Register Now"
        isLoading={response.isFetching}
        isDisabled={!isRegistrationRequired || isDisable}
      />
    </div>
  )
}

export default RegisterEvent
