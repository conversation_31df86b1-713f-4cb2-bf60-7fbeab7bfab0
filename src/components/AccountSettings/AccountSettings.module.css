/* Checkbox styles */
.checkbox__label {
    margin: 0 5px;
    cursor: pointer;
}

.checkbox__group {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 20px;
    height: 20px;
    background: transparent;
    border: 1px solid;
    border-radius: 5px;
}

.checkbox__input:checked {
    position: absolute;
    background-color: red;
}

.checkbox__input:checked+.checkbox__checked {
    opacity: 1;
}

.checkbox__checked {
    content: "";
    width: 4px;
    height: 10px;
    margin-top: -2px;
    border-right: 2px solid #FFBB54;
    border-bottom: 2px solid #FFBB54;
    opacity: 0;
    transform: rotate(30deg);
    transition: 0.2s;
}

/* Radio styles */
.radio {
    margin: 20px 0;
}

.radio__label {
    margin: 0 5px;
    cursor: pointer;
}

.radio__group {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 20px;
    height: 20px;
    background: transparent;
    border: 2px solid #28c4ae;
    border-radius: 50px;
}

.radio__input:checked+.radio__checked {
    opacity: 1;
}

.radio__checked {
    content: "";
    width: 10px;
    height: 10px;
    background-color: #28c4ae;
    border-radius: 50px;
    opacity: 0;
    transition: 0.2s;
}

/* Toggle styles */
.toggle__label {
    margin: 0 5px;
    cursor: pointer;
}

.toggle__group {
    position: relative;
    width: 50px;
    height: 25px;
    background: #ccc;
    border-radius: 50px;
    transition: 0.2s;
}

.toggle__input:checked~.toggle__group {
    background-color: #28c4ae;
}

.toggle__input:checked~.toggle__group .toggle__checked {
    right: 5px;
}

.toggle__checked {
    position: absolute;
    top: 50%;
    right: calc(100% - 20px);
    content: "";
    width: 15px;
    height: 15px;
    background-color: white;
    border-radius: 50px;
    transform: translateY(-50%);
    transition: 0.2s;
}