// app/account/page.tsx
'use client'
import { useState, useEffect } from 'react'
import styles from './AccountSettings.module.css'
import { useApi } from '@/hooks/useApi'
import { updateAccountById } from '@/lib/actions/account.actions'
import { toast } from 'react-toastify'
import { useAuth } from '@/contexts/AuthContext'
import { logout } from '@/lib/actions/login.actions'
import { useRouter } from 'next/navigation'

interface AccountSettings {
  currentPassword: string
  newPassword: string
  username: string
  email: string
  language: string
}

export default function AccountSettings() {
  const { user } = useAuth()
  
  const [formData, setFormData] = useState<{
    confirmDelete: boolean
  }>({
    confirmDelete: false,
  })

  const router = useRouter()

  const [response, updateDetails] = useApi(
    (access: string, data: any) => updateAccountById(access, data),
    true,
  )

  const handleDeletedAccount = async () => {
    await logout()
    router.replace('/sign-in')
  }

  useEffect(() => {
    if (response.isSuccess) {
      toast.success(
        'Account deletion request submitted. You have 14 days to cancel if you change your mind.',
      )
      // Reset form after successful submission
      setFormData({
        confirmDelete: false,
      })
      handleDeletedAccount()
    } else if (response.error) {
      toast.error('Error submitting deletion request. Please try again.')
    }
  }, [response])

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target
    setFormData((prev) => ({
      ...prev,
      [name]: checked,
    }))
  }

  const handleDeleteAccount = async () => {
    if (!formData.confirmDelete) {
      toast.error('Please confirm that you want to delete your account.')
      return
    }

    if (user && user?.id) {
      const updateData = {
        IsDeleted: true,
      }
      updateDetails(updateData)
    } else {
      toast.error('User not found. Please log in again.')
    }
  }
  return (
    <div className="">
      <h1 className="text-lg font-bold mb-6">Account Settings</h1>
      {/* Delete Account Section */}
      <div className="bg-white rounded-lg mb-6 border-b-1 border-color-grey pb-7">
        <div className="flex justify-between items-center mb-1">
          <h2 className="text-base font-medium">Delete Your Account</h2>
        </div>
        <p className="text-color-grey-1 text-sm mb-4 w-9/12">
          If you choose to delete your account, you will no longer have access
          to The Muse Writers Center’s online services, and all of your personal
          data will be permanently removed from our system. You have a 14-day
          grace period to change your mind and cancel the deletion before it
          becomes final.
        </p>
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <div className={styles.container}>
              <div className={styles.checkbox}>
                <label
                  htmlFor={`customCheckbox1`}
                  className={styles.checkbox__label}
                  key={`checkbox-1`}
                >
                  <div className={styles.checkbox__group}>
                    <input
                      type="checkbox"
                      className={styles.checkbox__input}
                      id={`customCheckbox1`}
                      name="confirmDelete"
                      checked={formData.confirmDelete}
                      onChange={handleCheckboxChange}
                      hidden
                    />
                    <span className={styles.checkbox__checked}></span>
                  </div>
                </label>
              </div>
            </div>
            <label className="text-xs">
              Confirm that I want to delete my account.
            </label>
          </div>

          <div className="flex gap-4">
            <button className="h-12 w-40 rounded-xl border text-sm font-medium border-gray-300">
              Learn More
            </button>
            <button
              onClick={handleDeleteAccount}
              disabled={response.isFetching}
              className={`h-12 w-40 rounded-xl border text-sm font-medium border-color-grey bg-color-yellow ${
                response.isFetching ? 'opacity-50 cursor-not-allowed' : ''
              }`}
            >
              {response.isFetching ? 'Deleting...' : 'Delete Account'}
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
