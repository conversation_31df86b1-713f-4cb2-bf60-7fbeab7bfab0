'use client'
import { useState } from 'react'
import Image from 'next/image'

export default function BannerImage({ bannerImage }: { bannerImage: string }) {
  const [isLoaded, setIsLoaded] = useState(false)

  return (
    <div className="relative w-full h-[200px] xs:h-[250px] sm:h-[350px] lg:h-[659px] max-h-[800px] p-0 m-0 overflow-hidden">
      {/* Shimmer */}
      {!isLoaded && (
        <div className="shimmer absolute inset-0 w-full h-full animate-pulse" />
      )}
      {/* Image */}
      {bannerImage && (
        <Image
          src={bannerImage}
          alt="Banner"
          fill
          priority
          sizes="(max-width: 640px) 100vw,
           (max-width: 1024px) 100vw,
           1800px"
          className={`object-contain md:object-cover transition-opacity duration-300 ${
            isLoaded ? 'opacity-100' : 'opacity-0'
          }`}
          onLoadingComplete={() => setIsLoaded(true)}
        />
      )}
    </div>
  )
}
