'use client'
import React, { useEffect, useState, memo } from 'react'
import SwitchRole from '../Icons/SwitchRole'
import UpArrow from '../Icons/UpArrow'
import EditIcon from '../Icons/EditIcon'
import Logout from '../Icons/Logout'
import useModal from '@/hooks/useModal'
import LogoutModal from '../LogoutModal/LogoutModal'
import { capitalizeFirstLetter } from '@/utils/utils'
import Link from 'next/link'
import RoleCard from '../RoleCard/RoleCard'
import { ROLES } from '@/types/enum'
import { useAsyncRefProp } from '@/hooks/useAsyncRefProp'
import UserProfileImage from '../UserProfileImage/UserProfileImage'

const UserProfile = ({
  userData,
  h = 50,
  w = 50,
}: {
  userData?: any
  h?: number
  w?: number
}) => {
  const [timer, setTimer] = useState<NodeJS.Timeout>()
  const [showUserBar, setShowUserBar] = useState<boolean>(false)
  const [showRole, setShowRole] = useState<boolean>(false)
  const [modal, showModal] = useModal()
  const [switchingRole, setSwitchingRole] = useState<boolean>(false)
  const switchingRoleRef = useAsyncRefProp(switchingRole)
  const handleLogout = () => {
    showModal({
      title: '',
      contentFn: (onClose) => <LogoutModal onClose={onClose} />,
      closeOnClickOutside: true,
      size: 'md',
      showCloseButton: false,
    })
  }

  const handleClickOutside = (event: MouseEvent) => {
    event.stopPropagation()
    if (showUserBar && !switchingRoleRef?.current) {
      const target = event.target as Element
      if (!target?.closest('.user-profile-container')) {
        setShowUserBar(false)
      }
    }
  }

  useEffect(() => {
    if (showUserBar && !switchingRoleRef?.current) {
      hideUserBar(5000)
    }
  }, [showUserBar])

  useEffect(() => {
    if (showUserBar) {
      document.addEventListener('mousedown', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showUserBar])

  useEffect(() => {
    if (switchingRole) {
      document.body.style.pointerEvents = 'none'
      document.body.style.opacity = '0.7'
      // document.body.style.overflow = 'hidden'
    } else {
      document.body.style.pointerEvents = ''
      document.body.style.opacity = ''
      // document.body.style.overflow = ''
    }
    return () => {
      document.body.style.pointerEvents = ''
      document.body.style.opacity = ''
      // document.body.style.overflow = ''
    }
  }, [switchingRole])

  const hideUserBar = (delay: number = 1500) => {
    clearTimeout(timer)
    const id = setTimeout(() => {
      setShowUserBar(false)
    }, delay)
    setTimer(id)
  }

  return (
    <div className="relative user-profile-container">
      <UserProfileImage height={h} width={w} setShowUserBar={setShowUserBar} />

      {showUserBar && (
        <div
          className="absolute w-64 bg-white text-color-grey-1 justify-start flex items-center flex-col right-0 top-14 h-max rounded-2xl p-5 shadow-2xl z-50"
          onPointerEnter={() => {
            if (timer) clearInterval(timer)
          }}
          // onPointerLeave={hasPointerEvents?handlePointerLeave:undefined}
          style={{ bottom: showRole ? '-380px' : '-196px' }}
        >
          <div className="w-full flex flex-col items-center gap-2 mb-4">
            <UserProfileImage
              height={73}
              width={73}
              setShowUserBar={setShowUserBar}
            />
            <h2 className="text-sm font-bold text-black truncate">
              {userData?.name}
            </h2>
            <h2 className="text-sm text-color-grey-11 truncate">
              {userData?.email ? userData?.email : userData?.phoneNumber}
            </h2>
          </div>
          <div className="w-full border mb-3"></div>
          <h2 className="w-full text-sm font-medium text-color-grey-1 mb-3">
            Viewing as: {capitalizeFirstLetter(userData?.role)}{' '}
          </h2>
          <div className="w-full ">
            {userData?.roles?.length > 1 && (
              <div
                className="flex px-5 py-3 items-center justify-between bg-color-grey rounded-xl mb-1 cursor-pointer"
                onClick={() => setShowRole(!showRole)}
              >
                <div className="flex items-center gap-2 text-color-black font-bold">
                  <SwitchRole />
                  <h2>Switch Role</h2>
                </div>
                <UpArrow showDownArrow={!showRole} />
              </div>
            )}
            <div className="space-y-1">
              {showRole &&
                userData?.roles
                  ?.filter(
                    (role: any, index: number, self: any[]) =>
                      role !== ROLES.Donor &&
                      index === self.findIndex((r: any) => r === role),
                  )
                  ?.map((role: any) => {
                    const isCurrentRole = role === userData?.role

                    return (
                      <RoleCard
                        key={role}
                        role={role}
                        isCurrentRole={isCurrentRole}
                        setShowUserBar={setShowUserBar}
                        setSwitchingRole={setSwitchingRole}
                      />
                    )
                  })}
            </div>
          </div>
          <Link
            href={'/account/my-public-profile'}
            className="flex px-5 py-3 w-full items-center gap-2 font-medium mt-1 cursor-pointer hover:bg-color-grey rounded-xl"
          >
            <EditIcon />
            <h2>Edit Profile</h2>
          </Link>
          <div
            className="flex px-5 py-3 w-full items-center gap-2 font-medium cursor-pointer hover:bg-color-grey rounded-xl"
            onClick={handleLogout}
          >
            <Logout />
            <h2 className="text-color-red-1">Log out</h2>
          </div>
        </div>
      )}
      {modal}
    </div>
  )
}

export default memo(UserProfile)
