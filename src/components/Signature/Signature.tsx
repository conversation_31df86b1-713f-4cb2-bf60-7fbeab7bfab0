import React, { useRef, useEffect, useState } from 'react'

interface SignatureProps {
  onSignatureComplete?: (signatureDataUrl: string) => void
}

const Signature: React.FC<SignatureProps> = ({ onSignatureComplete }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const [isDrawing, setIsDrawing] = useState(false)
  const [hasSignature, setHasSignature] = useState(false)
  const lastPointRef = useRef<{ x: number; y: number } | null>(null)
  const animationFrameRef = useRef<number | null>(null)

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const context = canvas.getContext('2d')
    if (!context) return

    // Set canvas size to match device pixel ratio for crisp rendering
    const dpr = window.devicePixelRatio || 1
    canvas.width = canvas.offsetWidth * dpr
    canvas.height = canvas.offsetHeight * dpr
    context.scale(dpr, dpr)

    // Set drawing styles
    context.strokeStyle = '#000000'
    context.lineWidth = 2
    context.lineCap = 'round'
    context.lineJoin = 'round'
    context.lineWidth = 1.5 // Slightly thinner for smoother appearance

    // Cleanup on unmount
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }
    }
  }, [])

  const getCoordinates = (
    event:
      | React.MouseEvent<HTMLCanvasElement>
      | React.TouchEvent<HTMLCanvasElement>,
    canvas: HTMLCanvasElement,
  ) => {
    const rect = canvas.getBoundingClientRect()
    const clientX =
      'touches' in event ? event.touches[0].clientX : event.clientX
    const clientY =
      'touches' in event ? event.touches[0].clientY : event.clientY
    return {
      x: clientX - rect.left,
      y: clientY - rect.top,
    }
  }

  const startDrawing = (
    e:
      | React.MouseEvent<HTMLCanvasElement>
      | React.TouchEvent<HTMLCanvasElement>,
  ) => {
    e.preventDefault()
    const canvas = canvasRef.current
    if (!canvas) return

    const context = canvas.getContext('2d')
    if (!context) return

    const { x, y } = getCoordinates(e, canvas)
    lastPointRef.current = { x, y }

    context.beginPath()
    context.moveTo(x, y)
    setIsDrawing(true)
  }

  const draw = (
    e:
      | React.MouseEvent<HTMLCanvasElement>
      | React.TouchEvent<HTMLCanvasElement>,
  ) => {
    if (!isDrawing) return

    const canvas = canvasRef.current
    if (!canvas) return

    const context = canvas.getContext('2d')
    if (!context) return

    const { x, y } = getCoordinates(e, canvas)

    if (lastPointRef.current) {
      // Use requestAnimationFrame for smoother rendering
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }

      animationFrameRef.current = requestAnimationFrame(() => {
        // Quadratic curve for smoother lines
        const midX = (lastPointRef.current!.x + x) / 2
        const midY = (lastPointRef.current!.y + y) / 2

        context.quadraticCurveTo(
          lastPointRef.current!.x,
          lastPointRef.current!.y,
          midX,
          midY,
        )
        context.stroke()

        lastPointRef.current = { x, y }
      })
    }
  }

  const stopDrawing = () => {
    if (!isDrawing) return
    setIsDrawing(false)
    setHasSignature(true)

    const canvas = canvasRef.current
    if (canvas && onSignatureComplete) {
      const dataUrl = canvas.toDataURL('image/png')
      onSignatureComplete(dataUrl)
    }

    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current)
      animationFrameRef.current = null
    }
  }

  const clearSignature = () => {
    const canvas = canvasRef.current
    if (!canvas) return

    const context = canvas.getContext('2d')
    if (!context) return

    context.clearRect(0, 0, canvas.width, canvas.height)
    setHasSignature(false)
    lastPointRef.current = null

    if (onSignatureComplete) {
      onSignatureComplete('')
    }
  }

  const handleTouchStart = (e: React.TouchEvent<HTMLCanvasElement>) => {
    startDrawing(e)
  }

  const handleTouchMove = (e: React.TouchEvent<HTMLCanvasElement>) => {
    e.preventDefault()
    draw(e)
  }

  const handleTouchEnd = (e: React.TouchEvent<HTMLCanvasElement>) => {
    e.preventDefault()
    stopDrawing()
  }

  return (
    <div className="w-full">
      <label className="block mb-2 text-sm font-medium">
        Signature <span className="text-red-500">*</span>
      </label>

      <div className="border border-gray-300 rounded-lg p-4">
        <canvas
          ref={canvasRef}
          className="w-full h-32 border border-gray-200 rounded cursor-crosshair bg-white"
          style={{ touchAction: 'none' }} // Prevent scrolling on touch
          onMouseDown={startDrawing}
          onMouseMove={draw}
          onMouseUp={stopDrawing}
          onMouseLeave={stopDrawing}
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
        />

        <div className="mt-2 flex justify-between items-center">
          <p className="text-sm text-gray-600">
            {hasSignature ? 'Signature captured' : 'Please sign above'}
          </p>
          <button
            type="button"
            onClick={clearSignature}
            className="px-3 py-1 text-sm bg-gray-200 hover:bg-gray-300 rounded transition-colors"
          >
            Clear
          </button>
        </div>
      </div>
    </div>
  )
}

export default Signature
