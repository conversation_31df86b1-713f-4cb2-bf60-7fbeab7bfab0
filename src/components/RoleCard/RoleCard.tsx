import { useAuth } from '@/contexts/AuthContext'
import { useApi } from '@/hooks/useApi'
import { updateAccountById } from '@/lib/actions/account.actions'
import { switchRole } from '@/lib/actions/login.actions'
import { useRouter } from 'next/navigation'
import React, { useEffect } from 'react'
import { toast } from 'react-toastify'

interface RoleCardProps {
  role: string
  isCurrentRole: boolean
  setShowUserBar: React.Dispatch<React.SetStateAction<boolean>>
  setSwitchingRole: React.Dispatch<React.SetStateAction<boolean>>
}

const RoleCard = ({
  role,
  isCurrentRole,
  setShowUserBar,
  setSwitchingRole,
}: RoleCardProps) => {
  const [isLoading, setIsLoading] = React.useState<boolean>(false)
  const { setUser } = useAuth()
  const router = useRouter()

  const [roleResponse, updateRole] = useApi((access: string) =>
    updateAccountById(access, { Last_Used_Role__c: role }),
  )

  useEffect(() => {
    if (roleResponse.isSuccess) {
      successSwitchRole(role)
    }
  }, [roleResponse])

  const successSwitchRole = async (role: string) => {
    try {
      await switchRole(role)
      setShowUserBar(false)
      setUser((prev: any) => ({ ...prev, role }))
      toast.success(`Switched to ${role} successfully!`)
      router.push('/dashboard')
    } catch (error) {
      console.error('Error switching role:', error)
    } finally {
      setIsLoading(false)
      setSwitchingRole(false)
    }
  }
  const handleRole = async (role: string) => {
    setIsLoading(true)
    setSwitchingRole(true)
    try {
      await updateRole(role)
    } catch (error) {
      console.error('Error updating role:', error)
      toast.error('Failed to switch role. Please try again.')
      setIsLoading(false)
      setSwitchingRole(false)
    }
  }
  return (
    <div
      onClick={isCurrentRole ? undefined : () => handleRole(role)}
      className={`ml-8 font-medium px-5 py-3 rounded-xl relative overflow-hidden ${
        isCurrentRole
          ? 'bg-gray-100 cursor-default'
          : 'cursor-pointer hover:bg-color-grey'
      }`}
    >
      <span className={`${isLoading ? 'animate-shimmer' : ''}`}>{role}</span>
      {isCurrentRole && (
        <div className="absolute -top-1 -right-1 bg-color-yellow text-white text-xs px-2 py-1 rounded-bl-lg rounded-tr-xl font-semibold">
          Active
        </div>
      )}
    </div>
  )
}

export default RoleCard
