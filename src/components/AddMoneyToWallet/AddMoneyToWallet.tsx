'use client'
import { useEffect, useState } from 'react'
import CustomButton from '../CustomComponents/CustomButton/CustomButton'
import { useApi } from '@/hooks/useApi'
import { addModeyToWallet } from '@/lib/actions/wallet.actions'
import { useDispatch } from 'react-redux'
import { useRouter } from 'next/navigation'
import { setPaymentToken } from '@/lib/redux'
import BackArrow from '../Icons/BackArrow'
import { addMoneyToYouthAccount } from '@/lib/actions/parent.actions'

const AddMoneyToWallet = ({
  balance,
  setIsAddWalletClicked,
  isFromParent = false,
  youthId = '',
}: {
  balance: number
  setIsAddWalletClicked: any
  isFromParent?: boolean
  youthId?: string
}) => {
  const [amount, setAmount] = useState<number>(0)
  const recommendedAmounts = [100, 200, 300, 500, 1000, 1500]
  const [addWalletResponse, setAddWalletResponse] = useApi((access: string) => {
    return isFromParent
      ? addMoneyToYouthAccount(access, youthId, amount)
      : addModeyToWallet(access, amount)
  })
  const dispatch = useDispatch()
  const router = useRouter()

  useEffect(() => {
    if (addWalletResponse.isSuccess) {
      dispatch(setPaymentToken(addWalletResponse.data?.data?.token))
      router.push(`/checkout?token=${addWalletResponse.data?.data?.token}`)
    }
  }, [addWalletResponse])

  const handleWalletAdd = () => {
    setAddWalletResponse(amount)
  }

  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/[^\d.]/g, '')
    if (value === '' || /^\d*\.?\d{0,2}$/.test(value)) {
      setAmount(Number(value))
    }
  }

  const handleRecommendedAmount = (value: number) => {
    setAmount(value)
  }

  const handleBack = () => {
    setIsAddWalletClicked(false)
  }

  return (
    <>
      {!isFromParent && (
        <div className="my-5">
          <BackArrow onClick={handleBack} />
        </div>
      )}
      <div className="max-w-2xl mx-auto bg-white rounded-2xl shadow-sm border-2">
        <div className="flex justify-between px-6 pt-6 items-center mb-5">
          <h1 className="text-xl font-bold">Add money</h1>
          <div className="font-semibold text-sm">
            Current balance: ${balance.toFixed(2)}
          </div>
        </div>
        <div className="border-b-2 w-full"></div>
        <div className="p-6">
          <div className="mb-6">
            <label className="block text-sm font-bold mb-2">
              How much would you like to add?
            </label>
            <div className="relative">
              <span className="absolute left-4 top-1/2 -translate-y-1/2 text-base font-semibold">
                $
              </span>
              <input
                type="text"
                value={amount == 0 ? '' : amount}
                onChange={handleAmountChange}
                placeholder="Enter amount"
                className="w-full px-8 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-600 placeholder-gray-400"
              />
            </div>
          </div>

          <div className="mb-8">
            <p className="mb-2 text-sm font-bold">Recommended</p>
            <div className="flex flex-wrap gap-3">
              {recommendedAmounts.map((value) => (
                <button
                  key={value}
                  onClick={() => handleRecommendedAmount(value)}
                  className={`px-4 py-2 rounded-lg text-sm    font-medium transition-colors
                                ${
                                  amount === value
                                    ? 'bg-gray-900 text-white'
                                    : 'bg-white text-gray-900 border border-gray-300 hover:bg-gray-50'
                                }`}
                >
                  ${value}
                </button>
              ))}
            </div>
          </div>

          <CustomButton
            isLoading={addWalletResponse.isFetching}
            title="Proceed to Pay"
            onClick={handleWalletAdd}
            height={12}
          />
        </div>
      </div>
    </>
  )
}

export default AddMoneyToWallet
