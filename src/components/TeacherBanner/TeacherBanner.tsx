'use client'
import Image from 'next/image'
import Link from 'next/link'
import React from 'react'
import useModal from '@/hooks/useModal'
import TeacherFollowComponentModal from '../TeacherFollowComponentModal/TeacherFollowComponentModal'

const getTeacherName = (
  teacherData: any,
  handleShowModal: () => void,
  isClickable: boolean,
) => {
  if (!teacherData || teacherData.length === 0) {
    return 'Anonymous Teacher'
  }

  const teacherNames = teacherData
    .filter((teacher: any) => teacher?.teacher_name)
    .map((teacher: any) => teacher.teacher_name)

  if (teacherNames.length === 0) {
    return 'Anonymous Teacher'
  }

  if (teacherNames.length === 1) {
    return isClickable ? (
      <Link href={`/teachers/${teacherData[0]?.teacher_id}`}>
        {teacherNames[0]}
      </Link>
    ) : (
      <span>{teacherNames[0]}</span>
    )
  }

  if (teacherNames.length === 2) {
    return isClickable ? (
      <>
        <Link href={`/teachers/${teacherData[0]?.teacher_id}`}>
          {teacherNames[0]}
        </Link>
        {' & '}
        <Link href={`/teachers/${teacherData[1]?.teacher_id}`}>
          {teacherNames[1]}
        </Link>
      </>
    ) : (
      <>
        <span>{teacherNames[0]}</span>
        {' & '}
        <span>{teacherNames[1]}</span>
      </>
    )
  }

  if (teacherNames.length > 2) {
    return isClickable ? (
      <>
        <Link
          href={`/teachers/${teacherData[0]?.teacher_id}`}
          className="hover:text-color-blue transition-colors"
        >
          {teacherNames[0]}
        </Link>
        <span className="text-color-grey-1"> & </span>
        <button
          className="inline-flex items-center px-3 py-1 bg-color-blue-4 text-color-blue-1 text-sm font-medium rounded-full hover:bg-color-blue-1 hover:text-white transition-all duration-200 cursor-pointer"
          onClick={handleShowModal}
        >
          {teacherNames.length - 1} others
        </button>
      </>
    ) : (
      <>
        <span>{teacherNames[0]}</span>
        <span className="text-color-grey-1"> & </span>
        <span>{teacherNames.length - 1} others</span>
      </>
    )
  }

  return 'Anonymous Teacher'
}

const TeacherBanner = ({
  teacherData,
  isClickable = true,
}: {
  teacherData: any
  isClickable?: boolean
}) => {
  const [modal, showModal] = useModal()

  const handleShowModal = () => {
    showModal({
      title: '',
      contentFn: (onClose) => (
        <TeacherFollowComponentModal teacherData={uniqueTeacherData} />
      ),
      size: 'md',
      showCloseButton: false,
      closeOnClickOutside: true,
    })
  }

  if (!teacherData || teacherData.length === 0) {
    return (
      <div className="flex items-center gap-2">
        <div className="flex items-center">
          <div className="flex">
            <div className="border-2 border-white rounded-full">
              <Image
                width={30}
                height={30}
                src="/teacher-dummy.png"
                alt="User avatar"
                className="rounded-full w-9 h-9 object-cover"
              />
            </div>
          </div>
        </div>
        <h2 className="text-sm font-bold w-max">Anonymous Teacher</h2>
      </div>
    )
  }

  // make sure that unique teacher data is passed
  const uniqueTeacherData = Array.from(
    new Set(teacherData.map((teacher: any) => teacher.teacher_id)),
  ).map((id) => {
    return teacherData.find((teacher: any) => teacher.teacher_id === id)
  })

  return (
    <>
      <div className="flex items-center gap-2">
        <div className="flex items-center">
          <div className="flex">
            {uniqueTeacherData
              .slice(0, 3)
              .map((teacher: any, index: number) => {
                const content = (
                  <Image
                    width={30}
                    height={30}
                    src={teacher?.teacher_photo ?? '/teacher-dummy.png'}
                    alt="User avatar"
                    className="rounded-full w-9 h-9 object-cover"
                  />
                )

                return isClickable ? (
                  <Link
                    href={`/teachers/${teacher?.teacher_id}`}
                    key={teacher?.teacher_id || index}
                    className={`border-2 border-white rounded-full ${
                      index > 0 ? '-ml-2' : ''
                    }`}
                  >
                    {content}
                  </Link>
                ) : (
                  <div
                    key={teacher?.teacher_id || index}
                    className={`border-2 border-white rounded-full ${
                      index > 0 ? '-ml-2' : ''
                    }`}
                  >
                    {content}
                  </div>
                )
              })}
          </div>
        </div>
        <h2 className="text-sm font-bold w-max">
          {getTeacherName(uniqueTeacherData, handleShowModal, isClickable)}
        </h2>
      </div>

      {modal}
    </>
  )
}

export default TeacherBanner
