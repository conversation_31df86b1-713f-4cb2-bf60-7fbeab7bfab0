'use client'
import React, { useCallback, useState } from 'react'
import CustomDropDown from '../CustomComponents/CustomDropDown/CustomDropDown'
import Genre from '@/components/Icons/Genre'
import Hybrid from '@/components/Icons/Hybrid'
import Level from '@/components/Icons/Level'
import Person from '@/components/Icons/Person'
import ForwardArrow from '../Icons/ForwardArrow'
import { useRouter } from 'next/navigation'
import { useBreakpoint } from '@/hooks/useBreakpoint'

const FindClass = ({
  genres,
  types,
  levels,
  teachers,
}: {
  genres: any
  types: any
  levels: any
  teachers: any
}) => {
  const [selectedGenre, setSelectedGenre] = useState<string[]>([])
  const [selectedLevel, setSelectedLevel] = useState<string[]>([])
  const [selectedType, setSelectedType] = useState<string>('')
  const [selectedTeacher, setSelectedTeacher] = useState<string>('')
  const router = useRouter()
  const breakpoint = useBreakpoint()
  const isMobile = breakpoint === 'xs' || breakpoint === 'sm'

  const handleFilter = useCallback((value: string, filterType: string) => {
    switch (filterType) {
      case 'Genre':
        setSelectedGenre((prev) =>
          prev.includes(value)
            ? prev.filter((item) => item !== value)
            : [...prev, value],
        )
        break
      case 'Level':
        setSelectedLevel((prev) =>
          prev.includes(value)
            ? prev.filter((item) => item !== value)
            : [...prev, value],
        )
        break
      case 'Type':
        setSelectedType((prev) => (prev === value ? '' : value))
        break
      case 'Instructor':
        setSelectedTeacher((prev) => (prev === value ? '' : value))
        break
    }
  }, [])

  const handleFilterSearch = () => {
    const query = new URLSearchParams({
      genre: selectedGenre.join(','),
      level: selectedLevel.join(','),
      type: selectedType,
      teacher: selectedTeacher,
    }).toString()
    router.push(`/classes?${query}`)
  }

  return (
    <section className="px-4 sm:px-8 md:px-16 lg:px-24 xl:px-40 pb-16 md:pb-24 lg:pb-28">
      {/* Heading */}
      <h1 className="font-extrabold text-2xl sm:text-3xl md:text-4xl lg:text-5xl w-full text-center">
        Find your next class
      </h1>

      {/* Filters */}
      <div className="w-full flex flex-wrap lg:flex-nowrap items-center justify-center gap-4 sm:gap-6 md:gap-8 lg:gap-10 mt-8 sm:mt-10 md:px-8 lg:px-16 xl:px-20">
        <CustomDropDown
          icon={<Person />}
          title={'Instructor'}
          options={teachers}
          column={1}
          handleOptionClick={handleFilter}
          width="w-full sm:w-auto"
          text="base"
          selectedList={selectedTeacher}
          isTitleType={true}
        />
        <CustomDropDown
          icon={<Genre />}
          title={'Genre'}
          options={genres?.data || []}
          selectedList={selectedGenre}
          column={isMobile ? 1 : 2}
          isTitleType={true}
          handleOptionClick={handleFilter}
          text="base"
        />
        <CustomDropDown
          icon={<Level />}
          title={'Level'}
          options={levels?.data || []}
          selectedList={selectedLevel}
          column={isMobile ? 1 : 2}
          isTitleType={true}
          handleOptionClick={handleFilter}
          text="base"
        />
        <CustomDropDown
          icon={<Hybrid />}
          title={'Type'}
          options={types?.data || []}
          selectedList={selectedType}
          column={1}
          isTitleType={true}
          handleOptionClick={handleFilter}
          width="w-full sm:w-auto"
          text="base"
        />
      </div>

      {/* Button */}
      <div className="w-full flex items-center justify-center mt-8 sm:mt-10">
        <button
          className="bg-color-yellow-1 text-lg sm:text-xl flex items-center justify-center gap-2 py-2.5 sm:py-3 px-6 sm:px-7 rounded-2xl sm:rounded-3xl w-max font-semibold"
          onClick={handleFilterSearch}
        >
          Search <ForwardArrow />
        </button>
      </div>
    </section>
  )
}

export default FindClass
