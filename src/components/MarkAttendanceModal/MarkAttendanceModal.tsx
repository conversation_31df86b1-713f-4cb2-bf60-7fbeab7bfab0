import React, { useEffect, useState } from 'react'
import BackArrow from '../Icons/BackArrow'
import { useApi } from '@/hooks/useApi'
import {
  getMeetingAttendance,
  markAttendance,
} from '@/lib/actions/teacher.actions'
import CustomButton from '../CustomComponents/CustomButton/CustomButton'
import { CrossIcon } from '../Icons/CrossIcon'
import { TickIcon } from '../Icons/TickIcon'
import { toast } from 'react-toastify'
import MarkAttendanceSkeleton from '../Skeletons/MarkAttendanceSkeleton'
import { X } from 'lucide-react'
import { setTodoList } from '@/lib/redux'
import { useDispatch } from 'react-redux'

type AttendanceArgs = {
  studentId: string
  attendenceType: 'In Person' | 'Online' | 'Absent'
}

interface Attendee {
  id: string
  name: string
  email: string | null
  selected?: boolean
  status?: 'Online' | 'In Person' | 'Absent' | null
}

const MarkAttendanceModal = ({
  onClose,
  meetingId,
  markedAttendance,
  classAttendees,
  setPastMeetings,
  pastMeetings,
  setTodoListItems,
  isNotesRequired,
  setAttendanceState,
  isSubjectNotMandatory,
}: {
  onClose: () => void
  meetingId: string
  markedAttendance: boolean
  classAttendees?: any
  setPastMeetings?: any
  pastMeetings?: false
  setTodoListItems?: any
  isNotesRequired?: boolean
  setAttendanceState?: any
  isSubjectNotMandatory?: boolean
}) => {
  const [isMarkAttendanceClicked, setIsMarkAttendanceClicked] =
    useState<boolean>(false)
  const [notes, setNotes] = useState<string>('')
  const [subjectOfClass, setSubjectOfClass] = useState<string>('')
  const [practicalApplications, setPracticalApplications] = useState<string>('')
  const [notesError, setNotesError] = useState<string>('')
  const [subjectError, setSubjectError] = useState<string>('')
  const [practicalError, setPracticalError] = useState<string>('')
  const [attendees, setAttendees] = useState<Attendee[]>(classAttendees)
  const [response, getAttendance] = useApi(
    (access: string, meetingId: string) =>
      getMeetingAttendance(access, meetingId),
  )
  const [markAttendanceResponse, markMeetingAttendance] = useApi(
    (
      access: string,
      meetingId: string,
      status: any,
      notes: string,
      subjectOfClass: string,
      practicalApplications: string,
    ) =>
      markAttendance(
        access,
        meetingId,
        status,
        notes,
        subjectOfClass,
        practicalApplications,
      ),
  )

  const dispatch = useDispatch()

  useEffect(() => {
    if (response.isSuccess) {
      const attendenceData = response?.data?.data?.data || []
      if (attendenceData.length > 0) {
        setAttendees(attendenceData)
      }
      setNotes(response?.data?.data?.notes || '')
      setSubjectOfClass(response?.data?.data?.subjectOfClass || '')
      setPracticalApplications(
        response?.data?.data?.practicalApplications || '',
      )
    }
  }, [response])

  useEffect(() => {
    if (markAttendanceResponse.isSuccess) {
      if (pastMeetings) {
        setPastMeetings((prev: any) => {
          const updatedMeetings = prev?.map((meeting: any) => {
            if (meeting.Id === meetingId) {
              return {
                ...meeting,
                Attendance_Marked__c: true,
              }
            }
            return meeting
          })
          return updatedMeetings
        })
      }
      if (setTodoListItems) {
        setTodoListItems((prev: any) => {
          const updated = prev.filter((item: any) => item.Id !== meetingId)
          dispatch(setTodoList(updated))
          return updated
        })
      }
      if (setAttendanceState) {
        setAttendanceState((prev: any) => ({
          ...prev,
          [meetingId]: true, // mark as attended
        }))
      }
      onClose()
      toast.success('Attendance marked successfully', { autoClose: 2000 })
    }
  }, [markAttendanceResponse])

  useEffect(() => {
    getAttendance(meetingId)
  }, [])

  const toggleSelectAll = () => {
    const allSelected = attendees?.every((a) => a.selected)
    setAttendees(
      attendees?.map((attendee) => ({
        ...attendee,
        selected: !allSelected,
      })),
    )
  }

  const toggleAttendee = (id: string) => {
    setAttendees(
      attendees?.map((attendee) =>
        attendee.id === id
          ? { ...attendee, selected: !attendee.selected }
          : attendee,
      ),
    )
  }

  const setAttendance = (
    id: string,
    status: 'Online' | 'In Person' | 'Absent',
  ) => {
    setAttendees(
      attendees?.map((attendee) =>
        attendee.id === id ? { ...attendee, status: status } : attendee,
      ),
    )
  }

  const handleMarkAttendance = () => {
    if (attendees?.length == 0) {
      toast.error('Cannot mark attendance when attendees count is 0')
      return
    }

    // Clear previous errors
    setNotesError('')
    setSubjectError('')
    setPracticalError('')

    let hasError = false

    // If Subject fields are NOT mandatory (Events/Clubs), skip validation
    if (!isSubjectNotMandatory) {
      // Validate Subject of Class (mandatory for all except Events/Clubs)
      if (!subjectOfClass || subjectOfClass.trim() === '') {
        setSubjectError('Subject of Class field is required')
        hasError = true
      }

      // Validate Practical Applications (mandatory for all except Events/Clubs)
      if (!practicalApplications || practicalApplications.trim() === '') {
        setPracticalError('Practical Applications field is required')
        hasError = true
      }

      // Validate Notes (mandatory for all except Events/Clubs)
      if (!notes || notes.trim() === '') {
        setNotesError('Notes field is required')
        hasError = true
      }
    } else {
      // For Events/Clubs, only validate notes if isNotesRequired is true
      if (isNotesRequired && (!notes || notes.trim() === '')) {
        setNotesError('Notes field is required')
        hasError = true
      }
    }

    if (hasError) {
      toast.error('Please fill in all required fields', {
        autoClose: 2000,
      })
      return
    }

    const data: AttendanceArgs[] = attendees?.map((attendee: any) => ({
      studentId: attendee.id,
      attendenceType: attendee.status,
    }))
    markMeetingAttendance(
      meetingId,
      data,
      notes,
      subjectOfClass,
      practicalApplications,
    )
  }

  const handleMarkAllAttendance = (
    status: 'Online' | 'In Person' | 'Absent',
  ) => {
    setAttendees(
      attendees?.map((attendee) =>
        attendee.selected ? { ...attendee, status: status } : attendee,
      ),
    )
  }

  return (
    <div className="p-5">
      <div
        className={`flex items-center justify-between ${isMarkAttendanceClicked ? 'border-b pb-4 border-gray-200' : 'border-b pb-4 border-gray-200'}`}
      >
        {isMarkAttendanceClicked ? (
          <>
            <div className="flex items-center gap-3">
              <BackArrow onClick={() => setIsMarkAttendanceClicked(false)} />
              <h2 className="text-lg font-semibold">Class Details</h2>
            </div>
          </>
        ) : (
          <>
            <h2 className="text-lg font-semibold">Name</h2>
            <h2 className="text-lg font-semibold">Attendance</h2>
          </>
        )}
        <button
          onClick={onClose}
          className="p-1 hover:bg-color-grey-5 rounded-full transition-colors"
        >
          <X size={20} className="text-color-grey-1" />
        </button>
      </div>
      {!isMarkAttendanceClicked && (
        <div className="bg-color-grey grid grid-cols-10 items-center py-2">
          <input
            type="checkbox"
            checked={attendees?.every((a) => a.selected)}
            onChange={toggleSelectAll}
            className="w-4 h-4 rounded border-gray-300 text-orange-400 focus:ring-orange-400 cursor-pointer col-span-1"
          />
          <h1 className="col-span-3 text-base font-semibold mt-0">
            Select all
          </h1>
          <div className="col-span-6 flex space-x-3">
            <button
              className="px-4 py-2 flex items-center h-8 justify-between gap-1 text-xs border border-gray-200 rounded hover:bg-gray-50"
              onClick={() => handleMarkAllAttendance('Online')}
            >
              <div className="hidden md:block">
                <TickIcon />
              </div>
              Online
            </button>
            <button
              className="px-4 py-2 flex items-center h-8 justify-between gap-1 text-xs border border-gray-200 rounded hover:bg-gray-50"
              onClick={() => handleMarkAllAttendance('In Person')}
            >
              <div className="hidden md:block">
                <TickIcon />
              </div>
              In Person
            </button>
            <button
              className="px-4 py-2 flex items-center h-8 justify-between gap-1 text-xs border border-gray-200 rounded hover:bg-gray-50"
              onClick={() => handleMarkAllAttendance('Absent')}
            >
              <CrossIcon />
              Absent
            </button>
          </div>
        </div>
      )}
      <div className="">
        {response.isFetching ? (
          <MarkAttendanceSkeleton />
        ) : (
          <>
            {isMarkAttendanceClicked ? (
              <div className="pt-3 space-y-2 w-96">
                {!isSubjectNotMandatory && (
                  <>
                    <div>
                      <h1 className="text-base font-medium mb-1">
                        Subject of Class
                        <span className="text-red-500 ml-1">*</span>
                      </h1>
                      <textarea
                        className={`border text-sm rounded-md w-full h-20 p-2 text-color-grey-1 resize-none ${
                          subjectError
                            ? 'border-red-500'
                            : 'border-color-grey-2'
                        }`}
                        onChange={(e) => {
                          setSubjectOfClass(e.target.value)
                          if (subjectError) {
                            setSubjectError('')
                          }
                        }}
                        value={subjectOfClass}
                        placeholder="Subject taught"
                      ></textarea>
                      {subjectError && (
                        <p className="text-red-500 text-xs mt-1">
                          {subjectError}
                        </p>
                      )}
                    </div>

                    <div>
                      <h1 className="text-base font-medium mb-1">
                        Practical Applications
                        <span className="text-red-500 ml-1">*</span>
                      </h1>
                      <textarea
                        className={`border text-sm rounded-md w-full h-20 p-2 text-color-grey-1 resize-none ${
                          practicalError
                            ? 'border-red-500'
                            : 'border-color-grey-2'
                        }`}
                        onChange={(e) => {
                          setPracticalApplications(e.target.value)
                          if (practicalError) {
                            setPracticalError('')
                          }
                        }}
                        value={practicalApplications}
                        placeholder="How was the learning implemented"
                      ></textarea>
                      {practicalError && (
                        <p className="text-red-500 text-xs mt-1">
                          {practicalError}
                        </p>
                      )}
                    </div>
                  </>
                )}

                <div>
                  <h1 className="text-base font-medium mb-1">
                    Notes
                    {(!isSubjectNotMandatory || isNotesRequired) && (
                      <span className="text-red-500 ml-1">*</span>
                    )}
                  </h1>
                  <textarea
                    className={`border text-sm rounded-md w-full h-20 p-2 text-color-grey-1 resize-none ${
                      notesError ? 'border-red-500' : 'border-color-grey-2'
                    }`}
                    onChange={(e) => {
                      setNotes(e.target.value)
                      if (notesError) {
                        setNotesError('')
                      }
                    }}
                    value={notes}
                    placeholder="Exercises used (did the students write/speak, etc)"
                  ></textarea>
                  {notesError && (
                    <p className="text-red-500 text-xs mt-1">{notesError}</p>
                  )}
                </div>
              </div>
            ) : (
              <>
                {attendees?.map((attendee) => (
                  <div
                    key={attendee.id}
                    className="grid grid-cols-10 gap-x-3 border-b border-color-grey-2 py-3"
                  >
                    <div className="flex items-center col-span-1 space-x-3">
                      {attendee.selected && (
                        <input
                          type="checkbox"
                          checked={attendee.selected}
                          onChange={() => toggleAttendee(attendee.id)}
                          className="w-4 h-4 rounded border-gray-300 text-orange-400 focus:ring-orange-400 cursor-pointer"
                        />
                      )}
                    </div>
                    <div
                      className="text-base  w-40 line-clamp-1 h-6 font-semibold col-span-3"
                      title={attendee.name}
                    >
                      {attendee.name}
                    </div>
                    <div className="flex space-x-3 col-span-6">
                      <button
                        onClick={() => setAttendance(attendee.id, 'Online')}
                        className={`px-4 py-2 border text-xs flex h-8 items-center justify-between gap-1 rounded transition-colors
                      ${
                        attendee.status === 'Online'
                          ? 'border-orange-400 text-orange-400'
                          : 'border-gray-200 hover:bg-gray-50'
                      }`}
                      >
                        <div className="hidden md:block">
                          <TickIcon />
                        </div>
                        Online
                      </button>
                      <button
                        onClick={() => setAttendance(attendee?.id, 'In Person')}
                        className={`px-4 py-2 border text-xs flex h-8 items-center justify-between gap-1 rounded transition-colors
                      ${
                        attendee?.status === 'In Person'
                          ? 'border-orange-400 text-orange-400'
                          : 'border-gray-200 hover:bg-gray-50'
                      }`}
                      >
                        <div className="hidden md:block">
                          <TickIcon />
                        </div>
                        In Person
                      </button>
                      <button
                        onClick={() => setAttendance(attendee.id, 'Absent')}
                        className={`px-4 py-2 border text-xs flex h-8 items-center justify-between gap-1 rounded transition-colors
                      ${
                        attendee.status === 'Absent'
                          ? 'border-orange-400 text-orange-400'
                          : 'border-gray-200 hover:bg-gray-50'
                      }`}
                      >
                        <CrossIcon />
                        Absent
                      </button>
                    </div>
                  </div>
                ))}
                {attendees?.length === 0 && !response.isFetching && (
                  <div className="w-full h-52 flex items-center justify-center">
                    <h1 className="text-lg font-semibold">
                      No attendees found
                    </h1>
                  </div>
                )}
              </>
            )}
          </>
        )}
        <div className="mt-3">
          <CustomButton
            isLoading={markAttendanceResponse.isFetching}
            title={
              isMarkAttendanceClicked
                ? 'Submit Report'
                : markedAttendance
                  ? 'Edit Attendance'
                  : 'Mark Attendance'
            }
            isDisabled={response.isFetching || attendees?.length === 0}
            onClick={() => {
              if (isMarkAttendanceClicked) {
                handleMarkAttendance()
              } else {
                // If isSubjectNotMandatory is true (Events/Clubs), skip the second page and submit directly
                if (isSubjectNotMandatory) {
                  handleMarkAttendance()
                } else {
                  setIsMarkAttendanceClicked(true)
                }
              }
            }}
          />
        </div>
      </div>
    </div>
  )
}

export default MarkAttendanceModal
