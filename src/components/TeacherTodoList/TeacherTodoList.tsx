'use client'
import { useA<PERSON> } from '@/hooks/useApi'
import useModal from '@/hooks/useModal'
import { getTodoList } from '@/lib/actions/teacher.actions'
import { setTodoList, useDispatch, useSelector } from '@/lib/redux'
import dayjs from 'dayjs'
import React, { useEffect, useState } from 'react'
import AttendanceModalContent from '../AttendanceModalContent/AttendanceModalContent'
import MarkAttendanceModal from '../MarkAttendanceModal/MarkAttendanceModal'
import TodoListShimmer from '../TeacherTodoListShimmer/TeacherTodoListShimmer'
import { selectTodoList } from '@/lib/redux'
import { useBreakpoint } from '@/hooks/useBreakpoint'

const TeacherTodoList = () => {
  const [showAll, setShowAll] = useState(false)
  const [todoListItems, setTodoListItems] = useState<any[]>([])
  const [visibleItems, setVisibleItems] = useState<any[]>([])
  const cachedTodoList = useSelector(selectTodoList)
  const dispatch = useDispatch()

  const handleViewAllButton = () => {
    setShowAll((prev) => !prev)
  }

  const [modal, showModal] = useModal()

  const [todoList, fetchTodoList] = useApi((accessToken: string) =>
    getTodoList(accessToken),
  )

  const breakpoint = useBreakpoint()

  const getModalSizeByBreakpoint = (bp: string) => {
    switch (bp) {
      // case 'xs':
      //   return 'xs'
      case 'sm':
        return 'sm'
      case 'md':
        return 'md'
      case 'lg':
        return 'lg'
      case 'xl':
        return 'xl'
      // case '2xl':
      //   return '2xl'
      default:
        return 'sm'
    }
  }

  const modalSize = getModalSizeByBreakpoint(breakpoint)

  useEffect(() => {
    setVisibleItems(showAll ? todoListItems : todoListItems?.slice(0, 3))
  }, [showAll, todoListItems])

  useEffect(() => {
    if (todoList?.isSuccess) {
      const dataItems = todoList?.data?.data
      dispatch(setTodoList(dataItems))
      setTodoListItems(dataItems)
    }
  }, [todoList])

  useEffect(() => {
    if (cachedTodoList.length > 0) {
      setTodoListItems(cachedTodoList)
      setVisibleItems(showAll ? cachedTodoList : cachedTodoList.slice(0, 3))
    }
  }, [cachedTodoList])

  useEffect(() => {
    if (cachedTodoList.length === 0) {
      fetchTodoList()
    }
  }, [])

  const handleModal = (
    meetingId: string,
    markedAttendance: boolean,
    countType: boolean = false,
    classAttendees: any,
  ) => {
    showModal({
      title: ``,
      contentFn: (onClose) =>
        countType ? (
          <AttendanceModalContent
            meetingId={meetingId}
            onClose={onClose}
            setTodoListItems={setTodoListItems}
          />
        ) : (
          <MarkAttendanceModal
            onClose={onClose}
            meetingId={meetingId}
            markedAttendance={markedAttendance}
            classAttendees={classAttendees}
            pastMeetings={false}
            setTodoListItems={setTodoListItems}
            // setPastMeetings={setPastMeetings}
          />
        ),
      closeOnClickOutside: true,
      size: 'lg',
      rounded: '2xl',
      padding: 0,
      showCloseButton: false,
    })
  }

  const renderTodoItem = (item: any, index: number) => (
    <div key={index} className="py-3 border-b last:border-none w-full">
      <div className="flex items-center justify-between flex-col xl:flex-row gap-4 w-full">
        <div className="w-full xl:w-[120px] shrink-0 text-sm font-semibold text-color-grey-1 text-center xl:text-start">
          {dayjs(item?.Starts_On__c).format('MMMM D, YYYY')}
        </div>
        <div className="flex-1 min-w-0">
          <div className="text-sm font-semibold text-color-black whitespace-normal break-words">
            {item?.Class_and_Event__r?.Title__c}
          </div>
        </div>

        <div className="w-full xl:w-[100px] shrink-0 text-sm font-semibold text-color-grey-1 text-center xl:text-start">
          {item?.Teacher__r?.Name}
        </div>

        <button
          className="shrink-0 bg-color-yellow px-5 py-[10px] text-color-black font-semibold text-sm rounded-lg"
          onClick={() =>
            handleModal(
              item?.Id,
              item?.Attendance_Marked__c,
              item?.Headcount_based_attendance__c,
              item?.classAttendees,
            )
          }
        >
          Mark Attendance
        </button>
      </div>
    </div>
  )

  return (
    <div className="bg-neutral-white rounded-xl p-5 w-full">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-base font-semibold leading-6 tracking-normal">
          To-Do List
        </h2>
        {(cachedTodoList?.length > 3 || todoListItems?.length > 3) && (
          <button
            className="text-xs font-normal leading-[18px] tracking-normal flex items-center gap-1"
            onClick={handleViewAllButton}
          >
            {showAll ? 'View Less' : 'View All'}
          </button>
        )}
      </div>

      <div
        className={showAll ? 'max-h-[320px] overflow-y-auto' : ''}
        style={{ scrollbarWidth: 'none' }}
      >
        {cachedTodoList?.length > 0 ? (
          (showAll ? cachedTodoList : cachedTodoList.slice(0, 3)).map(
            renderTodoItem,
          )
        ) : todoList?.isFetching ? (
          <TodoListShimmer />
        ) : visibleItems?.length === 0 ? (
          <div className="text-sm text-color-grey-1 text-center py-4 font-medium">
            No items to show
          </div>
        ) : (
          visibleItems?.map(renderTodoItem)
        )}
      </div>
      {modal}
    </div>
  )
}

export default TeacherTodoList
