'use client'
import React from 'react'
import { format, addWeeks, startOfWeek, addDays } from 'date-fns'
import Left<PERSON>rrow from '../Icons/LeftArrow'
import RightArrow from '../Icons/RightArrow'

const WeeklyCalendar = ({
  currentWeek,
  setCurrentWeek,
}: {
  currentWeek: any
  setCurrentWeek: any
}) => {
  const weekDays = ['S', 'M', 'T', 'W', 'T', 'F', 'S']

  const handlePrevWeek = () => {
    setCurrentWeek((prev: any) => addWeeks(prev, -1))
  }

  const handleNextWeek = () => {
    setCurrentWeek((prev: any) => addWeeks(prev, 1))
  }

  const handleDateClick = (date: any) => {
    setCurrentWeek(date)
  }

  const renderWeek = () => {
    const startDate = startOfWeek(currentWeek)
    return weekDays.map((day, index) => {
      const date = addDays(startDate, index)
      const isToday = date.toDateString() === currentWeek.toDateString()
      return (
        <div
          key={index}
          className={`flex flex-col items-center cursor-pointer ${isToday ? 'bg-color-yellow rounded-full' : ''} py-1.5`}
          onClick={() => handleDateClick(date)}
        >
          <div
            className={`text-sm  ${isToday ? 'text-color-black' : 'text-color-grey-1'}`}
          >
            {day}
          </div>
          <div
            className={`mt-4 flex items-center justify-center rounded-full ${isToday ? 'bg-color-black text-white' : 'bg-color-grey'} w-6 h-6 md:w-7 md:h-7`}
          >
            {format(date, 'd')}
          </div>
        </div>
      )
    })
  }

  return (
    <div className="h-40 bg-white rounded-2xl p-3 flex flex-col justify-center">
      <div className="flex justify-between items-center mb-4">
        <button onClick={handlePrevWeek} className="text-gray-600">
          <LeftArrow dark={true} />
        </button>
        <h2 className="text-sm font-bold">{format(currentWeek, 'MMM yyyy')}</h2>
        <button onClick={handleNextWeek} className="text-gray-600">
          <RightArrow dark={true} />
        </button>
      </div>
      <div className="grid grid-cols-7 gap-3 md:gap-3">{renderWeek()}</div>
    </div>
  )
}

export default WeeklyCalendar
