'use client'
import React, { useEffect, useMemo } from 'react'
import FollowFollowedButton from '../FollowFollowedButton/FollowFollowedButton'
import Person from '../Icons/Person'
import Class from '../Icons/Class'
import CalendarIcon from '../Icons/CalendarIcon'
import Clock from '../Icons/Clock'
import Share from '../Icons/Share'
import EyeIcon from '../Icons/EyeIcon'
import {
  CLASS_DETAILS_TABS,
  STUDENT_TABS,
  TEACHER_CLASS_TABS,
} from '@/utils/constants'
import { ROLES } from '@/types/enum'
import {
  formatDateToAmerican,
  formatSalesforceTime,
  formatDateTimeRange,
  getBackgroundColor,
} from '@/utils/utils'
import ClassAttendeesCard from '../ClassAttendeesCard/ClassAttendeesCard'
import TeacherBanner from '../TeacherBanner/TeacherBanner'
import ClassProgress from '../ClassProgress/ClassProgress'
import { useRouter } from 'next/navigation'
import { toast } from 'react-toastify'
import ClassTabs from '../ClassTabs/ClassTabs'
import useModal from '@/hooks/useModal'
import AddressModal from '../AddressModal/AddressModal'
import { useSelector } from 'react-redux'
import { selectFollowedMap } from '@/lib/redux'
import { useAuth } from '@/contexts/AuthContext'
import ClassTypeOnDetailPage from '../ClassTypeOnDetailPage/ClassTypeOnDetailPage'
import Level from '../Icons/Level'

interface User {
  id: number
  name: string
  email: string
}

const ClassDetailPage = ({
  classDetails,
  userData,
  userBadges,
  isAlreadyEnrolled = false,
}: {
  classDetails: any
  userData: any
  userBadges: any
  isAlreadyEnrolled?: boolean
}) => {
  const defaultTab = isAlreadyEnrolled ? 'schedule' : 'class-details'
  const [activeTab, setActiveTab] = React.useState(defaultTab)
  const { user } = useAuth()
  const users: User[] = classDetails?.classAttendees || []
  const router = useRouter()
  const followedMap = useSelector(selectFollowedMap)
  const [addressModal, showAddressModal] = useModal()
  const tabs = isAlreadyEnrolled ? TEACHER_CLASS_TABS : STUDENT_TABS

  useEffect(() => {
    let timmer: NodeJS.Timeout
    if (classDetails?.errors) {
      toast.error(classDetails?.errors[0])
      timmer = setTimeout(() => {
        router.push('/dashboard')
      }, 1000)
    }
    return () => {
      if (timmer) clearTimeout(timmer)
    }
  }, [classDetails])

  const handleShare = () => {
    const classLink = `${process.env.NEXT_PUBLIC_APP_URL}/classes/${classDetails?.Id}`
    const isOnline = classDetails?.Type__c === 'Online'
    const isHybrid = classDetails?.Type__c === 'Hybrid'
    let textToCopy = ''
    let toastMsg = ''

    if (isHybrid || isOnline) {
      textToCopy = `Class Link:\n${classLink}\n\nZoom Link:\n${classDetails?.Zoom_Meeting_Link__c || ''}`
      toastMsg = 'Class & Zoom Link copied to clipboard'
    } else {
      textToCopy = classLink
      toastMsg = 'Class Link copied to clipboard'
    }

    navigator.clipboard.writeText(textToCopy)
    toast.success(toastMsg, { autoClose: 2500 })
  }

  const classTime = formatSalesforceTime(
    classDetails?.Meetings__r?.records[0]?.Starts_On__c,
    classDetails?.Meetings__r?.records[0]?.Ends_On__c,
  )

  const nextMeetingDate = useMemo(() => {
    const nextMeetingStart =
      classDetails?.upcomingMeetings &&
      classDetails?.upcomingMeetings[0]?.Starts_On__c
    const nextMeetingEnd =
      classDetails?.upcomingMeetings &&
      classDetails?.upcomingMeetings[0]?.Ends_On__c
    return nextMeetingStart
      ? formatDateTimeRange(nextMeetingStart, nextMeetingEnd)
      : ''
  }, [classDetails])

  const teacherIds = useMemo(() => {
    return classDetails?.associatedTeachers
      ?.map((teacher: any) => teacher.teacher_id)
      .filter(
        (id: string) => id !== user?.id && id !== undefined && id !== null,
      )
  }, [classDetails?.associatedTeachers, user])

  return (
    <div
      className={`bg-white p-5 md:p-7 space-y-5 ${classDetails?.Banner_Image__c ? 'rounded-b-2xl' : 'rounded-2xl'}`}
    >
      {userData?.role === ROLES.Teacher && (
        <div className="flex justify-start w-full text-base font-semibold border-b-2 border-color-black mb-5 overflow-x-auto">
          <div className="w-max flex justify-center gap-5 border-color-black">
            {CLASS_DETAILS_TABS.map((tab, index) => (
              <div
                key={index}
                onClick={() => setActiveTab(tab.key)}
                className={`pb-3 cursor-pointer whitespace-nowrap ${activeTab === tab.key ? 'border-b-2 border-color-black' : ''}`}
              >
                {tab.title}
              </div>
            ))}
          </div>
        </div>
      )}

      {activeTab === 'class-attendees' ? (
        <div className="w-full overflow-x-auto">
          <div className="grid grid-cols-[1fr,1.5fr,48px] gap-4 mb-4 min-w-[400px]">
            <h2 className="font-extrabold text-base">Name</h2>
            <h2 className="font-extrabold text-base">Email</h2>
            <div />
          </div>
          <div className="space-y-4">
            {users.map((user) => (
              <ClassAttendeesCard key={user.id} user={user} />
            ))}
          </div>
        </div>
      ) : (
        <>
          <div className="flex items-center justify-between">
            <ClassTypeOnDetailPage type={classDetails?.Type__c} />
            {classDetails?.Level__c && (
              <div
                className={`flex items-center gap-1 px-3 py-1 rounded-md bg-color-yellow-3`}
                title="Level"
              >
                <Level />
                <span className="font-medium text-right w-full text-sm block">
                  {classDetails?.Level__c}
                </span>
              </div>
            )}
          </div>
          <h1 className="font-bold text-2xl md:text-3xl break-words">
            {classDetails?.Title__c}
          </h1>

          {/* Top Banner + Teacher Info */}
          <div className="flex flex-col md:flex-row md:items-center md:justify-between mt-4 gap-4">
            <div className="flex flex-row flex-wrap items-center gap-4 w-full">
              {(classDetails?.associatedTeachers || []).length > 0 && (
                <TeacherBanner teacherData={classDetails?.associatedTeachers} />
              )}
              <div
                className="min-h-[48px] h-auto border-l border-color-grey-2 mx-2 self-stretch"
                style={{ width: '2.5px' }}
              />
              {classDetails?.Genre__c && (
                <div
                  className="flex flex-col md:flex-row"
                  title={classDetails?.Genre__c}
                >
                  <span className="text-sm text-color-grey-1 truncate max-w-[150px] md:max-w-none">
                    {classDetails?.Genre__c ?? ''}
                  </span>
                </div>
              )}
              {teacherIds && teacherIds.length > 0 && (
                <>
                  <div
                    className="min-h-[48px] h-auto border-l border-color-grey-2 mx-2 self-stretch"
                    style={{ width: '2.5px' }}
                  />
                  <FollowFollowedButton
                    teacherIds={teacherIds}
                    isFollow={teacherIds.some((id: string) => followedMap[id])}
                  />
                </>
              )}
            </div>
          </div>

          {/* Meeting Info + Share */}
          <div className="flex flex-row items-start md:items-center justify-between gap-4">
            <div className="flex flex-col">
              <span className="text-sm font-medium">Next Meeting</span>
              <h2 className="font-semibold text-lg md:text-2xl">
                {nextMeetingDate || 'No upcoming meeting'}
              </h2>
            </div>
            <div className="flex items-center gap-3 flex-wrap">
              {(classDetails?.Type__c === 'Online' ||
                classDetails?.Type__c === 'Hybrid') &&
                classDetails?.Zoom_Meeting_Link__c &&
                isAlreadyEnrolled && (
                  <a
                    href={
                      classDetails?.Zoom_Meeting_Link__c.startsWith('http')
                        ? classDetails?.Zoom_Meeting_Link__c
                        : `https://${classDetails?.Zoom_Meeting_Link__c}`
                    }
                    target="_blank"
                    rel="noopener noreferrer"
                    className="h-12 flex items-center justify-center text-sm font-semibold bg-black text-white rounded-xl py-3 px-4 md:py-4 md:px-6 w-full md:w-auto text-center"
                  >
                    Join Zoom Meeting
                  </a>
                )}
              <Share onClick={handleShare} showBorder={true} />
            </div>
          </div>

          <ClassProgress
            completedCount={classDetails?.pastMeetings?.length || 0}
            totalCount={classDetails?.Total_Meetings_Count__c || 0}
          />

          {/* Hybrid / Online / In Person Info */}
          {classDetails?.Type__c === 'Hybrid' ? (
            <div className="flex flex-row items-stretch justify-between gap-5 w-full">
              <div className="bg-color-grey-10 rounded-2xl p-5 space-y-3 w-full flex items-center justify-between">
                <div className="space-y-3">
                  <h2 className="text-sm text-color-grey-12">In Person</h2>
                  <h3 className="text-sm font-bold break-words">
                    {classDetails?.Room__c ? (
                      <span>{classDetails.Room__c}</span>
                    ) : null}
                    {classDetails?.venue?.name ? (
                      <span>
                        {classDetails?.Room__c ? ', ' : ''}
                        {classDetails.venue.name}
                      </span>
                    ) : null}
                  </h3>
                </div>
                <button
                  className="ml-2 p-2 rounded hover:bg-gray-200 shrink-0"
                  onClick={() =>
                    showAddressModal({
                      title: '',
                      contentFn: (onClose) => (
                        <AddressModal
                          room={classDetails?.Room__c}
                          venueName={classDetails?.venue?.name}
                          address={classDetails?.venue?.address}
                          onClose={onClose}
                        />
                      ),
                      size: 'md',
                      rounded: '2xl',
                      padding: 0,
                      showCloseButton: false,
                    })
                  }
                  title="Show full address"
                >
                  <EyeIcon />
                </button>
              </div>
              <div className="bg-color-grey-10 rounded-2xl p-5 space-y-3 w-full">
                <h2 className="text-sm text-color-grey-12">Online</h2>
                <h3 className="text-sm font-bold break-words">
                  {classDetails?.Zoom_Room__c ?? 'N/A'}
                </h3>
              </div>
            </div>
          ) : (
            <div className="bg-color-grey-10 rounded-2xl p-5 space-y-3 flex items-center justify-between flex-row gap-3">
              <div>
                <h2 className="text-sm text-color-grey-12">
                  {classDetails?.Type__c === 'Online' ? 'Online' : 'In Person'}
                </h2>
                <h3 className="text-sm font-bold break-words">
                  {classDetails?.Type__c === 'Online' ? (
                    (classDetails?.Zoom_Room__c ?? 'N/A')
                  ) : (
                    <>
                      {classDetails?.Room__c && (
                        <span>{classDetails.Room__c}</span>
                      )}
                      {classDetails?.venue?.name && (
                        <span>
                          {classDetails?.Room__c ? ', ' : ''}
                          {classDetails.venue.name}
                        </span>
                      )}
                    </>
                  )}
                </h3>
              </div>
              {classDetails?.Type__c !== 'Online' && (
                <button
                  className="ml-2 p-2 rounded hover:bg-gray-200 shrink-0"
                  onClick={() =>
                    showAddressModal({
                      title: '',
                      contentFn: (onClose) => (
                        <AddressModal
                          room={classDetails?.Room__c}
                          venueName={classDetails?.venue?.name}
                          address={classDetails?.venue?.address}
                          onClose={onClose}
                        />
                      ),
                      size: 'md',
                      rounded: '2xl',
                      padding: 0,
                      showCloseButton: false,
                    })
                  }
                  title="Show full address"
                >
                  <EyeIcon />
                </button>
              )}
            </div>
          )}

          {addressModal}

          {/* Bottom Meta Info */}
          <div className="flex flex-wrap gap-3 mt-4 text-sm items-center justify-between">
            <div className="flex items-center gap-1">
              <Person />
              <span className="w-max">
                {classDetails?.Total_Seats__c - classDetails?.Booked_Seats__c}{' '}
                out of {classDetails?.Total_Seats__c ?? ''} seats
              </span>
            </div>
            <div className="flex items-center gap-1">
              <Class />
              <span className="w-max">
                {classDetails?.Total_Meetings_Count__c ?? ''} Meetings
              </span>
            </div>
            <div className="flex items-center gap-1">
              <CalendarIcon />
              <span className="w-max">
                {formatDateToAmerican(classDetails?.Start_Date__c)} -{' '}
                {formatDateToAmerican(classDetails?.End_Date__c)}
              </span>
            </div>
            <div className="flex items-center gap-1">
              <Clock />
              <span className="w-max">{classTime}</span>
            </div>
          </div>

          <ClassTabs
            classDetails={classDetails}
            userBadges={userBadges}
            tabs={tabs}
            viewType="student"
            hideButtonsInSchedule={isAlreadyEnrolled}
          />
        </>
      )}
    </div>
  )
}

export default ClassDetailPage
