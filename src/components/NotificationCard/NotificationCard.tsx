import Image from 'next/image'
import Link from 'next/link'
import React from 'react'
import RedDot from '../RedDot/RedDot'
import { getTimeAgo } from '@/utils/utils'

interface NotificationCardProps {
  notification: {
    Thumbnail_URL__c?: string
    Notification_Title__c: string
    Notification_Text__c: string
    Unread__c: boolean
    User__c: string
    Navigation_URL__c?: string
    CreatedDate: string
  }
  isRead: boolean
}

export const NotificationCard = ({
  notification,
  isRead,
}: NotificationCardProps) => {
  return (
    <Link href={notification?.Navigation_URL__c || '#'} className="block">
      <div className="flex items-center justify-between my-10">
        <div className="flex items-center w-full gap-3">
          {notification?.Thumbnail_URL__c && (
            <Image
              width={45}
              height={45}
              src={notification?.Thumbnail_URL__c}
              alt="Notification thumbnail"
              className="rounded-full w-12 h-12 object-cover"
            />
          )}
          <div className="flex items-center flex-col w-full">
            <div className="flex items-center w-full justify-between">
              <h1 className="font-medium text-sm text-color-black">
                {notification?.Notification_Title__c}
              </h1>
              <h4 className="text-color-grey-1 text-sm w-max">
                {getTimeAgo(notification?.CreatedDate)}
              </h4>
            </div>
            <div className="flex items-center w-full justify-between">
              <h4 className="text-sm text-color-grey-1">
                {notification?.Notification_Text__c}
              </h4>
              {notification?.Unread__c && !isRead && (
                <RedDot relative={false} />
              )}
            </div>
          </div>
        </div>
      </div>
    </Link>
  )
}
