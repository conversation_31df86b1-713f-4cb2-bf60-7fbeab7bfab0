import Facebook from '../Icons/Facebook'
import Globe from '../Icons/Globe'
import Instagram from '../Icons/Instagram'
import Substack from '../Icons/Substack'
import Twitter from '../Icons/Twitter'
import YoutubeSvg from '../Icons/YoutubeSvg'
import ProfileWithBadges from '../ProfileWithBadges/ProfileWithBadges'
import SocialLinks from '../SocialAccounts/SocialAccounts'
import { TagSection } from '../TagSection/TagSection'
import PublishedWorks from '../TeacherDetail/PublishedWorks'
import TeacherBio from '../TeacherDetail/TeacherBio'
import AuthorCTA from './AuthorCTA'

interface TeacherDetailProps {
  author: any
}

const AuthorDetail: React.FC<TeacherDetailProps> = ({ author }) => {
  const favoriteAuthorsArray = author?.data?.Favorite_Authors__c?.split(';')
  const favoriteGenresArray = author?.data?.Favorite_Genres__c?.split(';')

  const socialLinks = [
    {
      name: 'YouTube',
      icon: <YoutubeSvg />,
      url: author?.data?.YouTube__c,
    },
    {
      name: 'Facebook',
      icon: <Facebook />,
      url: author?.data?.Facebook__c,
    },
    {
      name: 'Instagram',
      icon: <Instagram />,
      url: author?.data?.Instagram__c,
    },
    {
      name: 'X',
      icon: <Twitter />,
      url: author?.data?.Twitter__c,
    },
    {
      name: 'Substack',
      icon: <Substack />,
      url: author?.data?.Substack__c,
    },
    {
      name: 'Website',
      icon: <Globe />,
      url: author?.data?.Website,
    },
  ]

  return (
    <div className="w-full flex flex-col items-center justify-center">
      <div className="w-full max-w-6xl p-6 flex flex-col xl:flex-row gap-10">
        <div className="flex-1 flex flex-col items-center md:items-start gap-6">
          <ProfileWithBadges author={author} />
          <TeacherBio teacher={author} />
        </div>

        <div className="xl:w-[568px] flex flex-col gap-8">
          <TagSection title="Favorite Authors" items={favoriteAuthorsArray} />
          <TagSection title="Favorite Genres" items={favoriteGenresArray} />
          <SocialLinks links={socialLinks} name={author?.data?.Name} />
          <PublishedWorks teacher={author} />
        </div>
      </div>
      <div className="p-6 w-full max-w-6xl">
        <AuthorCTA />
      </div>
    </div>
  )
}

export default AuthorDetail
