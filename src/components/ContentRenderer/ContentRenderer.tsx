import React from 'react'
import DynamicForm from '@/components/DynamicForm/DynamicForm'
import ClassListCarousel from '@/components/ClassListCarousel/ClassListCarousel'

interface FormShortcode {
  id: string
  version?: string
  theme?: string
  showTitle?: boolean
}

interface CarouselShortcode {
  id: string
  type: string
  limit?: number
  autoplay?: boolean
  arrows?: boolean
  dots?: boolean
  theme?: 'light' | 'dark'
}

interface ContentRendererProps {
  content: string
  formDataMap: Record<string, any> // Map of form IDs to their data
  classListDataMap: Record<string, any> // Map of class list IDs to their data
}

const ContentRenderer: React.FC<ContentRendererProps> = ({
  content,
  formDataMap,
  classListDataMap,
}) => {
  // DFS approach to find and replace form and carousel shortcodes
  const processContentWithShortcodes = (
    htmlContent: string,
  ): React.ReactNode[] => {
    const nodes: React.ReactNode[] = []

    // Decode HTML entities first
    const decodedContent = htmlContent
      .replace(/&quot;/g, '"')
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&#91;/g, '[')
      .replace(/&#93;/g, ']')

    // Find all shortcodes in the content
    const shortcodeMatches: Array<{
      match: string
      type: 'form' | 'carousel'
      id: string
      startIndex: number
      endIndex: number
      attributes: FormShortcode | CarouselShortcode
    }> = []

    // Find form shortcodes - simple and reliable pattern
    const formRegex = /\[form[^[\]]*\]/gi
    let match

    while ((match = formRegex.exec(decodedContent)) !== null) {
      const shortcodeText = match[0]
      const cleanShortcode = shortcodeText.replace(/<[^>]*>/g, '')
      const idMatch = cleanShortcode.match(/id\s*=\s*["']([^"']+)["']/i)

      if (idMatch) {
        const formId = idMatch[1]
        const attributes = parseFormShortcode(cleanShortcode)

        shortcodeMatches.push({
          match: shortcodeText,
          type: 'form',
          id: formId,
          startIndex: match.index,
          endIndex: match.index + shortcodeText.length,
          attributes,
        })
      }
    }

    // Reset regex lastIndex
    formRegex.lastIndex = 0

    // Find carousel shortcodes - simple and reliable pattern
    const carouselRegex = /\[events-carousel[^[\]]*\]/gi
    let carouselMatch

    while ((carouselMatch = carouselRegex.exec(decodedContent)) !== null) {
      const shortcodeText = carouselMatch[0]
      const cleanShortcode = shortcodeText.replace(/<[^>]*>/g, '')
      const idMatch = cleanShortcode.match(/id\s*=\s*["']([^"']+)["']/i)

      if (idMatch) {
        const carouselId = idMatch[1]
        const attributes = parseCarouselShortcode(cleanShortcode)

        shortcodeMatches.push({
          match: shortcodeText,
          type: 'carousel',
          id: carouselId,
          startIndex: carouselMatch.index,
          endIndex: carouselMatch.index + shortcodeText.length,
          attributes,
        })
      }
    }

    // Reset regex lastIndex
    carouselRegex.lastIndex = 0

    // Sort matches by start index (for proper content splitting)
    shortcodeMatches.sort((a, b) => a.startIndex - b.startIndex)

    let currentIndex = 0
    let nodeKey = 0

    // Process content segments and shortcodes
    shortcodeMatches.forEach((shortcodeMatch, index) => {
      // Add content before this shortcode
      if (shortcodeMatch.startIndex > currentIndex) {
        const beforeContent = decodedContent.slice(
          currentIndex,
          shortcodeMatch.startIndex,
        )
        if (beforeContent.trim()) {
          nodes.push(
            <div
              key={`content-${nodeKey++}`}
              dangerouslySetInnerHTML={{ __html: beforeContent }}
            />,
          )
        }
      }

      // Add the appropriate component
      let component: React.ReactNode = null

      if (shortcodeMatch.type === 'form') {
        component = renderFormById(
          shortcodeMatch.id,
          shortcodeMatch.attributes as FormShortcode,
          nodeKey++,
        )
      } else if (shortcodeMatch.type === 'carousel') {
        component = renderCarouselById(
          shortcodeMatch.id,
          shortcodeMatch.attributes as CarouselShortcode,
          nodeKey++,
        )
      }

      if (component) {
        nodes.push(component)
      }

      currentIndex = shortcodeMatch.endIndex
    })

    // Add remaining content after the last shortcode
    if (currentIndex < decodedContent.length) {
      const remainingContent = decodedContent.slice(currentIndex)
      if (remainingContent.trim()) {
        nodes.push(
          <div
            key={`content-${nodeKey++}`}
            dangerouslySetInnerHTML={{ __html: remainingContent }}
          />,
        )
      }
    }

    // If no shortcodes found, return original content
    if (shortcodeMatches.length === 0) {
      nodes.push(
        <div
          key="original-content"
          dangerouslySetInnerHTML={{ __html: decodedContent }}
        />,
      )
    }

    return nodes
  }

  // Parse shortcode attributes from full shortcode text
  const parseFormShortcode = (shortcodeText: string): FormShortcode => {
    const attributes: any = {}

    // Clean any HTML tags from the shortcode text first
    const cleanShortcode = shortcodeText.replace(/<[^>]*>/g, '')

    // Extract all attributes from the cleaned shortcode
    const attrRegex = /(\w+(?:-\w+)*)\s*=\s*["']([^"']+)["']/g
    let match

    while ((match = attrRegex.exec(cleanShortcode)) !== null) {
      const key = match[1]
      const value = match[2]

      switch (key.toLowerCase()) {
        case 'id':
          attributes.id = value
          break
        case 'version':
          attributes.version = value
          break
        case 'theme':
          attributes.theme = value
          break
        case 'show-title':
          attributes.showTitle = value === 'true'
          break
      }
    }

    return attributes
  }

  // Parse carousel shortcode attributes
  const parseCarouselShortcode = (shortcodeText: string): CarouselShortcode => {
    const attributes: any = {}

    // Clean any HTML tags from the shortcode text first
    const cleanShortcode = shortcodeText.replace(/<[^>]*>/g, '')

    // Extract all attributes from the cleaned shortcode
    const attrRegex = /(\w+(?:-\w+)*)\s*=\s*["']([^"']+)["']/g
    let match

    while ((match = attrRegex.exec(cleanShortcode)) !== null) {
      const key = match[1]
      const value = match[2]

      switch (key.toLowerCase()) {
        case 'id':
          attributes.id = value
          break
        case 'type':
          attributes.type = value
          break
        case 'limit':
          attributes.limit = parseInt(value) || 10
          break
        case 'autoplay':
          attributes.autoplay = value === 'true'
          break
        case 'arrows':
          attributes.arrows = value === 'true'
          break
        case 'dots':
          attributes.dots = value === 'true'
          break
        case 'theme':
          attributes.theme = value === 'light' ? 'light' : 'dark'
          break
      }
    }

    return attributes
  }

  // Render form component by ID
  const renderFormById = (
    formId: string,
    attributes: FormShortcode,
    key: number,
  ): React.ReactNode => {
    if (formDataMap[formId]) {
      const formData = formDataMap[formId]

      return (
        <div key={`form-${key}`} className="my-8">
          {attributes.showTitle && formData.Name && (
            <h2 className="text-2xl font-bold mb-4">{formData.Name}</h2>
          )}
          <div
            className={`form-container theme-${attributes.theme || 'light'}`}
          >
            <DynamicForm
              id={formId}
              data={JSON.parse(formData.Schema__c || '{}')}
            />
          </div>
        </div>
      )
    } else {
      return (
        <div
          key={`form-error-${key}`}
          className="my-8 p-4 bg-red-100 border border-red-400 rounded"
        >
          <p className="text-red-800">
            <strong>Form not found:</strong> {formId}
          </p>
          <p className="text-sm text-red-600 mt-1">
            Available forms: {Object.keys(formDataMap).join(', ') || 'None'}
          </p>
        </div>
      )
    }
  }

  // Render carousel component by ID
  const renderCarouselById = (
    carouselId: string,
    attributes: CarouselShortcode,
    key: number,
  ): React.ReactNode => {
    if (attributes.type === 'classes') {
      if (classListDataMap[carouselId]) {
        return (
          <div key={`carousel-${key}`} className="my-8">
            <ClassListCarousel
              classListData={classListDataMap[carouselId]}
              limit={attributes.limit}
              autoplay={attributes.autoplay}
              arrows={attributes.arrows}
              dots={attributes.dots}
              theme={attributes.theme}
            />
          </div>
        )
      }

      return (
        <div
          key={`carousel-error-${key}`}
          className="my-8 p-4 bg-red-100 border border-red-400 rounded"
        >
          <p className="text-red-800">
            <strong>Class list not found:</strong> {carouselId}
          </p>
        </div>
      )
    }

    return (
      <div
        key={`carousel-unsupported-${key}`}
        className="my-8 p-4 bg-yellow-100 border border-yellow-400 rounded"
      >
        <p className="text-yellow-800">
          <strong>Unsupported carousel type:</strong> {attributes.type}
        </p>
      </div>
    )
  }

  const renderContent = () => {
    const processedNodes = processContentWithShortcodes(content)

    // Add debug info if no shortcodes found
    if (
      content &&
      !content.includes('[form') &&
      !content.includes('[events-carousel')
    ) {
      // No shortcodes detected, but let's show what we have
      return (
        <>
          {processedNodes}
          {process.env.NODE_ENV === 'development' && (
            <div className="mt-4 p-2 bg-gray-100 border text-xs text-gray-600">
              <details>
                <summary>Debug: Content Analysis</summary>
                <pre>Raw content length: {content.length}</pre>
                <pre>Contains [form: {content.includes('[form')}</pre>
                <pre>
                  Contains [events-carousel:{' '}
                  {content.includes('[events-carousel')}
                </pre>
                <pre>Sample content: {content.slice(0, 200)}...</pre>
              </details>
            </div>
          )}
        </>
      )
    }

    return <>{processedNodes}</>
  }

  return <div className="content-renderer">{renderContent()}</div>
}

export default ContentRenderer
