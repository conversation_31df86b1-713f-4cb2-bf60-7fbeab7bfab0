import Image from 'next/image'
import CustomTooltip from '../CustomComponents/CustomTooltip/CustomTooltip'
interface ProfileWithStatsProps {
  author: any
}

const ProfileWithBadges: React.FC<ProfileWithStatsProps> = ({ author }) => {
  return (
    <div className="flex flex-col md:flex-row items-center gap-6">
      <div className="w-[249px] h-[249px] relative">
        {author?.data?.Photo__c ? (
          <Image
            src={author?.data?.Photo__c}
            alt="Profile Picture"
            fill
            className="rounded-full object-cover shadow"
          />
        ) : (
          <div className="bg-gray-300 rounded-full w-full h-full"></div>
        )}
      </div>

      <div className="flex flex-col gap-4">
        {author?.data?.userBadges
          ?.filter((badge: any) => badge?.badgeImage) // Only keep badges with an image
          ?.slice(0, 3)
          ?.map((badge: any, index: number) => (
            <StatCard
              key={badge.id ?? index}
              image={badge.badgeImage}
              label={badge.badgeName}
            />
          ))}
      </div>
    </div>
  )
}

export default ProfileWithBadges

type StatCardProps = {
  image: string
  label: string
}

const StatCard: React.FC<StatCardProps> = ({ image, label }) => (
  <div className="flex items-center gap-3 rounded-full w-16 h-16 relative">
    <CustomTooltip text={label} isVisible={true}>
      <Image
        src={image}
        alt="Badge"
        fill
        className="rounded-full object-cover"
      />
    </CustomTooltip>
  </div>
)
