import React, {
  useEffect,
  useRef,
  useState,
  ChangeEvent,
  KeyboardEvent,
} from 'react'
import CustomButton from '../CustomComponents/CustomButton/CustomButton'
import { toast } from 'react-toastify'

interface OtpInputProps {
  isLoading: boolean
  phone?: string
  email?: string
  setShowOtp: any
  setShowCaptcha: any
  showCaptcha: boolean
  length?: number
  onOtpSubmit?: (otp: string) => void
  handleOTP: any
  showOtpBtn?: boolean
}

const Otp: React.FC<OtpInputProps> = ({
  isLoading,
  phone,
  email,
  setShowOtp,
  setShowCaptcha,
  showCaptcha,
  length = 6,
  onOtpSubmit = () => {},
  handleOTP,
  showOtpBtn,
}) => {
  const [otp, setOtp] = useState<string[]>(new Array(length).fill(''))
  const inputRefs = useRef<HTMLInputElement[]>([])
  const [resentOtp, setResendOtp] = useState<boolean>(false)
  const [timer, setTimer] = useState<number>(30)

  const resentOTPhandle = (e: any) => {
    e.stopPropagation()
    if (showCaptcha) {
      toast.error('Please verify captcha.', { autoClose: 1000 })
      return
    }
    handleOTP()
    setResendOtp(false)
    setTimer(30)
  }

  useEffect(() => {
    let interval: NodeJS.Timeout
    if (!resentOtp && timer > 0) {
      interval = setInterval(() => {
        setTimer((prevTimer) => prevTimer - 1)
      }, 1000)
    } else if (timer === 0) {
      setResendOtp(true)
      setShowCaptcha(true)
    }

    return () => clearInterval(interval)
  }, [timer, resentOtp])

  useEffect(() => {
    if (inputRefs.current[0]) {
      inputRefs.current[0]?.focus()
    }
  }, [])

  const handleChange = (index: number, e: ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    if (isNaN(Number(value))) return

    const newOtp = [...otp]
    newOtp[index] = value.substring(value.length - 1)
    setOtp(newOtp)

    const combinedOtp = newOtp.join('')
    if (combinedOtp.length === length) onOtpSubmit(combinedOtp)

    if (value && index < length - 1 && inputRefs.current[index + 1]) {
      inputRefs.current[index + 1]?.focus()
    }
  }

  const handleClick = (index: number) => {
    inputRefs.current[index]?.setSelectionRange(1, 1)
    if (index > 0 && !otp[index - 1]) {
      inputRefs.current[otp.indexOf('')]?.focus()
    }
  }

  const handleKeyDown = (index: number, e: KeyboardEvent<HTMLInputElement>) => {
    if (
      e.key === 'Backspace' &&
      !otp[index] &&
      index > 0 &&
      inputRefs.current[index - 1]
    ) {
      inputRefs.current[index - 1]?.focus()
    }
  }

  const handlePaste = (e: React.ClipboardEvent<HTMLInputElement>) => {
    e.preventDefault()
    const pastedData = e.clipboardData.getData('text').trim()
    if (/^\d{6}$/.test(pastedData)) {
      const newOtp = pastedData.split('')
      setOtp(newOtp)
      inputRefs.current[length - 1]?.focus()
      onOtpSubmit(pastedData)
    } else {
      toast.error('Please paste a valid 6-digit OTP.', { autoClose: 1000 })
    }
  }

  return (
    <div className="">
      <h2 className="text-sm w-full mb-3">
        {`We have sent a 6-digit code to ${phone || email}`}
        <span
          className="mt-2 font-semibold text-color-blue-1 cursor-pointer"
          onClick={() => setShowOtp(false)}
        >
          {' '}
          (change)
        </span>
      </h2>
      <div
        className={`flex gap-5 ${showOtpBtn ? 'flex-col' : 'flex-row'} items-center`}
      >
        <div className="flex justify-around items-center gap-3">
          {otp.map((value, index) => (
            <input
              key={index}
              type="text"
              ref={(input) => {
                if (input) {
                  inputRefs.current[index] = input
                }
              }}
              value={value}
              onChange={(e) => handleChange(index, e)}
              onClick={() => handleClick(index)}
              onKeyDown={(e) => handleKeyDown(index, e)}
              onPaste={index === 0 ? handlePaste : undefined} // Attach paste handler to first input
              className="w-12 p-2 border border-gray-300 rounded-md text-center focus:outline-none focus:ring-1 focus:ring-black"
            />
          ))}
        </div>
        <div className={`${showOtpBtn ? 'w-full' : 'w-[200px]'}`}>
          {!showCaptcha && (
            <CustomButton
              title={'Verify'}
              onClick={() => {
                onOtpSubmit(otp.join(''))
              }}
              isLoading={isLoading}
            />
          )}
        </div>
      </div>
      <div className="flex items-center justify-center mt-3">
        {resentOtp ? (
          <h2 className="text-sm font-semibold">
            {"Didn't receive OTP ?"}
            <span
              className="text-color-blue-1 cursor-pointer"
              onClick={resentOTPhandle}
            >
              {' '}
              Resend
            </span>
          </h2>
        ) : (
          <span className="font-semibold text-xs text-color-grey-1">
            Resend OTP in {timer} seconds
          </span>
        )}
      </div>
    </div>
  )
}

export default Otp
