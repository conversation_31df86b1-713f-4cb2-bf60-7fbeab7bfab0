'use client'
import { useAuth } from '@/contexts/AuthContext'
import { readNotifications } from '@/lib/actions/notification.actions'
import React from 'react'
import { toast } from 'react-toastify'

const MarkNotifications = ({
  setAllNotificationsMarked,
}: {
  setAllNotificationsMarked: React.Dispatch<React.SetStateAction<boolean>>
}) => {
  const { user } = useAuth()
  const [isLoading, setIsLoading] = React.useState(false)
  const handleMarkNotifications = async () => {
    try {
      setIsLoading(true)
      await readNotifications(user?.id)
      toast.success('All notifications marked as read!', { autoClose: 1500 })
      setAllNotificationsMarked(true)
    } catch (error) {
      toast.error('Something went wrong!' + error, { autoClose: 1500 })
    } finally {
      setIsLoading(false)
    }
  }
  return (
    <div>
      {isLoading ? (
        <span className="font-medium text-gray-400 animate-shimmer">
          Mark all as read
        </span>
      ) : (
        <span
          className="font-medium cursor-pointer"
          onClick={handleMarkNotifications}
        >
          Mark all as read
        </span>
      )}
    </div>
  )
}

export default MarkNotifications
