'use client'
import React from 'react'

const DonationInfo = () => {
  return (
    <div className="p-6 bg-white rounded-2xl my-5 shadow-sm space-y-6">
      <h2 className="text-lg font-bold">
        Your donation will Support our programs
      </h2>

      <div className="space-y-4 text-gray-700 text-sm">
        <p>
          {`Since its start in 2005, The Muse has grown into one of the nation's
          largest community-based literary centers, serving thousands of adults
          and young persons in coastal Virginia and beyond, thanks in large part
          to the support of donors like you.`}
        </p>

        <p>
          {`The Muse's purpose ahead of writing centers is that nobody who wants
          to experience our lively, diverse menu of offerings is turned away,
          regardless of ability to pay.`}
        </p>

        <p>
          {`In addition to helping many persons establish a writing life—with both
          creative goals and practical tools—we now, with expanded virtual
          programming across the world, The Muse's community outreach programs
          serve young writers, The writers' and veteran community, and diverse
          populations as well as those dealing with challenges such as domestic
          trauma, loneliness, disability, homelessness, severe illness,
          bereavement, and loss.`}
        </p>

        <p>
          {`While The Muse experiences felt and lived, it is also an organization
          that requires practical and tangible resources to propel it.`}
        </p>

        <p>
          {`Many donors choose to make a fully tax-deductible gift to support The
          Muse and cement it as a center of creativity.`}
        </p>
      </div>

      <h2 className="text-lg font-bold mt-8">More ways to give</h2>

      <div className="space-y-6">
        <div>
          <h3 className="font-bold text-sm">Give and Become a Member</h3>
          <p className="text-gray-700 text-sm">
            {`Get an immediate return from a wide variety of benefits, including
            discounts on Muse classes, as well as discounts to local businesses.
            Make a one-time donation or split your donation into monthly
            increments. Learn more.`}
          </p>
        </div>

        <div>
          <h3 className="font-bold text-sm">
            {`Give the Gift of Stocks, Bonds or Mutual Funds`}
          </h3>
          <p className="text-gray-700 text-sm">
            {`One of the most financially sound ways to give is through the
            transfer of stock. Donors have the capacity to transfer stock, bonds
            or other securities to be transferred to The Muse. Giving a gift of
            appreciated stock has significant tax benefits for the contributor.
            Please consult a tax advisor for information related to your
            personal circumstances.`}
          </p>
        </div>

        <div>
          <h3 className="font-bold text-sm">
            {`Qualified Charitable Distributions from IRAs`}
          </h3>
          <p className="text-gray-700 text-sm">
            {`Individuals who are age 70½ or older can contribute up to $100,000
            from their IRA directly to a charity and avoid paying income taxes
            on the distribution. This is known as a qualified charitable
            distribution. It is limited to IRAs, and there are other excluded
            retirement accounts. Please consult a tax advisor for information
            related to your personal circumstances.`}
          </p>
        </div>

        <div>
          <h3 className="font-bold text-sm">Planned Giving</h3>
          <p className="text-gray-700 text-sm">
            {`Planned giving and estate planning allow you to create lasting
            support for your causes going forward for The Muse. By including The
            Muse in your estate plan, you make a strong statement confirming
            that our programming reflects your personal ability to make an
            impact and mission!`}
          </p>
        </div>
      </div>

      <div className="mt-8 space-y-4 text-gray-600 text-sm">
        <p>Thank you for your support of The Muse Writers Center!</p>
        <p>
          {`Please contact us if you have any questions or wish to discuss your
          options further.`}
        </p>
        <p>
          {`Financial statements are available upon written request from the
          Office of Charitable and Regulatory Programs.`}
        </p>
      </div>
    </div>
  )
}

export default DonationInfo
