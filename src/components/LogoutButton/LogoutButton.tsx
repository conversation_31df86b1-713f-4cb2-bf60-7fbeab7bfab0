'use client'
import useModal from '@/hooks/useModal'
import React from 'react'
import LogoutModal from '../LogoutModal/LogoutModal'

const LogoutButton = () => {
  const [modal, showModal] = useModal()
  const handleLogout = () => {
    showModal({
      title: '',
      contentFn: (onClose) => <LogoutModal onClose={onClose} />,
      closeOnClickOutside: true,
      size: 'md',
      showCloseButton: false,
    })
  }
  return (
    <div className="w-full">
      <button
        className="h-12 w-full border-color-grey-2 border rounded-xl"
        onClick={handleLogout}
      >
        Logout
      </button>
      {modal}
    </div>
  )
}

export default LogoutButton
