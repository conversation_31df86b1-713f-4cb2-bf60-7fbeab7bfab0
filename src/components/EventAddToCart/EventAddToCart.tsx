'use client'
import React, { useEffect, useState } from 'react'
import CustomButton from '../CustomComponents/CustomButton/CustomButton'
import { addToCart } from '@/lib/actions/cart.actions'
import { toast } from 'react-toastify'
import { useRouter } from 'next/navigation'
import { useApi } from '@/hooks/useApi'

const EventAddToCart = ({ eventId }: { eventId: string }) => {
  const [isAdded, setIsAdded] = useState<boolean>(false)
  const router = useRouter()
  const [response, addCart] = useApi((access: string) =>
    addToCart(access, eventId),
  )

  useEffect(() => {
    if (response.isSuccess) {
      setIsAdded(true)
      toast.success('Successfully added!', { autoClose: 1500 })
    }
  }, [response])

  return (
    <div className="mt-6">
      {isAdded ? (
        <CustomButton
          title={'Go to cart'}
          isLoading={false}
          onClick={() => router.push('/cart')}
        />
      ) : (
        <CustomButton
          title={'Add to cart'}
          isLoading={response.isFetching}
          onClick={addCart}
        />
      )}
    </div>
  )
}

export default EventAddToCart
