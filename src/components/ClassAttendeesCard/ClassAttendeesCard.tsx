import React from 'react'
import CopyIcon from '../Icons/CopyIcon'
import GreenTick from '../Icons/GreenTick'

const ClassAttendeesCard = ({ user }: { user: any }) => {
  const [copied, setCopied] = React.useState(false)
  const handleCopy = (text: string) => {
    if (!text) return
    navigator.clipboard.writeText(text)
    setCopied(true)
    setTimeout(() => {
      setCopied(false)
    }, 2000)
  }
  return (
    <div
      className={`grid grid-cols-[1fr,1.5fr,48px] gap-4 text-sm font-semibold border-b-2 border-color-grey pb-2 items-center rounded-lg transition-colors`}
    >
      <span className="text-color-grey-4">{user.name}</span>
      <span className="text-color-grey-4">{user.email ?? '-'}</span>
      <button
        onClick={() => handleCopy(`${user.email}`)}
        className="w-8 h-8 flex items-center justify-center text-gray-400 hover:text-gray-600 transition-colors"
        aria-label="Copy user information"
      >
        {user.email && (
          <>{copied ? <GreenTick color="#DFDFDF" /> : <CopyIcon />}</>
        )}
      </button>
    </div>
  )
}

export default ClassAttendeesCard
