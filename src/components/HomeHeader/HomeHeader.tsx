'use client'
import LogoIcon from '@/assets/icons/LogoIcon'
import Link from 'next/link'
import React from 'react'
import Hamburger from '../Icons/Hamburger'
import NavbarComponent from '../NavbarComponent/NavbarComponent'
import { usePathname } from 'next/navigation'
import { isHeaderFooterSidebarPath } from '@/utils/utils'
import Notification from '../Notification/Notification'
import UserProfile from '../UserProfile/UserProfile'
const HomeHeader = ({
  headerData,
  role,
  userData,
}: {
  headerData: any
  role: any
  userData: any
}) => {
  const pathname = usePathname()
  if (isHeaderFooterSidebarPath(pathname)) return null

  return (
    <header className="bg-color-cream w-full mx-auto sticky top-0 z-50">
      <div className="md:px-16 px-6 py-5 flex items-center justify-between">
        {/* Mobile Hamburger */}
        <div className="block xl:hidden">
          <Hamburger data={headerData} role={role} />
        </div>

        {/* Logo */}
        <Link href="/" className="flex-shrink-0 hidden xl:block">
          <LogoIcon height={50} width={100} />
        </Link>

        <div className="flex font-semibold text-lg items-center rounded-[40px] home-buttons bg-color-cream-1 block xl:hidden">
          {role ? (
            <Link href={'/dashboard'}>
              <div className="h-full px-7 py-4 w-full rounded-[40px] cursor-pointer hover:bg-color-yellow-1">
                <h1 className="hover:font-bold w-max">Dashboard</h1>
              </div>
            </Link>
          ) : (
            <Link href={'/sign-in'} className="rounded-[40px]">
              <div className="h-full px-7 py-4 w-full rounded-[40px] cursor-pointer hover:bg-color-yellow-1">
                <h1 className="hover:font-bold w-max">Sign in</h1>
              </div>
            </Link>
          )}
          <Link href={'/donations'}>
            <div className="h-full px-7 py-4 w-full rounded-[40px] cursor-pointer bg-color-yellow-1 hover:bg-color-yellow-1">
              <h1 className="hover:font-bold w-max">Donate</h1>
            </div>
          </Link>
        </div>

        {/* Desktop Navbar */}
        <div className="hidden xl:flex w-3/4 items-center justify-between">
          <NavbarComponent navData={headerData} />
          <div className="flex items-center justify-center gap-6">
            <Notification />
            {userData && <UserProfile userData={userData} h={50} w={50} />}
            <div className="flex font-semibold text-lg items-center rounded-[40px] home-buttons bg-color-cream-1">
              {role ? (
                <Link href={'/dashboard'}>
                  <div className="h-full px-7 py-4 w-full rounded-[40px] cursor-pointer hover:bg-color-yellow-1">
                    <h1 className="hover:font-bold w-max">Dashboard</h1>
                  </div>
                </Link>
              ) : (
                <Link href={'/sign-in'} className="rounded-[40px]">
                  <div className="h-full px-7 py-4 w-full rounded-[40px] cursor-pointer hover:bg-color-yellow-1">
                    <h1 className="hover:font-bold w-max">Sign in</h1>
                  </div>
                </Link>
              )}
              <Link href={'/donations'}>
                <div className="h-full px-7 py-4 w-full rounded-[40px] cursor-pointer bg-color-yellow-1 hover:bg-color-yellow-1">
                  <h1 className="hover:font-bold w-max">Donate</h1>
                </div>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </header>
  )
}

export default HomeHeader
