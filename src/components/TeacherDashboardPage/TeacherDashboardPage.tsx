import React from 'react'
import <PERSON>Header from '../PageHeader/PageHeader'
import { handleLoggedIn } from '@/lib/actions/login.actions'
import Image from 'next/image'
import Link from 'next/link'
import WebsiteGlobalcon from '../Icons/WebsiteGlobalcon'
import InstagramSVG from '../Icons/InstagramSVG'
import LinkedinSVG from '../Icons/LinkedinSVG'
import TwitterSVG from '../Icons/TwitterSVG'
import Genre from '../Icons/Genre'
import Class from '../Icons/Class'
import TeacherClassPage from '../TeacherClassPage/TeacherClassPage'

const TeacherDashboardPage = async () => {
  const userData: any = await handleLoggedIn()
  return (
    <div className="w-full h-screen bg-white">
      {userData && (
        <PageHeader
          showTittle={false}
          showSearch={false}
          showCart={false}
          showMessage={false}
          userData={userData}
        />
      )}
      <div className="w-full flex flex-col items-center justify-center bg-white">
        <div className="w-full items-center flex justify-center border-t-2 border-color-grey">
          <div className="w-full md:w-6/12">
            <div className="border-color-grey-2 border p-7 mt-3 rounded-2xl flex items-center gap-5 flex-col md:flex-row ">
              <div className="flex w-full md:w-max items-center gap-3">
                {userData?.photo && (
                  <Image
                    width={45}
                    height={45}
                    src={userData?.photo}
                    alt="User avatar"
                    className="w-28 md:w-52 h-28 md:h-52 object-cover rounded-full md:rounded-2xl"
                  />
                )}
                <div className="block md:hidden">
                  <h1 className="w-max text-xl font-extrabold">
                    {userData?.name}
                  </h1>
                </div>
              </div>
              <div className="w-full flex flex-col justify-center">
                <div className="flex justify-between w-full">
                  <h1 className="w-max text-2xl font-bold hidden md:block">
                    {userData?.name}
                  </h1>
                  <div className="flex items-center gap-2 mt-3">
                    {userData?.website && (
                      <Link
                        href={
                          userData?.website?.startsWith('http') ||
                          userData?.website?.startsWith('www')
                            ? userData?.website
                            : `https://${userData?.website}`
                        }
                        target="_blank"
                      >
                        {WebsiteGlobalcon()}
                      </Link>
                    )}
                    {userData?.instagram && (
                      <Link
                        href={
                          userData?.instagram?.startsWith('http') ||
                          userData?.instagram?.startsWith('www')
                            ? userData?.instagram
                            : `https://${userData?.instagram}`
                        }
                        target="_blank"
                      >
                        {InstagramSVG()}
                      </Link>
                    )}
                    {userData?.facebook && (
                      <Link
                        href={
                          userData?.facebook?.startsWith('http') ||
                          userData?.facebook?.startsWith('www')
                            ? userData?.facebook
                            : `https://${userData?.facebook}`
                        }
                        target="_blank"
                      >
                        {LinkedinSVG()}
                      </Link>
                    )}
                    {userData?.twitter && (
                      <Link
                        href={
                          userData?.twitter?.startsWith('http') ||
                          userData?.twitter?.startsWith('www')
                            ? userData?.twitter
                            : `https://${userData?.twitter}`
                        }
                        target="_blank"
                      >
                        {TwitterSVG()}
                      </Link>
                    )}
                  </div>
                </div>
                <p className="font-medium text-sm mt-3">{userData?.bio}</p>
                <div className="w-full flex items-center mt-3 gap-5">
                  <div className="flex items-center gap-2">
                    <Genre />
                    <span className="font-medium text-sm">
                      {userData?.genre}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Class />
                    <span className="font-medium text-sm">
                      {userData?.totalClass <= 1
                        ? userData?.totalClass + ' Class'
                        : userData?.totalClass + ' Classes'}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="w-full mt-5 flex justify-center">
          <TeacherClassPage classType="Active" />
        </div>
      </div>
    </div>
  )
}

export default TeacherDashboardPage
