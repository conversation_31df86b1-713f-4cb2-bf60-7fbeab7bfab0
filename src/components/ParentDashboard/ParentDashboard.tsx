import { ROLES } from '@/types/enum'
import DonorBanner from '../AdultStudentDashboard/DonorCard'
import PageHeader from '../PageHeader/PageHeader'
import QuotesCard from '../QuotesCard/QuotesCard'
import RightColumnHoc from '../RightColumnHoc/RightColumnHoc'
import MyKidsSection from './MyKidsSectionCard'
import ParentDashboardEvents from './ParentDashboardEvents'
import RegistrationRequests from './RegistrationRequests'

const ParentDashboard = ({ userData }: { userData: any }) => {
  return (
    <div className="flex flex-col sm:flex-row w-full">
      <div className="w-full flex-1">
        {userData && (
          <PageHeader
            heading={`Hey, ${userData?.name}!`}
            backgroundColor="#F5F5F7"
            showMessage={false}
            showSearch={false}
            showCart={userData?.role != ROLES.Teacher}
            userData={userData}
          />
        )}
        <div className="flex flex-col gap-5 p-3 md:pr-0">
          <QuotesCard type="Parent" />
          <div className="flex flex-col items-center gap-3">
            <RegistrationRequests />
          </div>
          <MyKidsSection />
          <DonorBanner isDonor={userData?.roles?.includes('Donor')} />
        </div>
      </div>
      <div className="md:w-96 p-3 md:shrink-0">
        <RightColumnHoc>
          <ParentDashboardEvents />
        </RightColumnHoc>
      </div>
    </div>
  )
}

export default ParentDashboard
