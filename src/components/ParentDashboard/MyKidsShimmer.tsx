import React from 'react'

const ProfileCardShimmer = () => {
  return (
    <div className="animate-pulse bg-white p-4 rounded-2xl shadow-md flex justify-between items-center w-full mx-auto gap-2">
      <div className="flex gap-4 items-start">
        <div className="w-16 h-16 rounded-full bg-gray-200" />
        <div className="flex flex-col gap-2">
          <div className="h-5 w-40 bg-gray-200 rounded" />
          <div className="h-4 w-32 bg-gray-200 rounded" />
          <div className="mt-4">
            <div className="h-4 w-24 bg-gray-200 mb-1 rounded" />
            <div className="h-4 w-64 bg-gray-200 mb-1 rounded" />
            <div className="h-4 w-40 bg-gray-200 rounded" />
          </div>
        </div>
      </div>
      <div className="flex gap-6 hidden lg:flex">
        {[1, 2, 3].map((_, idx) => (
          <div key={idx} className="flex flex-col items-center">
            <div className="h-6 w-6 bg-gray-200 rounded mb-1" />
            <div className="h-5 w-20 bg-gray-200 rounded" />
          </div>
        ))}
      </div>
    </div>
  )
}

export default ProfileCardShimmer
