'use client'
import useModal from '@/hooks/useModal'
import { useGetMyKidSectionQuery } from '@/lib/redux/slices/apiSlice/apiSlice'
import { getNameLetter } from '@/utils/utils'
import Image from 'next/image'
import Link from 'next/link'
import AddChildModal from './AddChildModal'
import ProfileCardShimmer from './MyKidsShimmer'

interface KidCardProps {
  Id: string
  Name: string
  Photo__c: string
  tagline: string
  stats: any
}

const KidCard: React.FC<KidCardProps> = ({
  Id,
  Name,
  Photo__c,
  tagline,
  stats,
}) => {
  return (
    <Link href={`/my-kids/${Id}`}>
      <div className={`rounded-xl p-4 mb-4 bg-neutral-white w-full`}>
        <div className="flex flex-col xl:flex-row items-center gap-4">
          <div className="flex items-center gap-4 w-full">
            <div className="h-20 w-20 relative shrink-0">
              {Photo__c ? (
                <Image
                  src={Photo__c}
                  alt="Profile Picture"
                  fill
                  className="rounded-full object-cover shadow"
                />
              ) : (
                <div className="rounded-full bg-gray-300 text-white flex items-center justify-center font-bold text-base w-full h-full">
                  <span className="text-2xl font-bold">
                    {getNameLetter(Name)}
                  </span>
                </div>
              )}
            </div>
            <div className="flex flex-1 flex-start w-full">
              <p className="text-lg sm:text-xl font-bold text-color-blue-6">
                {Name}
              </p>
              <p className="font-semibold text-sm leading-6 text-color-grey-1">
                {tagline}
              </p>
            </div>
          </div>

          <div className="flex justify-start xl:justify-end gap-6 text-center text-sm text-color-black w-full">
            <div className="flex flex-col gap-1">
              <p className="font-bold text-2xl leading-none">
                {stats?.ongoingClasses}
              </p>
              <p className="font-semibold text-xs leading-none align-bottom text-color-grey-1">
                Ongoing Classes
              </p>
            </div>
            <div className="flex flex-col gap-1">
              <p className="font-bold text-2xl leading-none">
                {stats?.classesAttended}
              </p>
              <p className="font-semibold text-xs leading-none align-bottom text-color-grey-1">
                Classes Taken
              </p>
            </div>
            <div className="flex flex-col gap-1">
              <p className="font-bold text-2xl leading-none">
                {stats?.badgesEarned}
              </p>
              <p className="font-semibold text-xs leading-none align-bottom text-color-grey-1">
                Badges
              </p>
            </div>
          </div>
        </div>

        {/* Next Class Section */}
        {/* {nextClassTitle && nextClassDate && (
        <div className="mt-4 flex flex-col gap-2">
          <p className="font-normal text-sm leading-none text-color-grey-1">
            Next Class
          </p>
          <p className="font-bold text-base leading-6 text-color-black">
            {nextClassTitle}
          </p>
          <div className="flex items-center gap-1 font-medium text-sm leading-none align-middle">
            <CalendarIcon />
            <span>{nextClassDate}</span>
          </div>
        </div>
      )} */}
      </div>
    </Link>
  )
}

const MyKidsSection = () => {
  const [modal, showModal] = useModal()
  const handleModal = () => {
    showModal({
      title: ``,
      contentFn: (onClose) => <AddChildModal onClose={onClose} />,
      closeOnClickOutside: true,
      size: 'lg',
      rounded: '2xl',
      padding: 0,
      showCloseButton: false,
    })
  }

  const { data, isFetching } = useGetMyKidSectionQuery({
    isTokenRequired: true,
  })

  const myKids = data?.data

  return (
    <div>
      <div className="mb-4 flex justify-between items-center">
        <h2 className="text-2xl font-medium leading-[1.5] tracking-normal align-middle text-color-black">
          My Kids
        </h2>
        <span
          className="font-normal text-xs cursor-pointer"
          onClick={() => handleModal()}
        >
          Add Child
        </span>
      </div>

      {isFetching ? (
        <div className="flex flex-col gap-4">
          {[1, 2, 3].map((item) => (
            <ProfileCardShimmer key={item} />
          ))}
        </div>
      ) : myKids?.length == 0 ? (
        <div className="text-sm text-color-grey-1 text-center py-4 font-medium">
          No kids
        </div>
      ) : (
        myKids?.map((kid: any) => <KidCard key={kid.Id} {...kid} />)
      )}

      {modal}
    </div>
  )
}

export default MyKidsSection
