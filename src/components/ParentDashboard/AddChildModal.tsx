'use client'
import { useEffect, useState } from 'react'
import CustomButton from '../CustomComponents/CustomButton/CustomButton'
import { X } from 'lucide-react'
import PhoneInput from 'react-phone-input-2'
import 'react-phone-input-2/lib/style.css'
import { useAddYouthAccountMutation } from '@/lib/redux/slices/apiSlice/apiSlice'
import { toast } from 'react-toastify'
import ProfileUploadYouth from '../ProfileUploadYouth/ProfileUploadYouth'
import { addPhotoToS3Bucket } from '@/utils/utils'

const AddChildModal = ({ onClose }: { onClose: () => void }) => {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    dateOfBirth: '',
    email: '',
    phone: '',
    photoUrl: '',
  })

  const [file, setFile] = useState<File | null>(null)
  const [isUploadingImage, setIsUploadingImage] = useState<boolean>(false)

  const handleChange = (e: any) => {
    setFormData({ ...formData, [e.target.name]: e.target.value })
  }

  const [addYouthAccount, { isLoading, isSuccess }] =
    useAddYouthAccountMutation()

  const handleSubmit = async (e: any) => {
    e.preventDefault()
    const today = new Date()
    const birthDate = new Date(formData.dateOfBirth)
    const age = today.getFullYear() - birthDate.getFullYear()
    const monthDiff = today.getMonth() - birthDate.getMonth()
    const dayDiff = today.getDate() - birthDate.getDate()

    const is18OrMore =
      age > 18 ||
      (age === 18 && (monthDiff > 0 || (monthDiff === 0 && dayDiff >= 0)))

    if (is18OrMore) {
      toast.error('Child must be below 18 years old', { autoClose: 3000 })
      return
    }
    let imageUrl = ''
    if (file) {
      setIsUploadingImage(true)
      imageUrl = (await addPhotoToS3Bucket(file)) || ''
      setIsUploadingImage(false)
    }

    const res = await addYouthAccount({
      isTokenRequired: true,
      firstName: formData.firstName,
      lastName: formData.lastName,
      dateOfBirth: formData.dateOfBirth,
      email: formData.email,
      phone: formData.phone,
      photoUrl: imageUrl,
    })
  }

  useEffect(() => {
    if (isSuccess) {
      toast.success('Child added successfully', { autoClose: 2000 })
      setFormData({
        firstName: '',
        lastName: '',
        dateOfBirth: '',
        email: '',
        phone: '',
        photoUrl: '',
      }) // Reset form
      onClose()
    }
  }, [isSuccess])

  return (
    <div>
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-3">
        <div className="bg-white rounded-2xl shadow-xl w-full max-w-md p-6">
          {/* Header */}
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-bold">Add Child</h2>
            <button
              onClick={onClose}
              className="p-1 hover:bg-color-grey-5 rounded-full transition-colors"
            >
              <X size={20} className="text-color-grey-1" />
            </button>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit} className="space-y-4">
            <ProfileUploadYouth setSelectedFile={setFile} />
            <div>
              <label className="block text-sm font-medium text-gray-700">
                First Name
              </label>
              <input
                name="firstName"
                type="text"
                value={formData.firstName}
                onChange={handleChange}
                required
                className="mt-1 w-full rounded-lg border border-gray-300 p-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Last Name
              </label>
              <input
                name="lastName"
                type="text"
                value={formData.lastName}
                onChange={handleChange}
                required
                className="mt-1 w-full rounded-lg border border-gray-300 p-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">
                Email
              </label>
              <input
                name="email"
                type="email"
                value={formData.email}
                onChange={handleChange}
                required
                className="mt-1 w-full rounded-lg border border-gray-300 p-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">
                Phone Number
              </label>
              <PhoneInput
                country={'us'}
                value={formData.phone}
                onChange={(_, __, ___, formattedValue) => {
                  setFormData((prev) => ({
                    ...prev,
                    phone: formattedValue.replace(/\s/g, ''),
                  }))
                }}
                inputProps={{
                  name: 'parentPhoneNumber',
                  required: true,
                  className: 'w-full p-2 border border-gray-300 rounded pl-12',
                }}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Date Of Birth
              </label>
              <input
                name="dateOfBirth"
                type="date"
                value={formData.dateOfBirth}
                onChange={handleChange}
                required
                className="mt-1 w-full rounded-lg border border-gray-300 p-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* Actions */}
            <div className="flex justify-end gap-3 pt-2">
              <CustomButton
                title="Cancel"
                onClick={onClose}
                isLoading={false}
                height={10}
                width="222px"
                backgroundColor="bg-white"
                classes="text-gray-700 border border-gray-200 hover:bg-gray-50"
                isDisabled={false}
              />
              <CustomButton
                title="Add Child"
                onClick={undefined}
                isLoading={isUploadingImage || isLoading}
                height={10}
                width="w-28"
                backgroundColor="bg-color-yellow"
                classes="text-color-black"
                isDisabled={false}
                type="submit"
              />
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}

export default AddChildModal
