'use client'

import { Dayjs } from 'dayjs'
import PrevNextButton from '../PrevNextButton/PrevNextButton'

type DatePickerMonthProps = {
  currentMonth: Dayjs
  setCurrentMonth: (month: Dayjs) => void
  selectedDate: Dayjs
  setSelectedDate: (date: Dayjs) => void
  highlightedDates: number[]
  isLoading: boolean
}

const DatePickerMonth = ({
  currentMonth,
  setCurrentMonth,
  selectedDate,
  setSelectedDate,
  highlightedDates,
  isLoading,
}: DatePickerMonthProps) => {
  const daysOfWeek = ['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa']

  const startDay = currentMonth.startOf('month').day()
  const daysInMonth = currentMonth.daysInMonth()

  const handlePrevMonth = () =>
    setCurrentMonth(currentMonth.subtract(1, 'month'))
  const handleNextMonth = () => setCurrentMonth(currentMonth.add(1, 'month'))

  const handleDateClick = (day: number) => {
    setSelectedDate(currentMonth.date(day))
  }

  const renderShimmerGrid = () => (
    <div className="grid grid-cols-7 text-center gap-y-1 text-sm font-medium text-black animate-pulse">
      {[...Array(startDay)]?.map((_, i) => <div key={`empty-${i}`}></div>)}
      {[...Array(daysInMonth)]?.map((_, i) => (
        <div key={i} className="w-9 h-9 bg-gray-300 rounded-full mx-auto"></div>
      ))}
    </div>
  )

  return (
    <div className="p-4 border-2 rounded-xl bg-neutral text-color-black">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-bold">
          {currentMonth?.format('MMMM YYYY')}
        </h2>
        <div>
          <PrevNextButton
            onPrev={handlePrevMonth}
            onNext={handleNextMonth}
            showBorder={false}
            notDisable={true}
            arrorwColor="#04A4F4"
          />
        </div>
      </div>

      <div className="grid grid-cols-7 text-center text-sm font-medium text-gray-600 mb-1">
        {daysOfWeek?.map((day) => <div key={day}>{day}</div>)}
      </div>

      {isLoading ? (
        renderShimmerGrid()
      ) : (
        <div className="grid grid-cols-7 text-center gap-y-1 text-sm font-medium text-black">
          {[...Array(startDay)]?.map((_, i) => <div key={`empty-${i}`}></div>)}
          {[...Array(daysInMonth)]?.map((_, i) => {
            const day = i + 1
            const isSelected =
              selectedDate?.date() === day &&
              selectedDate?.isSame(currentMonth, 'month')
            const isHighlighted = highlightedDates?.includes(day)
            return (
              <div
                key={day}
                onClick={() => handleDateClick(day)}
                className={`cursor-pointer p-2 w-full h-full flex flex-col items-center justify-center rounded-full relative
                ${
                  isSelected
                    ? 'border-2 border-color-blue-1 text-black font-semibold bg-white'
                    : 'hover:bg-blue-100 text-gray-800'
                }`}
              >
                <div>{day}</div>
                <div>
                  {isHighlighted && !isSelected && (
                    <div className="w-[7px] h-[7px] mt-0.5 bg-color-blue-1 rounded-full"></div>
                  )}
                </div>
              </div>
            )
          })}
        </div>
      )}
      <p className="text-sm text-gray-700 mt-4">
        Selected: {selectedDate?.format('M/D/YYYY')}
      </p>
    </div>
  )
}

export default DatePickerMonth
