import React from 'react'
import PricingToggle from '../Subscription/PricingToggle'
import { Fascinate } from 'next/font/google'

const MembershipPlanOverviewTab = () => {
  return (
    <div className="space-y-6">
      {/* Current Plan Card */}
      <div className="border rounded-xl py-4 px-6 flex flex-col md:flex-row md:items-start md:justify-between gap-6 bg-white">
        <div className="flex-1 min-w-[220px]">
          <div className="text-sm mb-1">Current Plan</div>
          <div className="flex items-center gap-2 mb-1">
            <span className="text-2xl font-bold">Ally</span>
            <span className="bg-green-100 text-color-grey-14  text-[10px] font-bold px-2 py-1 rounded-lg">
              Active
            </span>
          </div>
          <div className="text-color-grey-15 text-sm mb-4">
            5% discount on all classes at The Muse, Author page, invitation to
            Muse events
          </div>
          <div className="flex gap-8 text-xs mt-2">
            <div>
              <div className="text-color-grey-15 text-xs">
                Next Billing Date
              </div>
              <div className="font-bold text-sm">Aug 17, 2025</div>
            </div>
            <div>
              <div className="text-color-grey-15 text-xs">Payment Method</div>
              <div className="font-bold text-sm">•••• 4242</div>
            </div>
          </div>
        </div>
        <div className="flex flex-col items-end justify-between h-full min-w-[120px]">
          <div className="text-xl font-bold">$10/mo</div>
          <div className="text-xs text-color-grey-15">Billed Monthly</div>
        </div>
      </div>

      {/* Billing Cycle Switcher */}
      <div className="bg-white border rounded-xl py-4 px-6 flex flex-col gap-4">
        <div className="text-sm">Billing Cycle</div>
        <PricingToggle
          size="sm"
          showAnnual={{
            percentage: 0,
            show: false,
          }}
        />
        <div className="flex items-center justify-between mt-2">
          <div className="flex flex-col">
            <span className="text-color-grey-15 text-xs">Current Price</span>
            <span className="font-bold text-sm">$10/month</span>
          </div>
          <div className="flex flex-col">
            <span className="text-color-grey-15 text-xs">Annual Price</span>
            <span className="font-bold text-sm">$100/year</span>
          </div>
          <button className="bg-color-yellow text-sm text-black font-semibold px-8 py-3 rounded-lg hover:bg-yellow-400 transition ml-4">
            Switch to Annual Billing
          </button>
        </div>
      </div>
    </div>
  )
}

export default MembershipPlanOverviewTab
