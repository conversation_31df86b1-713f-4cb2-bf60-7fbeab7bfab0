import React, { useState } from 'react'
import MembershipPlanOverviewTab from './MembershipPlanOverviewTab'
import MembershipPlanChangeTab from './MembershipPlanChangeTab'
import MembershipPlanCancelTab from './MembershipPlanCancelTab'
const TABS = [
  { key: 'Overview', value: 'overview' },
  { key: 'Change Plan', value: 'change_plan' },
  { key: 'Cancel', value: 'cancel' },
]
import { X } from 'lucide-react'

const MembershipPlanModal = ({
  onClose,
  plans,
}: {
  onClose: () => void
  plans: any
}) => {
  const [activeTab, setActiveTab] = useState(TABS[0].value)
  return (
    <div className="space-y-4 w-full" style={{ width: 600, maxWidth: '100%' }}>
      <div className="flex items-center justify-between w-full">
        <h1 className="font-bold text-lg">Manage Your Subscription</h1>
        <button
          onClick={onClose}
          className="p-1 hover:bg-color-grey-5 rounded-full transition-colors"
        >
          <X size={20} className="text-color-grey-1" />
        </button>
      </div>
      <div className="flex items-center w-max justify-start">
        {TABS.map((tab: any) => (
          <div
            key={tab?.key}
            className={`flex items-center text-sm pb-3 gap-1 px-5 ${activeTab == tab?.value ? 'border-b-4 border-color-yellow' : 'text-color-grey-1'} cursor-pointer`}
            onClick={() => setActiveTab(tab?.value)}
          >
            <span className="font-semibold text-base">{tab?.key}</span>
          </div>
        ))}
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && <MembershipPlanOverviewTab />}
      {activeTab === 'change_plan' && <MembershipPlanChangeTab plans={plans} />}
      {activeTab === 'cancel' && (
        <MembershipPlanCancelTab
          setActiveTab={setActiveTab}
          onClose={onClose}
        />
      )}
    </div>
  )
}

export default MembershipPlanModal
