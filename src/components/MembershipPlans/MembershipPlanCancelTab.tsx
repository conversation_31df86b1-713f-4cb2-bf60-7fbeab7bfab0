import React, { useEffect, useState } from 'react'
import { CheckCircle, XCircle } from 'lucide-react'
import { useApi } from '@/hooks/useApi'
import { cancelActiveMembership } from '@/lib/actions/membership.actions'
import CustomButton from '../CustomComponents/CustomButton/CustomButton'
import { toast } from 'react-toastify'

const MembershipPlanCancelTab = ({
  setActiveTab,
  onClose,
}: {
  setActiveTab: (_val: string) => void
  onClose: () => void
}) => {
  const [cancelResponse, cancelMembership] = useApi((access: string) =>
    cancelActiveMembership(access),
  )
  const [cancelClick, setCancelClick] = useState<boolean>(false)

  useEffect(() => {
    if (cancelResponse.isSuccess) {
      toast.success('Successfully cancelled mmebership')
      onClose()
    }
  }, [cancelResponse])

  const handleCancel = () => {
    if (!cancelClick) {
      setCancelClick(true)
    } else {
      cancelMembership()
    }
  }
  return (
    <div className="space-y-6">
      {/* Cancel Subscription Notice */}
      <div className="bg-red-100 border border-red-200 rounded-lg p-4">
        <div className="font-bold text-base text-red-700 mb-1">
          Cancel Subscription?
        </div>
        <div className="text-color-grey-15 text-sm">
          Your subscription will remain active until the end of your current
          billing period.
        </div>
      </div>

      {/* What happens when you cancel */}
      <div className="bg-white border rounded-xl text-sm px-4 py-3">
        <div className="font-bold mb-4 text-base">
          What happens when you cancel?
        </div>
        <div className="flex flex-col gap-4">
          <div className="flex items-start gap-3">
            <CheckCircle className="text-green-500 mt-1" size={22} />
            <div>
              <div>Keep access until Aug 16, 2025</div>
              <div className="text-color-grey-15 text-sm mt-1">
                Continue enjoying all Ally benefits until your current period
                ends
              </div>
            </div>
          </div>
          <div className="flex items-start gap-3">
            <XCircle className="text-red-500 mt-1" size={22} />
            <div>
              <div>No automatic renewal</div>
              <div className="text-color-grey-15 text-sm mt-1">
                {`Your subscription won't renew and no future charges will occur`}
              </div>
            </div>
          </div>
          <div className="flex items-start gap-3">
            <CheckCircle className="text-green-500 mt-1" size={22} />
            <div>
              <div>Reactivate anytime</div>
              <div className="text-color-grey-15 text-sm mt-1">
                You can resubscribe at any time to restore your benefits
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Before you go */}
      <div className="bg-white border rounded-xl text-sm px-4 py-3">
        <div className="font-bold text-base mb-2">Before you go …</div>
        <div className="text-color-grey-15 mb-4">
          Consider switching to a different plan instead of canceling
          completely.
        </div>
        <button
          onClick={() => setActiveTab('change_plan')}
          className="w-full border border-color-grey-2 rounded-lg py-3 font-semibold text-color-grey-7 bg-white hover:bg-color-grey-2 transition"
        >
          View Other Plans
        </button>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-end gap-4 mt-8 text-sm font-semibold">
        <button
          className="px-6 py-3 rounded-md border border-color-grey-2 bg-white"
          onClick={() => onClose()}
        >
          Keep Subscription
        </button>
        <CustomButton
          title={cancelClick ? 'Confirm' : 'Cancel Subscription'}
          onClick={handleCancel}
          width="w-48"
          height={12}
          isLoading={cancelResponse.isFetching}
          backgroundColor="bg-red-700"
          textColor="text-white"
        />
      </div>
    </div>
  )
}

export default MembershipPlanCancelTab
