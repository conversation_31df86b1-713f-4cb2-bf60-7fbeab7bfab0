import React, { useMemo, useState } from 'react'
import PricingToggle from '../Subscription/PricingToggle'

const MembershipPlanChangeTab = ({ plans }: { plans: any }) => {
  const [billing, setBilling] = useState<'monthly' | 'annual'>('monthly')
  const [selected, setSelected] = useState('Ally')

  const showAnnual = useMemo(() => {
    let yearlySaving = {
      percentage: 0,
      show: false,
    }
    plans.forEach((product: any) => {
      if (Number(product.annualSavings) > 0) {
        yearlySaving.percentage = Math.max(
          yearlySaving.percentage,
          Number(product.annualSavings),
        )
        yearlySaving.show = true
      }
    })
    return yearlySaving
  }, [plans])

  return (
    <div className="space-y-6">
      {/* Billing Cycle Switcher */}
      <div className="mb-6">
        <PricingToggle
          size="sm"
          billing={billing}
          onChangeBilling={setBilling}
          showAnnual={showAnnual}
        />
      </div>

      {/* Plans List */}
      <div className="flex flex-col gap-4">
        {plans.map((plan: any) => {
          const isCurrent = plan.current
          const isSelected = selected === plan.name
          return (
            <div
              key={plan.name}
              className={`flex items-center justify-between border rounded-xl py-3 px-4 cursor-pointer transition-all ${isSelected ? 'border-2 border-color-yellow bg-color-grey-2' : 'border-color-grey-2'} ${isCurrent ? 'bg-gray-50' : 'bg-white'}`}
              onClick={() => setSelected(plan.name)}
            >
              <div className="flex items-center gap-4">
                <span
                  className={`w-4 h-4 flex items-center justify-center rounded-full border-2 ${isSelected ? 'border-color-yellow bg-color-yellow' : 'border-color-grey-3 bg-white'}`}
                >
                  {isSelected ? (
                    // Black tick SVG
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 16 16"
                      className="w-3 h-3"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M4 8.5L7 11.5L12 6.5"
                        stroke="black"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  ) : null}
                </span>
                <div>
                  <div className="flex items-center gap-2">
                    <span className="font-bold text-base">{plan.name}</span>
                    {isCurrent && (
                      <span className="bg-green-100 text-color-grey-14  text-[10px] font-bold px-2 py-1 rounded-lg">
                        Current Plan
                      </span>
                    )}
                  </div>
                  <div className="text-color-grey-15 text-xs mt-1">
                    {plan.description}
                  </div>
                </div>
              </div>
              <div className="font-bold text-lg">
                ${plan.price[billing]}/{billing === 'monthly' ? 'mo' : 'yr'}
              </div>
            </div>
          )
        })}
      </div>

      {/* Action Buttons */}
      <div className="flex justify-end gap-4 mt-8 text-sm">
        <button className="px-6 py-3 rounded-lg border border-color-grey-27 font-semibold bg-white">
          Cancel
        </button>
        <button className="px-6 py-2 rounded-lg bg-color-yellow text-black font-semibold border border-color-yellow">
          Current Plan
        </button>
      </div>
    </div>
  )
}

export default MembershipPlanChangeTab
