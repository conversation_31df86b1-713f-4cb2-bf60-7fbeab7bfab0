type StatsCardProps = {
  icon: React.ReactNode
  count: string | number
  label: string
  iconBg: string
}

const StatsCard: React.FC<StatsCardProps> = ({
  icon,
  count,
  label,
  iconBg,
}) => (
  <div className="flex items-center gap-3 bg-white rounded-xl shadow p-4 w-64">
    <div
      className={`w-10 h-10 rounded-full flex items-center justify-center ${iconBg}`}
    >
      {icon}
    </div>
    <div>
      <div className="font-semibold text-lg text-gray-800">{count}</div>
      <div className="text-sm text-gray-500">{label}</div>
    </div>
  </div>
)

export default StatsCard
