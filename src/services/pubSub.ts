export type PubSubHandler<T = any> = (data: T) => void

class PubSub {
  private listeners = new Map<string, Set<EventListener>>()

  subscribe<T = any>(event: string, handler: PubSubHandler<T>) {
    const listener = (e: Event) => {
      const customEvent = e as CustomEvent<T>
      handler(customEvent.detail)
    }
    window.addEventListener(event, listener as EventListener)

    // Track listeners for unsubscribeAll
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set())
    }
    this.listeners.get(event)!.add(listener as EventListener)

    // Return unsubscribe function
    return () => {
      window.removeEventListener(event, listener as EventListener)
      this.listeners.get(event)?.delete(listener as EventListener)
    }
  }

  publish<T = any>(event: string, data: T) {
    window.dispatchEvent(new CustomEvent<T>(event, { detail: data }))
  }

  unsubscribeAll(event: string) {
    const set = this.listeners.get(event)
    if (set) {
      Array.from(set).forEach((listener) => {
        window.removeEventListener(event, listener)
      })
      this.listeners.delete(event)
    }
  }
}

const pubSub = new PubSub()
export default pubSub
