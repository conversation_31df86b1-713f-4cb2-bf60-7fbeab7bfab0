import { useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { getToken } from '@/lib/actions/login.actions'
import { handleEmailLogin } from '@/lib/actions/account.actions'

export const useCheckProfileCompletion = () => {
  const router = useRouter()

  const checkProfile = useCallback(async () => {
    const accessToken = await getToken()
    if (!accessToken) return false
    const userData: any = await handleEmailLogin()
    if (!userData && accessToken) {
      router.push('/fill-details')
      return false
    } else if (userData && !userData?.Personal_Information_Reviewed__c) {
      const userdetails = {
        first_name: userData?.FirstName,
        last_name: userData?.LastName,
        phone: userData?.Phone,
        dob: userData?.PersonBirthdate,
      }
      router.push(
        `/fill-details?user=${encodeURIComponent(JSON.stringify(userdetails))}`,
      )
      return false
    }
    return userData
  }, [router])

  return checkProfile
}
