import { useState } from 'react'
import { getToken, handleToken, logout } from '@/lib/actions/login.actions'
import { toast } from 'react-toastify'
import { useRouter } from 'next/navigation'

interface ApiResponse<T> {
  data: any
  isFetching: boolean
  error: string | null
  isSuccess: boolean
}
export function useApi<T>(
  apiFunction: (...args: any[]) => Promise<T>, // Ensure args are passed
  isTokenRequired: boolean = true, // Default to true
  ...args: any[] // Can pass initial arguments to the hook itself, if needed
): [ApiResponse<T>, (...args1: any[]) => Promise<void>] {
  // Explicitly define that the second value accepts args
  const [response, setResponse] = useState<ApiResponse<T>>({
    data: null,
    isFetching: false,
    error: null,
    isSuccess: false,
  })
  const router = useRouter()

  const fetchMethod = async (...args1: any[]) => {
    setResponse({
      data: null,
      isFetching: true,
      error: null,
      isSuccess: false,
    })

    try {
      let accessToken = await getToken('access')
      if (!accessToken && isTokenRequired) {
        await logout()
        throw new Error('Session expired, logging out...')
      }
      // Pass both sets of args to the apiFunction
      let res: any = null
      if (isTokenRequired) {
        res = await apiFunction(accessToken, ...[...args, ...args1])
      } else {
        res = await apiFunction(...[...args, ...args1])
      }
      if ((res as any)?.tokenExpired) {
        const newAccessToken = await handleToken()
        if (newAccessToken) {
          if (isTokenRequired) {
            res = await apiFunction(newAccessToken, ...[...args, ...args1])
          } else {
            res = await apiFunction(...[...args, ...args1])
          }
        } else {
          await logout()
          router.push('/sign-in')
          throw new Error('Session expired, logging out...')
        }
      }
      if (res?.success) {
        setResponse({
          data: res,
          isFetching: false,
          error: null,
          isSuccess: true,
        })
      } else {
        toast.error(res?.data?.errors[0] || 'An error occurred')
        console.error(res?.data?.errors[0] || 'An error occurred')
        setResponse({
          data: res,
          isFetching: false,
          error: res?.data?.errors[0],
          isSuccess: false,
        })
      }
    } catch (err: any) {
      toast.error('Something went wrong')
      console.error(err.message || 'An error occurred')
      setResponse({
        data: null,
        isFetching: false,
        error: err.message || 'An error occurred',
        isSuccess: false,
      })
    }
  }

  return [response, fetchMethod]
}
