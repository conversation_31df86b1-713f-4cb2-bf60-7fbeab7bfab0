import { useEffect, useRef, useCallback } from 'react'

interface IntersectionOptions {
  root?: Element | null
  rootMargin?: string
  threshold?: number | number[]
}

const useInfiniteScroll = (
  callback: () => void,
  options: IntersectionOptions = {},
) => {
  const { root = null, rootMargin = '0px', threshold = 0 } = options

  const observerRef = useRef<HTMLDivElement | null>(null)

  const handleObserver = useCallback(
    (entries: IntersectionObserverEntry[]) => {
      const [target] = entries
      if (target.isIntersecting) {
        callback()
      }
    },
    [callback],
  )

  useEffect(() => {
    const observer = new IntersectionObserver(handleObserver, {
      root,
      rootMargin,
      threshold,
    })

    const target = observerRef.current
    if (target) {
      observer.observe(target)
    }

    return () => {
      if (target) observer.unobserve(target)
    }
  }, [handleObserver, root, rootMargin, threshold])

  return observerRef
}

export default useInfiniteScroll
