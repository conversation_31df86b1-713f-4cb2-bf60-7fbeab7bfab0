import { throttle } from '@/utils/utils'
import { useState, useEffect } from 'react'

export const useIsMobile = (breakpoint = 768) => {
  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    const handleResize = throttle(() => {
      setIsMobile(window.innerWidth < breakpoint)
    }, 5)

    handleResize()

    window.addEventListener('resize', handleResize)

    return () => window.removeEventListener('resize', handleResize)
  }, [breakpoint])

  return isMobile
}
