'use client'

import { Provider } from 'react-redux'
import { useEffect, useState } from 'react'
import { reduxStore } from '@/lib/redux'

export const Providers = (props: React.PropsWithChildren) => {
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true) // Set to true once the component is mounted on the client-side
  }, [])

  if (!isClient) {
    return null // Prevent rendering on the server-side
  }

  return (
    // Wrap children with SessionProvider and Redux Provider for client-side
    <Provider store={reduxStore}>{props.children}</Provider>
  )
}
