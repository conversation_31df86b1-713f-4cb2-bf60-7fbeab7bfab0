// import { Connection, QueryResult } from 'jsforce'
// import { Logger, LogLevels } from 'jsforce/lib/util/logger'

// let cachedConnection: Connection | null = null

// declare global {
//   // eslint-disable-next-line no-var
//   var _sfConn: Connection | null
// }

// const username = process.env.SALESFORCE_USERNAME as string
// const password =
//   (process.env.SALESFORCE_PASSWORD as string) +
//   (process.env.SALESFORCE_SECURITY_TOKEN as string)

// export async function connectToSalesforce() {
//   // If a cached connection exists, return it
//   if (global._sfConn) {
//     return global._sfConn
//   }

//   try {
//     // Establish a new connection to Salesforce with security token
//     const conn = new Connection({
//       loginUrl: process.env.SALESFORCE_LOGIN_URL,
//       version: '61.0',
//     })

//     // --- Patch query with logging ---
//     const originalQuery = conn.query.bind(conn)
//     conn.query = function (soql: string, options?: any) {
//       const start = Date.now()
//       console.log(`[SF QUERY] ${soql}`)

//       const q = originalQuery(soql, options)

//       q.on('end', (res: QueryResult<any>) => {
//         console.log(`[SF QUERY DONE] time=${Date.now() - start}ms`)
//       })

//       q.on('error', (err: Error) => {
//         console.error(`[SF QUERY ERROR] ${soql}`, err)
//       })

//       return q
//     } as typeof conn.query
//     await conn.login(username, password)

//     global._sfConn = conn
//     return conn
//   } catch (error) {
//     console.error('Error connecting to Salesforce:', error)
//     throw error
//   }
// }



// ---------------------------------------------------------------------
import axios from "axios";
import jwt from "jsonwebtoken";
import jsforce, { QueryResult, Connection } from "jsforce";

let cachedConnection: { conn: Connection; expiresAt: number } | null = null;

declare global {
  // eslint-disable-next-line no-var
  var _sfConn: { conn: Connection; expiresAt: number } | null;
}

async function createNewConnection() {
  const loginUrl = process.env.SALESFORCE_LOGIN_URL || "https://test.salesforce.com";
  const clientId = process.env.SALESFORCE_CLIENT_ID!;
  const username = process.env.SALESFORCE_USERNAME!;
  const privateKey = process.env.SALESFORCE_PRIVATE_KEY!.replace(/\\n/g, "\n");

  const token = jwt.sign(
    {
      iss: clientId,
      sub: username,
      aud: loginUrl,
      exp: Math.floor(Date.now() / 1000) + 180, // 3 mins expiry for the signed JWT
    },
    privateKey,
    { algorithm: "RS256" }
  );

  const params = new URLSearchParams();
  params.append("grant_type", "urn:ietf:params:oauth:grant-type:jwt-bearer");
  params.append("assertion", token);

  const response = await axios.post(`${loginUrl}/services/oauth2/token`, params.toString(), {
    headers: { "Content-Type": "application/x-www-form-urlencoded" },
  });

  const data = response.data;


  const conn = new jsforce.Connection({
    instanceUrl: data.instance_url,
    accessToken: data.access_token,
    version: "61.0",
  });

  // Optional query logger
  const originalQuery = conn.query.bind(conn);
  conn.query = function (soql: string, options?: any) {
    const start = Date.now();
    console.log(`[SF QUERY] ${soql}`);


    const q = originalQuery(soql, options);
    q.on("end", (res: QueryResult<any>) => {
      console.log(`[SF QUERY DONE] time=${Date.now() - start}ms`);
    });
    q.on("error", async (err: any) => {
      console.error(`[SF QUERY ERROR] ${soql}`, err);
      if (err?.errorCode === "INVALID_SESSION_ID") {
        console.warn("⚠️ Session expired. Refreshing connection...");
        const newConn = await createNewConnection();
        return newConn.query(soql, options); // retry once with fresh conn
      }
      console.error(`[SF QUERY ERROR] ${soql}`, err);
      throw err;
    });

    return q;
  } as typeof conn.query;

  // Cache with expiry (15 mins from now)
  const expiresAt = Date.now() + 14 * 60 * 1000; // refresh 1 min before expiry

  global._sfConn = { conn, expiresAt };
  return conn;
}

export async function connectToSalesforce() {
  if (global._sfConn && Date.now() < global._sfConn.expiresAt) {
    return global._sfConn.conn;
  }

  console.log("🔑 Refreshing Salesforce connection...");
  return await createNewConnection();
}