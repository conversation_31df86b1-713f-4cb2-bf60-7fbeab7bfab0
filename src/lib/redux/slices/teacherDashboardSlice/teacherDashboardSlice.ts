import { createSlice, PayloadAction } from '@reduxjs/toolkit'

interface JourneyStats {
  classesInProgress: number
  classesTaught: number
  studentsTaught: number
  studentsFavorited: number
}

interface TeacherDashboardState {
  quotes: any[]
  journey: JourneyStats | null
  todoList: any[]
  teacherClasses: any[]
  eventsByMonth: Record<string, any[]>
  eventsForToday: any[]
}

const initialState: TeacherDashboardState = {
  quotes: [],
  journey: null,
  todoList: [],
  teacherClasses: [],
  eventsByMonth: {},
  eventsForToday: [],
}

export const teacherDashboardSlice = createSlice({
  name: 'teacherDashboard',
  initialState,
  reducers: {
    setTeacherQuotes: (state, action: PayloadAction<any[]>) => {
      state.quotes = action.payload
    },
    setTeacherMuseJourney: (state, action: PayloadAction<any>) => {
      state.journey = action.payload
    },
    setTodoList: (state, action: PayloadAction<any[]>) => {
      state.todoList = action.payload
    },
    setTeacherClasses: (state, action: PayloadAction<any[]>) => {
      state.teacherClasses = action.payload
    },
    setMonthlyEvents: (
      state,
      action: PayloadAction<{ month: string; events: any[] }>,
    ) => {
      state.eventsByMonth[action.payload.month] = action.payload.events
    },
    setEventsForToday: (state, action: PayloadAction<any[]>) => {
      state.eventsForToday = action.payload
    },
  },
})

export const {
  setTeacherQuotes,
  setTeacherMuseJourney,
  setTodoList,
  setTeacherClasses,
  setMonthlyEvents,
  setEventsForToday,
} = teacherDashboardSlice.actions

export default teacherDashboardSlice.reducer
