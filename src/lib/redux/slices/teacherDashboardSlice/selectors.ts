import type { ReduxState } from '@/lib/redux'

export const selectTeacherQuotes = (state: ReduxState) =>
  state.teacherDashboard.quotes

export const selectTeacherMuseJourney = (state: ReduxState) =>
  state.teacherDashboard.journey

export const selectTodoList = (state: ReduxState) =>
  state.teacherDashboard.todoList

export const selectTeacherClasses = (state: ReduxState) =>
  state.teacherDashboard.teacherClasses

export const selectEventsByMonth = (state: ReduxState) =>
  state.teacherDashboard.eventsByMonth

export const selectEventsToday = (state: ReduxState) =>
  state.teacherDashboard.eventsForToday
