import { createSlice, PayloadAction } from '@reduxjs/toolkit'

interface JourneyStats {
  ongoingClasses: number
  classesAttended: number
  badgesEarned: number
  teachersFollowed: number
}

interface YouthStudentDashboardState {
  quotes: any[]
  journey: JourneyStats | null
  youthStudentClasses: any[]
  eventsByMonth: Record<string, any[]>
  eventsForToday: any[]
}

const initialState: YouthStudentDashboardState = {
  quotes: [],
  journey: null,
  youthStudentClasses: [],
  eventsByMonth: {},
  eventsForToday: [],
}

export const youthStudentDashboardSlice = createSlice({
  name: 'youthStudentDashboard',
  initialState,
  reducers: {
    setYouthStudentQuotes: (state, action: PayloadAction<any[]>) => {
      state.quotes = action.payload
    },
    setYouthMuseJourney: (state, action: PayloadAction<any>) => {
      state.journey = action.payload
    },
    setYouthStudentClasses: (state, action: PayloadAction<any[]>) => {
      state.youthStudentClasses = action.payload
    },
    setYouthMonthlyEvents: (
      state,
      action: PayloadAction<{ month: string; events: any[] }>,
    ) => {
      state.eventsByMonth[action.payload.month] = action.payload.events
    },
    setYouthEventsForToday: (state, action: PayloadAction<any[]>) => {
      state.eventsForToday = action.payload
    },
  },
})

export const {
  setYouthStudentQuotes,
  setYouthMuseJourney,
  setYouthStudentClasses,
  setYouthMonthlyEvents,
  setYouthEventsForToday,
} = youthStudentDashboardSlice.actions

export default youthStudentDashboardSlice.reducer
