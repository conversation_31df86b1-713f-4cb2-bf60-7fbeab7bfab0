import type { ReduxState } from '@/lib/redux'

export const selectAdultMuseJourney = (state: ReduxState) =>
  state.adultStudentDashboard.journey

export const selectAdultStudentQuotes = (state: ReduxState) =>
  state.adultStudentDashboard.quotes

export const selectAdultEventsByMonth = (state: ReduxState) =>
  state.adultStudentDashboard.eventsByMonth

export const selectAdultEventsToday = (state: ReduxState) =>
  state.adultStudentDashboard.eventsForToday
