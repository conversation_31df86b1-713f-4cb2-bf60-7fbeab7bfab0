import { createSlice, PayloadAction } from '@reduxjs/toolkit'

interface JourneyStats {
  ongoingClasses: number
  classesAttended: number
  badgesEarned: number
  teachersFollowed: number
}

interface AdultStudentDashboardState {
  quotes: any[]
  journey: JourneyStats | null
  adultStudentClasses: any[]
  eventsByMonth: Record<string, any[]>
  eventsForToday: any[]
}

const initialState: AdultStudentDashboardState = {
  quotes: [],
  journey: null,
  adultStudentClasses: [],
  eventsByMonth: {},
  eventsForToday: [],
}

export const adultStudentDashboardSlice = createSlice({
  name: 'adultStudentDashboard',
  initialState,
  reducers: {
    setAdultStudentQuotes: (state, action: PayloadAction<any[]>) => {
      state.quotes = action.payload
    },
    setAdultMuseJourney: (state, action: PayloadAction<any>) => {
      state.journey = action.payload
    },
    setAdultStudentClasses: (state, action: PayloadAction<any[]>) => {
      state.adultStudentClasses = action.payload
    },
    setAdultMonthlyEvents: (
      state,
      action: PayloadAction<{ month: string; events: any[] }>,
    ) => {
      state.eventsByMonth[action.payload.month] = action.payload.events
    },
    setAdultEventsForToday: (state, action: PayloadAction<any[]>) => {
      state.eventsForToday = action.payload
    },
  },
})

export const {
  setAdultStudentQuotes,
  setAdultMuseJourney,
  setAdultStudentClasses,
  setAdultMonthlyEvents,
  setAdultEventsForToday,
} = adultStudentDashboardSlice.actions

export default adultStudentDashboardSlice.reducer
