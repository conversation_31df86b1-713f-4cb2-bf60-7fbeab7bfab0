import { createSlice, PayloadAction } from '@reduxjs/toolkit'

interface ParentDashboardState {
  eventsByMonth: Record<string, any[]>
  eventsForToday: any[]
}

const initialState: ParentDashboardState = {
  eventsByMonth: {},
  eventsForToday: [],
}

export const parentDashboardSlice = createSlice({
  name: 'parentDashboard',
  initialState,
  reducers: {
    setParentMonthlyEvents: (
      state,
      action: PayloadAction<{ month: string; events: any[] }>,
    ) => {
      state.eventsByMonth[action.payload.month] = action.payload.events
    },
    setParentEventsForToday: (state, action: PayloadAction<any[]>) => {
      state.eventsForToday = action.payload
    },
  },
})

export const { setParentMonthlyEvents, setParentEventsForToday } =
  parentDashboardSlice.actions

export default parentDashboardSlice.reducer
