import { createSlice, PayloadAction } from '@reduxjs/toolkit'

interface MetaState {
  isSideHeaderVisible: boolean
  phoneNumber: string
  paymentToken: string | null
  profilePhoto: string
  cartCount: number
}

const initialState: MetaState = {
  isSideHeaderVisible: false,
  phoneNumber: '',
  paymentToken: null,
  profilePhoto: '',
  cartCount: 0,
}

export const metaSlice = createSlice({
  name: 'metadata',
  initialState,
  reducers: {
    setSideHeaderVisiblity: (state, action: PayloadAction<boolean>) => {
      state.isSideHeaderVisible = action.payload
    },
    setUserPhoneNumber: (state, action: PayloadAction<string>) => {
      state.phoneNumber = action.payload
    },
    setPaymentToken: (state, action: PayloadAction<string | null>) => {
      state.paymentToken = action.payload
    },
    setProfilePhoto: (state, action: PayloadAction<string>) => {
      state.profilePhoto = action.payload
    },
    setCartCount: (state, action: PayloadAction<number>) => {
      state.cartCount = action.payload
    },
    increaseCartCount: (state) => {
      state.cartCount += 1
    },
    decreaseCartCount: (state) => {
      state.cartCount -= 1
    },
  },
})

export const {
  setSideHeaderVisiblity,
  setUserPhoneNumber,
  setPaymentToken,
  setProfilePhoto,
  setCartCount,
  increaseCartCount,
  decreaseCartCount,
} = metaSlice.actions

export default metaSlice.reducer
