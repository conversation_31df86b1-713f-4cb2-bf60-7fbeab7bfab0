import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'
import { getAllFollowedTeachers } from '@/lib/actions/follow.actions'
import { callApiWithToken } from '@/utils/apiHelperWithToken'
import { getAllMeetings, getClasses, getMyBookings } from '@/lib/actions/class.actions'
import { getAccountQuotes } from '@/lib/actions/account.actions'
import { callApiWithoutToken } from '@/utils/apiHelperWithoutToken'
import {
  getParentTodoList,
  getMyKidSection,
  updateParentTodo,
  updateYouthAccount,
  getAllParentMeetings,
  addYouthAccount,
} from '@/lib/actions/parent.actions'
import { ROLES } from '@/types/enum'
import {
  getAllTeacherMeetings,
  getTeacherClassesByDate,
} from '@/lib/actions/teacher.actions'
export const apiSlice = createApi({
  reducerPath: 'api',
  baseQuery: fetchBaseQuery({ baseUrl: '/' }), // not used here
  tagTypes: ['ParentTodo', 'KidSection'],
  endpoints: (builder) => ({
    getAllFollowedTeachers: builder.query<any, void>({
      async queryFn(_arg, _api, _extraOptions, _baseQuery) {
        try {
          const result = await callApiWithToken(getAllFollowedTeachers)
          if (result.success) {
            return { data: result }
          } else {
            return {
              error: {
                status: 500,
                data: result?.data?.errors?.[0] || 'Unknown error',
              },
            }
          }
        } catch (error: any) {
          return {
            error: {
              status: 500,
              data: error.message || 'Unexpected error',
            },
          }
        }
      },
      keepUnusedDataFor: 0,
    }),
    getClasses: builder.query<any, Record<string, any>>({
      async queryFn(_arg, _api, _extraOptions, _baseQuery) {
        try {
          const {
            isTokenRequired = false,
            classType,
            classCategory,
          } = _arg || {}

          const result = await callApiWithToken(
            getClasses,
            isTokenRequired,
            classType,
            classCategory,
          )
          if (result.success) {
            return { data: result }
          } else {
            return {
              error: {
                status: 500,
                data: result?.data?.errors?.[0] || 'Unknown error',
              },
            }
          }
        } catch (error: any) {
          return {
            error: {
              status: 500,
              data: error.message || 'Unexpected error',
            },
          }
        }
      },
    }),
    getMyBookings: builder.query<any, Record<string, any>>({
      async queryFn(_arg, _api, _extraOptions, _baseQuery) {
        try {
          const {
            isTokenRequired = false,
            classType,
            classCategory,
          } = _arg || {}

          const result = await callApiWithToken(
            getMyBookings,
            isTokenRequired,
            classType,
            classCategory,
          )
          if (result.success) {
            return { data: result }
          } else {
            return {
              error: {
                status: 500,
                data: result?.data?.errors?.[0] || 'Unknown error',
              },
            }
          }
        } catch (error: any) {
          return {
            error: {
              status: 500,
              data: error.message || 'Unexpected error',
            },
          }
        }
      },
    }),
    getAccountQuotes: builder.query<any, Record<string, any>>({
      async queryFn(_arg, _api, _extraOptions, _baseQuery) {
        try {
          const { type } = _arg || {}

          const result = await callApiWithoutToken(getAccountQuotes, type)
          if (result.success) {
            return { data: result }
          } else {
            return {
              error: {
                status: 500,
                data: result?.data?.errors?.[0] || 'Unknown error',
              },
            }
          }
        } catch (error: any) {
          return {
            error: {
              status: 500,
              data: error.message || 'Unexpected error',
            },
          }
        }
      },
    }),
    getParentTodoList: builder.query<any, Record<string, any>>({
      async queryFn(_arg, _api, _extraOptions, _baseQuery) {
        try {
          const { isTokenRequired = false } = _arg || {}

          const result = await callApiWithToken(
            getParentTodoList,
            isTokenRequired,
          )
          if (result.success) {
            return { data: result }
          } else {
            return {
              error: {
                status: 500,
                data: result?.data?.errors?.[0] || 'Unknown error',
              },
            }
          }
        } catch (error: any) {
          return {
            error: {
              status: 500,
              data: error.message || 'Unexpected error',
            },
          }
        }
      },
      providesTags: ['ParentTodo'],
    }),
    getMyKidSection: builder.query<any, Record<string, any>>({
      async queryFn(_arg, _api, _extraOptions, _baseQuery) {
        try {
          const { isTokenRequired = false } = _arg || {}

          const result = await callApiWithToken(
            getMyKidSection,
            isTokenRequired,
          )
          if (result.success) {
            return { data: result }
          } else {
            return {
              error: {
                status: 500,
                data: result?.data?.errors?.[0] || 'Unknown error',
              },
            }
          }
        } catch (error: any) {
          return {
            error: {
              status: 500,
              data: error.message || 'Unexpected error',
            },
          }
        }
      },
      providesTags: ['KidSection'],
    }),
    updateParentTodo: builder.mutation<
      any,
      {
        isTokenRequired: boolean
        orderId: string
        type: 'Approved' | 'Rejected'
        paidBy?: 'Parent' | 'Child'
      }
    >({
      async queryFn({ isTokenRequired, orderId, type, paidBy }) {
        try {
          const result = await callApiWithToken(
            updateParentTodo,
            isTokenRequired,
            orderId,
            type,
            paidBy,
          )
          if (result?.success) {
            return { data: result }
          } else {
            return {
              error: {
                status: 500,
                data: result?.data?.errors?.[0] || 'Unknown error',
              },
            }
          }
        } catch (error: any) {
          return {
            error: {
              status: 500,
              data: error.message || 'Unexpected error',
            },
          }
        }
      },
      invalidatesTags: ['ParentTodo'],
    }),
    updateYouthAccount: builder.mutation<
      any,
      { isTokenRequired: boolean; youthId: string; data: any }
    >({
      async queryFn({ isTokenRequired, youthId, data }) {
        try {
          const result = await callApiWithToken(
            updateYouthAccount,
            isTokenRequired,
            youthId,
            data,
          )
          if (result?.success) {
            return { data: result }
          } else {
            return {
              error: {
                status: 500,
                data: result?.data?.errors?.[0] || 'Unknown error',
              },
            }
          }
        } catch (error: any) {
          return {
            error: {
              status: 500,
              data: error.message || 'Unexpected error',
            },
          }
        }
      },
    }),
    getMeetingsByRole: builder.query<any, Record<string, any>>({
      async queryFn(_arg, _api, _extraOptions, _baseQuery) {
        try {
          const {
            isTokenRequired = false,
            startDate,
            endDate,
            role,
          } = _arg || {}

          let result
          switch (role) {
            case ROLES.Teacher:
              result = await callApiWithToken(
                getAllTeacherMeetings,
                isTokenRequired,
                startDate,
                endDate,
              )
              break
            case ROLES.Adult_Student:
            case ROLES.Youth_Student:
              result = await callApiWithToken(
                getAllMeetings,
                isTokenRequired,
                startDate,
                endDate,
              )
              break
            case ROLES.Parent:
              result = await callApiWithToken(
                getAllParentMeetings,
                isTokenRequired,
                startDate,
                endDate,
              )
              break
          }
          if (result?.success) {
            return { data: result }
          } else {
            return {
              error: {
                status: 500,
                data: result?.data?.errors?.[0] || 'Unknown error',
              },
            }
          }
        } catch (error: any) {
          return {
            error: {
              status: 500,
              data: error.message || 'Unexpected error',
            },
          }
        }
      },
    }),
    addYouthAccount: builder.mutation<
      any,
      {
        isTokenRequired: boolean
        firstName: string
        lastName: string
        dateOfBirth: string
        email: string
        phone: string
        photoUrl: string
      }
    >({
      async queryFn({
        isTokenRequired,
        firstName,
        lastName,
        dateOfBirth,
        email,
        phone,
        photoUrl,
      }) {
        try {
          const result = await callApiWithToken(
            addYouthAccount,
            isTokenRequired,
            firstName,
            lastName,
            dateOfBirth,
            email,
            phone,
            photoUrl,
          )
          if (result?.success) {
            return { data: result }
          } else {
            return {
              error: {
                status: 500,
                data: result?.data?.errors?.[0] || 'Unknown error',
              },
            }
          }
        } catch (error: any) {
          return {
            error: {
              status: 500,
              data: error.message || 'Unexpected error',
            },
          }
        }
      },
      invalidatesTags: ['KidSection'],
    }),
  }),
})

export const {
  useLazyGetAllFollowedTeachersQuery,
  useGetAllFollowedTeachersQuery,
  useGetClassesQuery,
  useGetMyBookingsQuery,
  useGetAccountQuotesQuery,
  useGetParentTodoListQuery,
  useGetMyKidSectionQuery,
  useUpdateParentTodoMutation,
  useUpdateYouthAccountMutation,
  useGetMeetingsByRoleQuery,
  useAddYouthAccountMutation,
} = apiSlice
