import { createSlice, PayloadAction } from '@reduxjs/toolkit'

export interface FollowedTeacher {
  id: string
  name: string
  photo: string
}

export interface FollowState {
  followedMap: Record<string, FollowedTeacher>
}

const initialState: FollowState = {
  followedMap: {},
}

export const followSlice = createSlice({
  name: 'follow',
  initialState,
  reducers: {
    setFollowedTeachers: (state, action: PayloadAction<FollowedTeacher[]>) => {
      const map: Record<string, FollowedTeacher> = {}
      action.payload.forEach((teacher) => {
        map[teacher.id] = teacher
      })
      state.followedMap = map
    },
    addFollowedTeacher: (state, action: PayloadAction<FollowedTeacher>) => {
      state.followedMap[action.payload.id] = action.payload
    },
    removeFollowedTeacher: (state, action: PayloadAction<string>) => {
      delete state.followedMap[action.payload]
    },
  },
})

export const {
  setFollowedTeachers,
  addFollowedTeacher,
  removeFollowedTeacher,
} = followSlice.actions

export default followSlice.reducer
