import { createClient, RedisClientType } from 'redis'

class RedisClient {
  private static instance: RedisClient
  private client: RedisClientType | null = null
  private isConnecting: boolean = false
  private connectionPromise: Promise<void> | null = null

  private constructor() {
    // Private constructor to prevent direct construction calls with the `new` operator
  }

  public static getInstance(): RedisClient {
    if (!RedisClient.instance) {
      RedisClient.instance = new RedisClient()
    }
    return RedisClient.instance
  }

  private async connect(): Promise<void | null> {
    if (this.isConnecting) {
      return this.connectionPromise
    }

    this.isConnecting = true
    this.connectionPromise = new Promise(async (resolve, reject) => {
      try {
        this.client = await createClient({
          url: process.env.REDIS_URL ?? 'redis://localhost:6379',
          socket: {
            connectTimeout: 10000,
            keepAlive: true,
          },
        })

        // Error handling
        this.client.on('error', (err: any) => {
          console.error('Redis Client Error:', err)
        })

        // Reconnection handling
        this.client.on('reconnecting', () => {
          console.log('Redis Client reconnecting...')
        })

        this.client.on('connect', () => {
          console.log('Redis Client connected')
        })

        await this.client.connect()
        resolve()
      } catch (error) {
        console.error('Redis connection error:', error)
        this.client = null
        reject(error)
      } finally {
        this.isConnecting = false
      }
    })

    return this.connectionPromise
  }

  public async getClient(): Promise<RedisClientType | null> {
    if (!this.client) {
      await this.connect()
    }
    return this.client
  }

  public async get(key: string): Promise<string | null> {
    const client = await this.getClient()
    if (!client) return null
    return client.get(key)
  }

  public async set(
    key: string,
    value: string,
    options?: {
      EX?: number // Expiration in seconds
      NX?: boolean // Only set if key doesn't exist
    },
  ): Promise<string | null> {
    const client = await this.getClient()
    if (!client) return null
    return client.set(key, value, options)
  }

  public async delete(key: string): Promise<number> {
    const client = await this.getClient()
    if (!client) return 0
    return client.del(key)
  }

  public async getCacheKey(
    prefix: string,
    ...parts: (string | string[] | any)[]
  ): Promise<string> {
    const flattenAndFormat = (part: any): string[] => {
      if (Array.isArray(part)) {
        // Recursively flatten arrays and filter out empty/null values
        return part.flatMap(flattenAndFormat).filter(Boolean)
      } else if (part === null || part === undefined) {
        return []
      } else {
        // Convert any value to string, handle dates and objects
        return [
          typeof part === 'object'
            ? part instanceof Date
              ? part.toISOString()
              : JSON.stringify(part)
            : String(part),
        ]
      }
    }

    // Flatten and clean all parts
    const flattenedParts = parts
      .flatMap(flattenAndFormat)
      .filter(Boolean) // Remove empty strings
      .map((part) => part.replace(/:/g, '_')) // Replace colons to avoid key parsing issues

    // Join with prefix
    return `${prefix}:${flattenedParts.join(':')}`
  }
}

export const redisClient = RedisClient.getInstance()
