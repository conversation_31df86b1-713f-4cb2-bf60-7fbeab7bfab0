'use server'

import { connectToSalesforce } from '@/lib/salesforce'

export const getPageContent = async (slug: string) => {
  try {
    const conn = await connectToSalesforce()
    const soql = `SELECT Id,Title__c, Content__c, CreatedDate,Form_ID__c, Author__c from Page__c where Slug_ID__c ='${slug}'`
    const { records, totalSize } = await conn.query(soql)
    if (totalSize === 0) {
      return null
    }
    const pageData = records[0]
    return pageData
  } catch (error) {
    return null
  }
}
