'use server'
import { connectToSalesforce } from '@/lib/salesforce'

export const getAllBlogs = async (limit = 10, offset = 0) => {
  const conn = await connectToSalesforce()
  const query = `Select Id,Title__c, Author__r.Name, Content__c from Blog__c ORDER BY CreatedDate DESC LIMIT ${limit} OFFSET ${offset}`
  const countQuery = 'Select Id, Author__r.Name, Content__c from Page__c'

  const blogs = await conn.query(query)
  const countBlogs = await conn.query(countQuery)
  const returnData = blogs.records.map((blog) => {
    return {
      id: blog.Id,
      author: blog.Author__r.Name,
      content: blog.Content__c,
      title: blog.Title__c,
    }
  })

  const data = {
    data: returnData,
    total: countBlogs.totalSize,
  }

  return data
}

export const getBlogById = async (id: string) => {
  const conn = await connectToSalesforce()
  const query = `Select Id,Title__c, Author__r.Name, Content__c, CreatedDate  from Blog__c WHERE Id = '${id}'`

  const blogs = await conn.query(query)
  const returnData = blogs.records.map((blog) => {
    return {
      id: blog.Id,
      title: blog.Title__c,
      author: blog.Author__r.Name,
      content: blog.Content__c,
      createdDate: blog.CreatedDate,
    }
  })

  return returnData
}
