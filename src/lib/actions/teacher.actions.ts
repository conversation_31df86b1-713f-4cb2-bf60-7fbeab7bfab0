'use server'
import { connectToSalesforce } from '@/lib/salesforce'
import {
  arrayToSqlInQuery,
  convertTimestampToET,
  endOfDay,
  responseGenerator,
  startOfDay,
} from '@/utils/utils'
import {
  getAccountRolesServer,
  getAccountId,
  getTeacherForClassServer,
} from '@/lib/actions/account.actions'
import { ROLES } from '@/types/enum'
import { verifyCustomAccountSession } from '@/lib/actions/login.actions'
import jsforce from 'jsforce'
import { getRecordTypeByName } from './salesforce.actions'
import {
  getClassesBadgesByTypeServer,
  getClassVenueServer,
} from './class.actions'
import { redisClient } from '../redis'

type ClassType = 'Attendance Pending' | 'Active' | 'Previous'

type AttendanceArgs = {
  studentId: string
  attendenceType: 'In Person' | 'Online' | 'Absent'
}

export const getAllTeacherMeetingsByIdServer = async (
  teacherId: string,
  startDate: string,
  endDate: string,
) => {
  const from = startOfDay(startDate)
  const to = endOfDay(endDate)

  const conn = await connectToSalesforce()
  const soql = `SELECT Id,Meeting_Time__c, Venue__r.Name, Duration__c,  Class_and_Event__r.Title__c, Class_and_Event__r.Id,Class_and_Event__r.Banner_Image__c,Class_and_Event__r.Category__c, Class_and_Event__r.Level__c, Class_and_Event__r.Type__c, Class_and_Event__r.Total_Seats__c, Class_and_Event__r.Booked_Seats__c,Starts_On__c, Ends_On__c FROM Meeting__c WHERE Ends_On__c >= ${new Date(from).toISOString()} AND Ends_On__c <=${new Date(to).toISOString()} AND Teacher__c = '${teacherId}' ORDER BY Starts_On__c`
  const records = (await conn.query(soql)).records

  const meetingData = records.map((item) => {
    return {
      classId: item.Class_and_Event__r.Id,
      classBannerImage: item.Class_and_Event__r.Banner_Image__c,
      classTitle: item.Class_and_Event__r.Title__c,
      classCategory: item.Class_and_Event__r.Category__c,
      classType: item.Class_and_Event__r.Type__c,
      classLevel: item.Class_and_Event__r.Level__c,
      totalSeats: item.Class_and_Event__r.Total_Seats__c,
      bookedSeats: item.Class_and_Event__r.Booked_Seats__c,
      meetingId: item.Id,
      meetingTime: item.Meeting_Time__c,
      meetingLocation: item.Venue__r?.Name || '',
      meetingDuration: item.Duration__c,
      startDate: item.Starts_On__c,
      startTime: item.Starts_On__c,
      endTime: item.Ends_On__c,
    }
  })
  return meetingData
}

export const getAssociatedTeachersForClassByIdServer = async (
  classId: string,
) => {
  const conn = await connectToSalesforce()

  let soql = `SELECT Teacher__c FROM Meeting__c WHERE Class_and_Event__c = '${classId}' GROUP BY Teacher__c `
  const records = (await conn.query(soql)).records
  const idList = records
    .map((record: any) => record.Teacher__c)
    .filter((id: string) => id && id !== null && id !== undefined)

  if (!idList.length) {
    return []
  }

  soql = `SELECT Id, Name, Photo__c,PersonEmail FROM Account WHERE Id IN ${arrayToSqlInQuery(idList)}`
  const accounts = (await conn.query(soql)).records

  const returnData = accounts.map((account: any) => {
    return {
      teacher_id: account.Id,
      teacher_name: account.Name,
      teacher_photo: account.Photo__c,
      teacher_email: account.PersonEmail,
    }
  })

  return returnData
}

export const getTeachersOfUserByIdServer = async (userId: string) => {
  const conn = await connectToSalesforce()
  const soql = `SELECT Teacher__c FROM Meeting__c WHERE Class_and_Event__c IN (SELECT Class_and_Event__c FROM Registration__c WHERE Student__c = '${userId}') GROUP BY Teacher__c`
  const records = (await conn.query(soql)).records
  const idList = records
    .map((record: any) => record.Teacher__c)
    .filter((id: string) => id && id !== null && id !== undefined)
  return idList
}

export const getTeacherDetails = async (accessToken: string) => {
  try {
    if (!(await verifyCustomAccountSession(accessToken))) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const accessTokenUserId = await getAccountId(accessToken)

    if (!accessTokenUserId) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const roles = await getAccountRolesServer(accessTokenUserId)

    if (!roles.includes(ROLES.Teacher)) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const conn = await connectToSalesforce()

    const teacherDetailSoql = `Select Id,Name,Photo__c,Description,Website,Instagram__c,Facebook__c,Twitter__c,Genre_Teacher__c from Account where Id='${accessTokenUserId}'`
    const teacherDetail = (await conn.query(teacherDetailSoql)).records[0]

    if (!teacherDetail) {
      throw new Error('Teacher details not found')
    }

    const totalClasses = await conn
      .sobject('Class_Event_Club__c')
      .count({ Teacher__c: accessTokenUserId })

    const result = {
      ...teacherDetail,
      totalClasses,
    }

    return responseGenerator(true, result)
  } catch (error) {
    return responseGenerator(false, { errors: ['Error getting details'] })
  }
}

export const getTeacherClasses = async (
  accessToken: string,
  classType: ClassType,
  limit = 10,
  offset = 0,
) => {
  try {
    if (!(await verifyCustomAccountSession(accessToken))) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const accessTokenUserId = await getAccountId(accessToken)

    if (!accessTokenUserId) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const roles = await getAccountRolesServer(accessTokenUserId)

    if (!roles.includes(ROLES.Teacher)) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const conn = await connectToSalesforce()
    const teacherClassesSoql = `SELECT Class_and_Event__c FROM Meeting__c WHERE Teacher__c = '${accessTokenUserId}'`

    switch (classType) {
      case 'Attendance Pending':
        const attendancePendingClassesSoql = `Select Id, Title__c, Genre__c, Banner_Image__c, Booked_Seats__c, Duration__c, Total_Meetings_Count__c, Teacher__c from Class_Event_Club__c where  Total_Attendees__c = 0 AND Id IN (${teacherClassesSoql}) LIMIT ${limit} OFFSET ${offset}`
        const attendancePendingClassesCountSoql = `Select Id from Class_Event_Club__c where  Total_Attendees__c = 0 AND Id IN (${teacherClassesSoql})`
        const countResponse = (
          await conn.query(attendancePendingClassesCountSoql)
        ).totalSize
        const attendancePendingClasses = (
          await conn.query(attendancePendingClassesSoql)
        ).records

        const attendancePendingClassesWithTeacher = await Promise.all(
          attendancePendingClasses.map(async (classEvent) => {
            if (!classEvent.Teacher__c)
              return { ...classEvent, teacher_data: null }
            const teacher = await getTeacherForClassServer(
              classEvent.Teacher__c,
            )
            return { ...classEvent, teacher_data: teacher }
          }),
        )

        const data = {
          data: attendancePendingClassesWithTeacher,
          total: countResponse,
        }

        return responseGenerator(true, data)
      case 'Active':
        const activeClassesSoql = `Select Id,Title__c, Genre__c, Banner_Image__c, Booked_Seats__c, Duration__c, Total_Meetings_Count__c, Teacher__c from Class_Event_Club__c where  End_Date__c >= ${jsforce.Date.TODAY} AND Id IN (${teacherClassesSoql}) LIMIT ${limit} OFFSET ${offset}`
        const activeClassesCountSoql = `Select Id from Class_Event_Club__c where  End_Date__c >= ${jsforce.Date.TODAY} AND Id IN (${teacherClassesSoql})`
        const activeClassescountResponse = (
          await conn.query(activeClassesCountSoql)
        ).totalSize
        const activeClasses = (await conn.query(activeClassesSoql)).records

        const activeClassesWithTeacher = await Promise.all(
          activeClasses.map(async (classEvent) => {
            if (!classEvent.Teacher__c)
              return { ...classEvent, teacher_data: null }
            const teacher = await getTeacherForClassServer(
              classEvent.Teacher__c,
            )
            return { ...classEvent, teacher_data: teacher }
          }),
        )

        const activeClassesData = {
          data: activeClassesWithTeacher,
          total: activeClassescountResponse,
        }
        return responseGenerator(true, activeClassesData)

      case 'Previous':
        const previousClassesSoql = `Select Id, Title__c, Genre__c, Banner_Image__c, Booked_Seats__c, Duration__c, Total_Meetings_Count__c, Teacher__c from Class_Event_Club__c where  End_Date__c < ${jsforce.Date.TODAY} AND Id IN (${teacherClassesSoql}) LIMIT ${limit} OFFSET ${offset}`
        const previousClassesCountSoql = `Select Id from Class_Event_Club__c where  End_Date__c < ${jsforce.Date.TODAY} AND Id IN (${teacherClassesSoql})`
        const previousClassescountResponse = (
          await conn.query(previousClassesCountSoql)
        ).totalSize
        const previousClasses = (await conn.query(previousClassesSoql)).records

        const previousClassesWithTeacher = await Promise.all(
          previousClasses.map(async (classEvent) => {
            if (!classEvent.Teacher__c)
              return { ...classEvent, teacher_data: null }
            const teacher = await getTeacherForClassServer(
              classEvent.Teacher__c,
            )
            return { ...classEvent, teacher_data: teacher }
          }),
        )

        const previousClassesData = {
          data: previousClassesWithTeacher,
          total: previousClassescountResponse,
        }
        return responseGenerator(true, previousClassesData)
      default:
        return responseGenerator(false, { errors: ['Invalid class type'] })
    }
  } catch (error) {
    return responseGenerator(false, { errors: ['Error getting classes'] })
  }
}

export const getClassAttendeesServer = async (classId: string) => {
  const conn = await connectToSalesforce()
  const classAttendeesSoql = `Select Id,Student__r.Id, Student__r.Name, Student__r.PersonEmail,Student__r.Photo__c from Registration__c where Class_and_Event__c = '${classId}'`
  const classAttendees = (await conn.query(classAttendeesSoql)).records

  const classAttendeesResult = classAttendees.map((attendee: any) => {
    return {
      id: attendee.Student__r.Id,
      name: attendee.Student__r.Name,
      email: attendee.Student__r.PersonEmail,
      photo: attendee.Student__r.Photo__c,
    }
  })

  return classAttendeesResult
}

export const getClassMailingListsServer = async (classId: string) => {
  const conn = await connectToSalesforce()
  const classMailingListsSoql = `Select Id, Name, Type__c, Total_Contacts__c from Mailing_List__c where Class_Event_and_Outreach__c = '${classId}'`
  const classMailingLists = (await conn.query(classMailingListsSoql)).records

  const classMailingListsResult = classMailingLists.map((mailingList: any) => {
    return {
      id: mailingList.Id,
      name: mailingList.Name,
      type: mailingList.Type__c,
      totalContacts: mailingList.Total_Contacts__c,
    }
  })

  return classMailingListsResult
}

export const getTeacherClassDetails = async (
  accessToken: string,
  classId: string,
) => {
  try {
    if (!(await verifyCustomAccountSession(accessToken))) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const accessTokenUserId = await getAccountId(accessToken)

    if (!accessTokenUserId) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const roles = await getAccountRolesServer(accessTokenUserId)

    if (!roles.includes(ROLES.Teacher)) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const conn = await connectToSalesforce()

    const classDetailsSoql = `Select Id,Title__c,Level__c,Start_Date__c, End_Date__c,Zoom_Room__c,Genre__c,Room__c, Banner_Image__c,Venue__c, Zoom_Meeting_Link__c,Total_Seats__c, Booked_Seats__c, Total_Meetings_Count__c, Category__c, Description__c, Type__c  from Class_Event_Club__c where Id='${classId}'`
    const classDetails = (await conn.query(classDetailsSoql)).records[0]

    if (!classDetails) {
      return responseGenerator(false, { errors: ['Class not found'] })
    }

    const classAttendeesSoql = `Select Id,Student__r.Id, Student__r.Name,Student__r.Allergies__c, Student__r.Allergy_Info__c,Student__r.PersonEmail,Student__r.Photo__c, Parent_Account__r.Name, Parent_Account__r.PersonEmail,Parent_Account__r.Photo__c from Registration__c where Class_and_Event__c = '${classId}'`
    const classAttendees = (await conn.query(classAttendeesSoql)).records

    const classAttendeesResult = classAttendees.map((attendee: any) => {
      return {
        id: attendee.Student__r?.Id,
        name: attendee.Student__r?.Name,
        email: attendee.Student__r?.PersonEmail,
        photo: attendee.Student__r?.Photo__c,
        allergies: attendee.Student__r?.Allergies__c,
        allergyInfo: attendee.Student__r?.Allergy_Info__c,
        parentName: attendee.Parent_Account__r?.Name,
        parentEmail: attendee.Parent_Account__r?.PersonEmail,
        parentPhoto: attendee.Parent_Account__r?.Photo__c,
      }
    })

    const pastMeetingsSoql = `Select Id, Starts_On__c,Ends_On__c,Meeting_Time__c,Headcount_based_attendance__c, Attendance_Marked__c, Duration__c, Venue__r.Name,Teacher__r.Id,Teacher__r.Name,Teacher__r.Photo__c from Meeting__c where Class_and_Event__c = '${classId}' AND Ends_On__c < ${jsforce.Date.TODAY}`
    const pastMeetings = (await conn.query(pastMeetingsSoql)).records

    const upcomingMeetingsSoql = `Select Id, Starts_On__c,Ends_On__c,Meeting_Time__c,Headcount_based_attendance__c, Attendance_Marked__c, Duration__c, Venue__r.Name,Teacher__r.Id,Teacher__r.Name,Teacher__r.Photo__c from Meeting__c where Class_and_Event__c = '${classId}' AND Ends_On__c >= ${jsforce.Date.TODAY}`
    const upcomingMeetings = (await conn.query(upcomingMeetingsSoql)).records

    const [
      classPrequisiteBadges,
      classAwardedBadges,
      associatedTeachers,
      venue,
      classMailingLists,
    ] = await Promise.all([
      getClassesBadgesByTypeServer(classId, 'Prerequisite'),
      getClassesBadgesByTypeServer(classId, 'Award'),
      getAssociatedTeachersForClassByIdServer(classId),
      getClassVenueServer(classDetails.Venue__c),
      getClassMailingListsServer(classId),
    ])
    const result = {
      ...classDetails,
      classAttendees: classAttendeesResult,
      pastMeetings,
      upcomingMeetings,
      associatedTeachers,
      prerequisiteBadges: classPrequisiteBadges,
      awardedBadges: classAwardedBadges,
      venue,
      classMailingLists,
    }

    return responseGenerator(true, result)
  } catch (error) {
    console.error('Error getting class details:', error)
    return responseGenerator(false, { errors: ['Error getting class details'] })
  }
}

export const markAttendance = async (
  accessToken: string,
  meetingId: string,
  attendanceArgs: AttendanceArgs[],
  attendanceNotes: string,
  subjectOfClass?: string,
  practicalApplications?: string,
) => {
  try {
    if (!(await verifyCustomAccountSession(accessToken))) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const accessTokenUserId = await getAccountId(accessToken)

    if (!accessTokenUserId) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const roles = await getAccountRolesServer(accessTokenUserId)

    if (!roles.includes(ROLES.Teacher)) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const conn = await connectToSalesforce()

    const meeting = await conn.sobject('Meeting__c').retrieve(meetingId)

    if (!meeting) {
      return responseGenerator(false, { errors: ['Meeting not found'] })
    }

    attendanceArgs.forEach(async (attendance) => {
      const studentAttendance = {
        Meeting__c: meetingId,
        Attendee_Name__c: attendance.studentId,
        Status__c: attendance.attendenceType,
      }
      // check if the student is already marked as present
      const existingAttendance = await conn.sobject('Attendee__c').find({
        Meeting__c: meetingId,
        Attendee_Name__c: attendance.studentId,
      })

      if (existingAttendance.length) {
        await conn.sobject('Attendee__c').update({
          Id: existingAttendance[0].Id!,
          Status__c: attendance.attendenceType,
        })
      } else {
        await conn.sobject('Attendee__c').create(studentAttendance)
      }
    })

    await conn.sobject('Meeting__c').update({
      Id: meetingId,
      Attendance_Notes__c: attendanceNotes,
      Subject_of_Class__c: subjectOfClass,
      Practical_Applications__c: practicalApplications,
    })

    return responseGenerator(true, null)
  } catch (error) {
    return responseGenerator(false, { errors: ['Error marking attendance'] })
  }
}

export const getTeacherClassesByDate = async (
  accessToken: string,
  startDate: string,
  endDate: string,
  classCategory: 'All' | 'Adult Class' | 'Youth Class',
) => {
  const from = startOfDay(startDate)
  const to = endOfDay(endDate)

  const recordTypeIds = []
  try {
    if (!(await verifyCustomAccountSession(accessToken))) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const accessTokenUserId = await getAccountId(accessToken)

    if (!accessTokenUserId) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const roles = await getAccountRolesServer(accessTokenUserId)

    if (!roles.includes(ROLES.Teacher)) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const cacheKey = await redisClient.getCacheKey(
      'getTeacherClassesByDate',
      accessTokenUserId,
      startDate,
      endDate,
      classCategory,
    )

    const cachedData = await redisClient.get(cacheKey)
    // cache hit
    if (cachedData) {
      return JSON.parse(cachedData)
    }

    if (classCategory === 'Adult Class') {
      recordTypeIds.push(
        await getRecordTypeByName('Adult_Class', 'Class_Event_Club__c'),
      )
    } else if (classCategory === 'Youth Class') {
      recordTypeIds.push(
        await getRecordTypeByName('Youth_Class', 'Class_Event_Club__c'),
      )
    } else {
      recordTypeIds.push(
        await getRecordTypeByName('Youth_Class', 'Class_Event_Club__c'),
      )
      recordTypeIds.push(
        await getRecordTypeByName('Adult_Class', 'Class_Event_Club__c'),
      )
    }

    const conn = await connectToSalesforce()
    const soql = `SELECT Id,Meeting_Time__c, Venue__r.Name, Duration__c,  Class_and_Event__r.Title__c, Class_and_Event__r.Id,Class_and_Event__r.Banner_Image__c,Class_and_Event__r.Category__c, Class_and_Event__r.Level__c, Class_and_Event__r.Type__c, Class_and_Event__r.Total_Seats__c, Class_and_Event__r.Booked_Seats__c,Starts_On__c, Ends_On__c FROM Meeting__c WHERE Ends_On__c >= ${new Date(from).toISOString()} AND Ends_On__c <= ${new Date(to).toISOString()} AND Class_and_Event__r.RecordTypeId in ${arrayToSqlInQuery(recordTypeIds)} AND Class_and_Event__r.Visible__c = true AND Teacher__c = '${accessTokenUserId}' ORDER BY Starts_On__c`

    const classes = (await conn.query(soql)).records

    const returnData = classes.map((item) => {
      return {
        classId: item.Class_and_Event__r.Id,
        classBannerImage: item.Class_and_Event__r.Banner_Image__c,
        classTitle: item.Class_and_Event__r.Title__c,
        classCategory: item.Class_and_Event__r.Category__c,
        classType: item.Class_and_Event__r.Type__c,
        classLevel: item.Class_and_Event__r.Level__c,
        totalSeats: item.Class_and_Event__r.Total_Seats__c,
        bookedSeats: item.Class_and_Event__r.Booked_Seats__c,
        meetingId: item.Id,
        meetingTime: item.Meeting_Time__c,
        meetingLocation: item.Venue__r?.Name || '',
        meetingDuration: item.Duration__c,
        startDate: item.Starts_On__c,
        startTime: item.Starts_On__c,
        endTime: item.Ends_On__c,
      }
    })

    const response = responseGenerator(true, returnData)
    await redisClient.set(cacheKey, JSON.stringify(response), { EX: 300 })
    return response
  } catch (err) {
    console.log('Error: ', err)
    return responseGenerator(false, {
      errors: ['error getting teacher classes by date'],
    })
  }
}

export const getMeetingAttendance = async (
  accessToken: string,
  meetingId: string,
) => {
  try {
    if (!(await verifyCustomAccountSession(accessToken))) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const accessTokenUserId = await getAccountId(accessToken)

    if (!accessTokenUserId) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const roles = await getAccountRolesServer(accessTokenUserId)

    if (!roles.includes(ROLES.Teacher)) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const conn = await connectToSalesforce()

    const meeting = await conn.sobject('Meeting__c').retrieve(meetingId)

    if (!meeting) {
      return responseGenerator(false, { errors: ['Meeting not found'] })
    }

    const meetingAttendanceSoql = `Select Id,Status__c, Attendee_Name__r.Name, Attendee_Name__r.PersonEmail, Attendee_Name__r.Id, Meeting__c from Attendee__c where Meeting__c ='${meetingId}' AND Role__c !='Teacher/Host/Performer'`
    const meetingAttendance = (await conn.query(meetingAttendanceSoql)).records

    const result = meetingAttendance.map((attendance: any) => {
      return {
        id: attendance.Attendee_Name__r.Id,
        name: attendance.Attendee_Name__r.Name,
        email: attendance.Attendee_Name__r.PersonEmail,
        status: attendance.Status__c,
      }
    })

    const returnData = {
      data: result,
      notes: meeting.Attendance_Notes__c,
      subjectOfClass: meeting.Subject_of_Class__c,
      practicalApplications: meeting.Practical_Applications__c,
    }

    return responseGenerator(true, returnData)
  } catch (error) {
    return responseGenerator(false, { errors: ['Error getting attendance'] })
  }
}

export const getMeetingManualAttendance = async (
  accessToken: string,
  meetingId: string,
) => {
  try {
    if (!(await verifyCustomAccountSession(accessToken))) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const accessTokenUserId = await getAccountId(accessToken)

    if (!accessTokenUserId) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const roles = await getAccountRolesServer(accessTokenUserId)

    if (!roles.includes(ROLES.Teacher)) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const conn = await connectToSalesforce()

    const meeting = await conn.sobject('Meeting__c').retrieve(meetingId)

    if (!meeting) {
      return responseGenerator(false, { errors: ['Meeting not found'] })
    }

    const meetingAttendanceSoql = `SELECT Id, Present_In_Person_Adult_Manual__c, Present_In_Person_Youth_Manual__c, Present_Online_Adult_Manual__c, Present_Online_Youth_Manual__c,Paid_Teacher_In_Person__c,Unpaid_Teacher_In_Person__c,Paid_Teacher_Online__c,Unpaid_Teacher_Online__c, Attendance_Notes__c, Subject_of_Class__c, Practical_Applications__c FROM Meeting__c WHERE Id='${meetingId}'`
    const meetingAttendance = (await conn.query(meetingAttendanceSoql)).records

    const result = meetingAttendance.map((attendance: any) => {
      return {
        meeting_id: attendance.id,
        present_in_person_adult:
          attendance.Present_In_Person_Adult_Manual__c ?? 0,
        present_online_adult: attendance.Present_Online_Adult_Manual__c ?? 0,
        present_in_person_youth:
          attendance.Present_In_Person_Youth_Manual__c ?? 0,
        present_online_youth: attendance.Present_Online_Youth_Manual__c ?? 0,
        present_online_paid_teacher: attendance.Paid_Teacher_Online__c ?? 0,
        present_online_unpaid_teacher: attendance.Unpaid_Teacher_Online__c ?? 0,
        present_in_person_paid_teacher:
          attendance.Paid_Teacher_In_Person__c ?? 0,
        present_in_person_unpaid_teacher:
          attendance.Unpaid_Teacher_In_Person__c ?? 0,
        attendance_notes: attendance.Attendance_Notes__c,
        subject_of_class: attendance.Subject_of_Class__c,
        practical_applications: attendance.Practical_Applications__c,
      }
    })

    return responseGenerator(true, result)
  } catch (error) {
    return responseGenerator(false, { errors: ['Error getting attendance'] })
  }
}

export const getTodoList = async (accessToken: string) => {
  try {
    if (!(await verifyCustomAccountSession(accessToken))) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }
    const accessTokenUserId = await getAccountId(accessToken)
    if (!accessTokenUserId) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }
    const roles = await getAccountRolesServer(accessTokenUserId)
    if (!roles.includes(ROLES.Teacher)) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }
    const currentTime = new Date()
    const etTime = convertTimestampToET(currentTime)
    const conn = await connectToSalesforce()
    const soql = `SELECT Id,Class_and_Event__r.Id, Class_and_Event__r.Title__c, Starts_On__c,Ends_On__c,Meeting_Time__c,Headcount_based_attendance__c, Duration__c, Venue__r.Name,Teacher__r.Name,Teacher__r.Photo__c from Meeting__c  WHERE Attendance_Marked__c=false AND Ends_On__c < ${etTime} AND Teacher__c = '${accessTokenUserId}'`

    const classRecords = (await conn.query(soql)).records

    const classIdToClassAttendeesMap: Record<string, any> = {}

    for (const classItem of classRecords) {
      const classId = classItem.Class_and_Event__r.Id
      if (classIdToClassAttendeesMap.hasOwnProperty(classId)) continue
      const classAttendees = await getClassAttendeesServer(classId)
      classIdToClassAttendeesMap[classId] = classAttendees
    }

    const returnData = classRecords.map((record) => {
      return {
        ...record,
        classAttendees:
          classIdToClassAttendeesMap[record.Class_and_Event__r.Id],
      }
    })
    return responseGenerator(true, returnData)
  } catch (error) {
    return responseGenerator(false, { errors: ['error getting the todo list'] })
  }
}

export const markManualAttendance = async (
  accessToken: string,
  meetingId: string,
  adultInPerson: number,
  youthInPerson: number,
  adultOnline: number,
  youthOnline: number,
  paidTeacherInPerson: number,
  paidTeacherOnline: number,
  unpaidTeacherInPerson: number,
  unpaidTeacherOnline: number,
  attendanceNotes: string,
  subjectOfClass?: string,
  practicalApplications?: string,
) => {
  try {
    if (!(await verifyCustomAccountSession(accessToken))) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const accessTokenUserId = await getAccountId(accessToken)

    if (!accessTokenUserId) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const roles = await getAccountRolesServer(accessTokenUserId)

    if (!roles.includes(ROLES.Teacher)) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const conn = await connectToSalesforce()

    const meeting = await conn.sobject('Meeting__c').retrieve(meetingId)

    if (!meeting) {
      return responseGenerator(false, { errors: ['Meeting not found'] })
    }

    await conn.sobject('Meeting__c').update({
      Id: meeting.Id!,
      Present_In_Person_Adult_Manual__c: adultInPerson,
      Present_In_Person_Youth_Manual__c: youthInPerson,
      Present_Online_Adult_Manual__c: adultOnline,
      Present_Online_Youth_Manual__c: youthOnline,
      Paid_Teacher_In_Person__c: paidTeacherInPerson,
      Paid_Teacher_Online__c: paidTeacherOnline,
      Unpaid_Teacher_In_Person__c: unpaidTeacherInPerson,
      Unpaid_Teacher_Online__c: unpaidTeacherOnline,
      Attendance_Notes__c: attendanceNotes,
      Subject_of_Class__c: subjectOfClass,
      Practical_Applications__c: practicalApplications,
    })

    return responseGenerator(true, null)
  } catch (error) {
    return responseGenerator(false, { errors: ['Error getting attendance'] })
  }
}

export const sendNotification = async (
  accessToken: string,
  mailingListId: string,
  subject: string,
  content: string,
) => {
  try {
    if (!(await verifyCustomAccountSession(accessToken))) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const accessTokenUserId = await getAccountId(accessToken)

    if (!accessTokenUserId) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const roles = await getAccountRolesServer(accessTokenUserId)

    if (!roles.includes(ROLES.Teacher)) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const conn = await connectToSalesforce()
    const payload = {
      senderId: accessTokenUserId,
      mailingListId,
      subject,
      content,
    }
    await conn.requestPost('/services/apexrest/notifyStudents', payload)

    return responseGenerator(true, null)
  } catch (error) {
    return responseGenerator(false, { errors: ['Error sending notification'] })
  }
}

export const sendNotificationIndividual = async (
  accessToken: string,
  email: string,
  subject: string,
  content: string,
) => {
  try {
    if (!(await verifyCustomAccountSession(accessToken))) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const accessTokenUserId = await getAccountId(accessToken)

    if (!accessTokenUserId) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const roles = await getAccountRolesServer(accessTokenUserId)

    if (!roles.includes(ROLES.Teacher)) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const conn = await connectToSalesforce()
    const payload = {
      senderId: accessTokenUserId,
      recipientEmails: [email],
      subject,
      content,
    }
    await conn.requestPost('/services/apexrest/notifyStudents', payload)

    return responseGenerator(true, null)
  } catch (error) {
    return responseGenerator(false, { errors: ['Error sending notification'] })
  }
}

export const getTeacherStats = async (accessToken: string) => {
  try {
    if (!(await verifyCustomAccountSession(accessToken))) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const accessTokenUserId = await getAccountId(accessToken)

    if (!accessTokenUserId) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const roles = await getAccountRolesServer(accessTokenUserId)

    if (!roles.includes(ROLES.Teacher)) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const conn = await connectToSalesforce()

    const payload = {
      accountId: accessTokenUserId,
      type: 'Teacher',
    }

    const response = (await conn.requestPost(
      '/services/apexrest/AccountStats',
      payload,
    )) as any
    const parsedResponse = JSON.parse(response)
    return responseGenerator(true, parsedResponse.teacherStats)
  } catch (error) {
    return responseGenerator(false, { errors: ['Error getting teacher stats'] })
  }
}

export const getTeacherDetailsById = async (teacherId: string) => {
  try {
    const roles = await getAccountRolesServer(teacherId)

    if (!roles.includes(ROLES.Teacher)) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const conn = await connectToSalesforce()

    const teacherDetailSoql = `SELECT Id,Name,Photo__c,Description,YouTube__c, Facebook__c, Instagram__c, Twitter__c, Substack__c, Website, Teacher_Type__c FROM Account where Id='${teacherId}'`
    const teacherDetail = (await conn.query(teacherDetailSoql)).records[0]

    const payload = {
      accountId: teacherId,
      type: 'Teacher',
    }

    const response = (await conn.requestPost(
      '/services/apexrest/AccountStats',
      payload,
    )) as any
    const parsedResponse = JSON.parse(response)

    const publishedWorksSoql = `SELECT Id, Title__c, Thumbnail_URL__c, Link__c  FROM Published_Work__c WHERE Author__c ='${teacherId}'`
    const publishedWorks = (await conn.query(publishedWorksSoql)).records

    if (!teacherDetail) {
      throw new Error('Teacher details not found')
    }

    const returnData = {
      ...teacherDetail,
      teacherStats: parsedResponse.teacherStats,
      publishedWorks: publishedWorks,
    }

    return responseGenerator(true, returnData)
  } catch (error) {
    return responseGenerator(false, {
      errors: ['Error getting teacher details'],
    })
  }
}

export const getAllTeacherMeetings = async (
  accessToken: string,
  startDate: string,
  endDate: string,
) => {
  try {
    if (!(await verifyCustomAccountSession(accessToken))) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const accessTokenUserId = await getAccountId(accessToken)

    if (!accessTokenUserId) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const roles = await getAccountRolesServer(accessTokenUserId)

    if (!roles.includes(ROLES.Teacher)) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }
    const meetingData = await getAllTeacherMeetingsByIdServer(
      accessTokenUserId,
      startDate,
      endDate,
    )
    return responseGenerator(true, meetingData)
  } catch (error) {
    return responseGenerator(false, {
      errors: [error instanceof Error ? error.message : String(error)],
    })
  }
}

export const getAllTeacherMeetingsById = async (
  teacherId: string,
  startDate: string,
  endDate: string,
) => {
  try {
    const roles = await getAccountRolesServer(teacherId)

    if (!roles.includes(ROLES.Teacher)) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }
    const meetingData = await getAllTeacherMeetingsByIdServer(
      teacherId,
      startDate,
      endDate,
    )
    return responseGenerator(true, meetingData)
  } catch (error) {
    return responseGenerator(false, {
      errors: ['error getting all the meetings'],
    })
  }
}
