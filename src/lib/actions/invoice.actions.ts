'use server'

import jsforce from 'jsforce'

import { createPaymentToken, DecodedPaymentToken } from '@/lib/jwt'
import { connectToSalesforce } from '@/lib/salesforce'
import { IPaymentToken } from '@/models/paymentToken.model'
import { PaymentType } from '@/utils/constants'
import { responseGenerator } from '@/utils/utils'
import { createPaymentTokenSF } from '@/lib/actions/payment.actions'

export const processIvoice = async (invoiceId: string) => {
  try {
    const conn = await connectToSalesforce()
    const invoiceSoql = `SELECT Id,Account__c,  Net_Total__c FROM Invoice__c WHERE Due_Date__c >= ${jsforce.Date.TODAY} AND Status__c ='Draft' AND Id = '${invoiceId}'`

    const records = (await conn.query(invoiceSoql)).records
    if (records.length === 0) {
      return responseGenerator(false, {
        errors: ['invalid invoiceId'],
      })
    }

    const invoiceObject = {
      id: records[0].Id!,
      userId: records[0].Account__c,
      amount: records[0].Net_Total__c,
    }

    const paymentType: PaymentType = 'One-time'

    const paymentTokenData: DecodedPaymentToken = {
      userId: invoiceObject.userId,
      entityId: invoiceObject.id,
      entityType: 'Invoice',
      amount: invoiceObject.amount,
      walletBalance: 0,
      paymentType: 'One-time',
      isWalletEnabled: true,
    }

    const paymentJwtToken = createPaymentToken(paymentTokenData)

    //create a new payment token for the user
    const paymentToken: IPaymentToken = {
      userId: invoiceObject.userId,
      token: paymentJwtToken,
      status: 'active',
      amount: invoiceObject.amount,
      paymentType: paymentType,
      occurrences: paymentType === 'One-time' ? 1 : 9999,
    }

    await createPaymentTokenSF(paymentToken)

    const returnData = {
      token: paymentJwtToken,
    }

    return responseGenerator(true, returnData)
  } catch (error) {
    console.log('Error: ', error)
    return responseGenerator(false, {
      errors: ['error processing invoice'],
    })
  }
}
