'use server'
import { connectToSalesforce } from '@/lib/salesforce'

export const getBanners = async () => {
  const conn = await connectToSalesforce()

  const soql = `SELECT Id,Slug__c, Public_URL__c FROM Banner__c`

  const records = (await conn.query(soql)).records

  const returnData = records.map((record) => {
    return {
      id: record.Id,
      slug: record.Slug__c,
      imageUrl: record.Public_URL__c,
    }
  })

  return returnData
}
