'use server'
import jsforce from 'jsforce'
import { connectToSalesforce } from '@/lib/salesforce'
import { responseGenerator } from '@/utils/utils'

import { getAccountId } from '@/lib/actions/account.actions'
import { verifyCustomAccountSession } from '@/lib/actions/login.actions'
import { IPaymentToken } from '@/models/paymentToken.model'
import { PaymentType } from '@/utils/constants'
import { createPaymentToken, DecodedPaymentToken } from '@/lib/jwt'
import { createPaymentTokenSF } from '@/lib/actions/payment.actions'

type TransactionType = 'All' | 'Credit' | 'Debit'

export const addMoneyToWalletByIdServer = async (
  userId: string,
  amount: number,
) => {
  const userWalletBalance = await getWalletBalanceServer(userId)
  const paymentType: PaymentType = 'One-time' //TODO: hardcoding it for now

  const paymentTokenData: DecodedPaymentToken = {
    userId: userId,
    entityId: userId,
    entityType: 'Wallet',
    amount,
    walletBalance: userWalletBalance,
    paymentType: 'One-time',
    isWalletEnabled: false, //wallet diabled for wallet recharge
  }

  const paymentJwtToken = createPaymentToken(paymentTokenData)

  //create a new payment token for the user
  const paymentToken: IPaymentToken = {
    userId: userId,
    token: paymentJwtToken,
    status: 'active',
    amount: amount,
    paymentType,
    occurrences: paymentType === 'One-time' ? 1 : 9999, //9999 for ongoing subscription
  }

  await createPaymentTokenSF(paymentToken)

  return paymentJwtToken
}

export const getWalletHistory = async (
  accessToken: string,
  transactionType: TransactionType,
  startDate: string,
  endDate: string,
  limit = 10,
  offset = 0,
) => {
  if (!(await verifyCustomAccountSession(accessToken))) {
    return responseGenerator(false, { errors: ['Unauthorized'] }, true)
  }

  const accessTokenUserId = await getAccountId(accessToken)
  const sfStartDate = jsforce.Date.toDateTimeLiteral(startDate)
  const sfEndDate = jsforce.Date.toDateTimeLiteral(endDate)

  const conn = await connectToSalesforce()
  let soql = `SELECT Id,Account__c, Amount__c, Type__c, CreatedDate from Transaction__c where Account__c = '${accessTokenUserId}' AND CreatedDate >= ${sfStartDate} AND createdDate <= ${sfEndDate} ORDER BY CreatedDate DESC NULLS LAST LIMIT ${limit} OFFSET ${offset}`
  let countSoql = `SELECT Id from Transaction__c where Account__c = '${accessTokenUserId}' AND CreatedDate >= ${sfStartDate} AND createdDate <= ${sfEndDate}`

  switch (transactionType) {
    case 'Credit':
      soql = `SELECT Id,Account__c, Amount__c, Type__c, CreatedDate from Transaction__c where Account__c = '${accessTokenUserId}' AND Type__c = 'Credit' AND createdDate >= ${sfStartDate} AND createdDate <= ${sfEndDate} ORDER BY CreatedDate DESC NULLS LAST LIMIT ${limit} OFFSET ${offset}`
      countSoql = `SELECT Id from Transaction__c where Account__c = '${accessTokenUserId}' AND Type__c = 'Credit' AND CreatedDate >= ${sfStartDate} AND createdDate <= ${sfEndDate}`
      break
    case 'Debit':
      soql = `SELECT Id,Account__c, Amount__c, Type__c, CreatedDate from Transaction__c where Account__c = '${accessTokenUserId}' AND Type__c = 'Debit' AND createdDate >= ${sfStartDate} AND createdDate <= ${sfEndDate} ORDER BY CreatedDate DESC NULLS LAST LIMIT ${limit} OFFSET ${offset}`
      countSoql = `SELECT Id from Transaction__c where Account__c = '${accessTokenUserId}' AND Type__c = 'Debit' AND CreatedDate >= ${sfStartDate} AND createdDate <= ${sfEndDate}`
      break
    default:
      break
  }

  const response = (await conn.query(soql)).records
  const countResponse = (await conn.query(countSoql)).totalSize
  const transactions = response.map((record: any) => {
    return {
      id: record.Id,
      amount: record.Amount__c,
      type: record.Type__c,
      createdDate: record.CreatedDate,
    }
  })

  const data = {
    data: transactions,
    total: countResponse,
  }

  return responseGenerator(true, data)
}

export const getWalletBalance = async (accessToken: string) => {
  if (!(await verifyCustomAccountSession(accessToken))) {
    return responseGenerator(false, { errors: ['Unauthorized'] }, true)
  }

  const accessTokenUserId = await getAccountId(accessToken)

  let soql = `SELECT Account__c, SUM(Amount__c) balance from Transaction__c where Account__c = '${accessTokenUserId}' GROUP by Account__c`

  const conn = await connectToSalesforce()
  const response = (await conn.query(soql)).records[0]
  const balance = response ? response.balance : 0

  return responseGenerator(true, { balance })
}

export const getWalletBalanceServer = async (
  userId: string,
): Promise<number> => {
  let soql = `SELECT Account__c, SUM(Amount__c) balance from Transaction__c where Account__c = '${userId}' GROUP by Account__c`

  const conn = await connectToSalesforce()
  const response = (await conn.query(soql)).records[0]
  const balance = response ? response.balance : 0

  return balance
}

export const addModeyToWallet = async (accessToken: string, amount: number) => {
  try {
    if (!(await verifyCustomAccountSession(accessToken))) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const accessTokenUserId = await getAccountId(accessToken)

    if (!accessTokenUserId) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const paymentJwtToken = await addMoneyToWalletByIdServer(
      accessTokenUserId,
      amount,
    )

    const returnData = {
      token: paymentJwtToken,
    }

    return responseGenerator(true, returnData)
  } catch (error) {
    return responseGenerator(false, {
      errors: ['Failed to add money to wallet'],
    })
  }
}
