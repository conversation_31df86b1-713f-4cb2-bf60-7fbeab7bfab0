'use server'
import { connectToSalesforce } from '@/lib/salesforce'
import { StackAuthServerAxios } from '@/utils/axios'
import { getRecordTypeByName } from '@/lib/actions/salesforce.actions'
import {
  generateDummyEmailFromPhone,
  getPhoneFromDummyEmail,
  isAbove18,
  isDummyEmail,
  responseGenerator,
} from '@/utils/utils'
import { ROLES } from '@/types/enum'
import { verifyCustomAccountSession } from '@/lib/actions/login.actions'
import { stackServerApp } from '@/stack'
import { TEEN_APPLICATION_QUESTIONS } from '@/utils/constants'
import { getActiveMembershipByUserIdServer } from './membership.actions'

type AccountData = {
  Phone: string
  PersonEmail: string
  FirstName: string
  LastName: string
  PersonBirthdate: string
  RecordTypeId: string
  Photo__c: string
}

//GET ACCOUNTS
export const getAccountId = async (accessToken: string) => {
  const response = await StackAuthServerAxios.get('/users/me', {
    headers: {
      'x-stack-access-token': accessToken,
    },
  })
  const user = response.data

  let email = user?.primary_email?.toLowerCase()
  let phone = undefined

  if (isDummyEmail(email)) {
    phone = getPhoneFromDummyEmail(email)
    email = undefined
  }

  // const conn = await connectToSalesforce()
  // const account = await conn.sobject('Account').findOne({ PersonEmail: email })

  const account = await getAccountByPhoneOrEmailServer(phone, email)

  return account?.Id
}
export const getAccountDataByAccessTokenServer = async (
  accessToken: string,
) => {
  const accessTokenUserId = await getAccountId(accessToken)

  const conn = await connectToSalesforce()
  const query = { Id: accessTokenUserId }

  const account: any = await conn
    .sobject('Account')
    .findOne(query)
    .include('Roles__r')
    .where({
      Is_Active__c: true,
      IsDeleted: false,
    })
    .end()

  const returnData = {
    ...account,
  }

  const lastUsedRole = account.Last_Used_Role__c as ROLES

  if (lastUsedRole === ROLES.Teacher) {
    const totalClasses = await conn
      .sobject('Class_Event_Club__c')
      .count({ Teacher__c: account.Id })
    returnData.totalClasses = totalClasses
  }
  return returnData
}

export const getPicklistOptionsServer = async (
  sobject: string,
  fieldName: string,
) => {
  const conn = await connectToSalesforce()
  const fieldMetadata = await conn.sobject(sobject).describe()

  const multiPicklistField = fieldMetadata.fields.find(
    (field) => field.name === fieldName,
  )

  if (!multiPicklistField || !multiPicklistField.picklistValues) {
    throw new Error('Multi-picklist field not found or has no values')
  }

  const picklist = multiPicklistField.picklistValues.map((picklistValue) => {
    if (picklistValue.active) {
      return {
        label: picklistValue.label,
        value: picklistValue.value,
      }
    }
  })

  return picklist
}

export const getAccountRolesServer = async (accountId: string) => {
  const conn = await connectToSalesforce()
  const soql = `select Role_Type__c from Role__c where Account__c = '${accountId}'`
  const data = (await conn.query(soql)).records

  const roles = data.map((role) => role.Role_Type__c) as ROLES[]

  return roles
}

export const getAccountAwardedBadgesServer = async (accountId: string) => {
  const conn = await connectToSalesforce()
  const soql = `SELECT Badge__r.Rank__c, Badge__r.Name, Badge__r.Id,Badge__r.Image_URL__c FROM Awarded_Badge__c where Account__c = '${accountId}'`

  const data = (await conn.query(soql)).records

  const badges = data.map((item) => {
    return {
      id: item.Badge__r.Id,
      badgeName: item.Badge__r.Name,
      badgeRank: item.Badge__r.Rank__c,
      badgeImage: item.Badge__r.Image_URL__c,
    }
  })

  return badges
}

export const getAccountPublishedWorksServer = async (accountId: string) => {
  const conn = await connectToSalesforce()
  const soql = `SELECT Id, Title__c, Thumbnail_URL__c, Link__c FROM Published_Work__c WHERE Author__c = '${accountId}'`

  const data = (await conn.query(soql)).records

  return data
}

export const getAccountPrimaryEmail = async (accessToken: string) => {
  const response = await StackAuthServerAxios.get('/users/me', {
    headers: {
      'x-stack-access-token': accessToken,
    },
  })
  const user = response.data

  return user?.primary_email
}

export const getAccounts = async () => {
  const conn = await connectToSalesforce()
  const accounts = await conn.query('SELECT Id, Name FROM Account')
  return accounts
}

export const getAccountBadges = async (accessToken: string) => {
  try {
    if (!(await verifyCustomAccountSession(accessToken))) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const accessTokenUserId = await getAccountId(accessToken)

    if (!accessTokenUserId) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const userBadges = await getAccountAwardedBadgesServer(accessTokenUserId)

    return responseGenerator(true, userBadges)
  } catch (err) {
    console.log('Error: ', err)
    return responseGenerator(false, {
      errors: ['error getting account badges'],
    })
  }
}

export const getAccountStatsByIdServer = async (
  accountId: string,
  accountType: 'Adult Student' | 'Youth Student',
) => {
  const conn = await connectToSalesforce()
  const payload = {
    accountId: accountId,
    type: accountType,
  }
  const response = (await conn.requestPost(
    '/services/apexrest/AccountStats',
    payload,
  )) as any
  const parsedResponse = JSON.parse(response)
  return parsedResponse
}

export const getAccountStats = async (
  accessToken: string,
  dashboardType: 'Adult Student' | 'Youth Student',
) => {
  try {
    if (!(await verifyCustomAccountSession(accessToken))) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const accessTokenUserId = await getAccountId(accessToken)

    if (!accessTokenUserId) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const conn = await connectToSalesforce()
    let studentType = 'Adult Student'

    if (dashboardType === 'Youth Student') {
      studentType = 'Youth Student'
    }
    const payload = {
      accountId: accessTokenUserId,
      type: studentType,
    }
    const response = (await conn.requestPost(
      '/services/apexrest/AccountStats',
      payload,
    )) as any
    const parsedResponse = JSON.parse(response)
    return responseGenerator(true, parsedResponse.studentStats)
  } catch (err) {
    console.log('Error: ', err)
    return responseGenerator(false, {
      errors: ['error getting account stats'],
    })
  }
}

export const getAccountQuotes = async (
  type: 'Teacher' | 'Adult Student' | 'Youth Student' | 'Donor' | 'Parent',
) => {
  try {
    if (
      ![
        'Teacher',
        'Adult Student',
        'Youth Student',
        'Donor',
        'Parent',
      ].includes(type)
    ) {
      throw new Error('unkown dashboard type')
    }
    const conn = await connectToSalesforce()
    const soql = `SELECT Title__c, Content__c, Author__c, Background_Color__c, Important__c from Dashboard_Highlight__c WHERE Active__c = true AND ( Visible_for__c INCLUDES ('${type}') OR Visible_for__c = '') ORDER BY Important__c DESC`

    const records = (await conn.query(soql)).records
    return responseGenerator(true, records)
  } catch (err) {
    console.log('Error: ', err)
    return responseGenerator(false, {
      errors: ['error getting quotes'],
    })
  }
}

export const getAccountByPhoneOrEmail = async (
  phone?: string,
  email?: string,
) => {
  const conn = await connectToSalesforce()
  const query: {
    Phone?: string
    PersonEmail?: string
  } = {}
  if (phone) {
    query['Phone'] = phone
  }
  if (email) {
    // Convert email to lowercase for consistent querying
    query['PersonEmail'] = email.toLowerCase()
  }
  const account = await conn
    .sobject('Account')
    .findOne(query)
    .include('Roles__r')
    .where({
      Is_Active__c: true,
      IsDeleted: false,
    })
    .end()
  return responseGenerator(true, account)
}

export const getAccountByPhoneOrEmailServer = async (
  phone?: string,
  email?: string,
) => {
  const conn = await connectToSalesforce()
  const query: Record<string, any> = {}

  // Convert email to lowercase for consistent querying
  const normalizedEmail = email?.toLowerCase()

  if (phone && normalizedEmail) {
    query.$or = [{ Phone: phone }, { PersonEmail: normalizedEmail }]
  } else if (phone) {
    query.Phone = phone
  } else if (normalizedEmail) {
    query.PersonEmail = normalizedEmail
  }

  const account = await conn
    .sobject('Account')
    .findOne(query)
    .include('Roles__r')
    .where({
      Is_Active__c: true,
      IsDeleted: false,
    })
    .sort('CreatedDate', 'DESC') // deterministic: newest account first
    .end()

  // If we found an account, update phone if needed
  if (account && phone && account.Phone !== phone) {
    await conn.sobject('Account').update({
      Id: account.Id!,
      Phone: phone,
    })
    account.Phone = phone // reflect change in returned object
  }

  return account
}

export const getOrCreateAccountServer = async (
  data: Partial<AccountData>,
  phone?: string,
  email?: string,
) => {
  // Convert email to lowercase for Salesforce consistency
  const normalizedEmail = email?.toLowerCase()

  // Update the data object with lowercase email if email is provided
  if (data.PersonEmail) {
    data.PersonEmail = data.PersonEmail.toLowerCase()
  }

  let account = await getAccountByPhoneOrEmailServer(phone, normalizedEmail)
  let accountExistedBefore = true
  if (!account) {
    const conn = await connectToSalesforce()
    accountExistedBefore = false

    const result = await conn.sobject('Account').create(data)

    if (result.success) {
      account = await getAccountByPhoneOrEmailServer(phone, normalizedEmail)
    }
  }

  return { account, accountExistedBefore }
}

export const getParentAccountIdServer = async (id: string): Promise<string> => {
  const conn = await connectToSalesforce()
  const soql = `SELECT Id, Name, Parent_Account__c FROM Account WHERE id = '${id}'`

  const records = (await conn.query(soql)).records

  if (!records[0]) {
    throw new Error('parent account not found')
  }

  return records[0].Parent_Account__c!
}

export const getAccountById = async (accessToken: string, id: string) => {
  try {
    if (!(await verifyCustomAccountSession(accessToken))) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }
    const accessTokenUserId = await getAccountId(accessToken)

    if (!accessTokenUserId) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const conn = await connectToSalesforce()
    const query = { Id: accessTokenUserId }

    const account: any = await conn
      .sobject('Account')
      .findOne(query)
      .include('Roles__r')
      .where({
        Is_Active__c: true,
        IsDeleted: false,
      })
      .end()
      .include('Published_Works__r')
      .end()

    const badges = await getAccountAwardedBadgesServer(accessTokenUserId)
    const returnData = {
      ...account,
      userBadges: badges,
    }

    const lastUsedRole = account.Last_Used_Role__c as ROLES

    if (lastUsedRole === ROLES.Teacher) {
      const totalClasses = await conn
        .sobject('Class_Event_Club__c')
        .count({ Teacher__c: account.Id })
      returnData.totalClasses = totalClasses
    }

    return responseGenerator(true, returnData)
  } catch (err) {
    console.log('Error: ', err)
    return responseGenerator(false, { errors: ['Error getting account'] })
  }
}

export const getTeacherOboardingProgramOption = async () => {
  const picklist = await getPicklistOptionsServer(
    'Application__c',
    'Program__c',
  )

  return responseGenerator(true, picklist)
}

export const getTuitionAssistancePayOption = async () => {
  const picklist = await getPicklistOptionsServer(
    'Application__c',
    'Requested_Assistance__c',
  )

  return responseGenerator(true, picklist)
}

export const getAuthorsOption = async () => {
  const picklist = await getPicklistOptionsServer(
    'Account',
    'Favorite_Authors__c',
  )

  return responseGenerator(true, picklist)
}

export const createTeenApplication = async (
  firstName: string,
  lastName: string,
  phone: string,
  email: string,
  pronouns: string,
  parentFirstName: string,
  parentLastName: string,
  parentPhone: string,
  parentEmail: string,
  zipCode: string,
  highSchool: string,
  dob: string,
  academicLevel: string,
  answers: string[],
  needTransport: boolean,
  attachmentURL: string,
) => {
  try {
    const conn = await connectToSalesforce()
    const { account } = await getOrCreateAccountServer(
      {
        FirstName: firstName,
        LastName: lastName,
        PersonBirthdate: dob,
        Phone: phone,
        PersonEmail: email,
        RecordTypeId: await getRecordTypeByName('PersonAccount', 'Account'),
      },
      phone,
      email,
    )

    const { account: parentAccount } = await getOrCreateAccountServer(
      {
        FirstName: parentFirstName,
        LastName: parentLastName,
        Phone: parentPhone,
        PersonEmail: parentEmail,
        RecordTypeId: await getRecordTypeByName('PersonAccount', 'Account'),
      },
      parentPhone,
      parentEmail,
    )

    //add parent to child account
    await conn.sobject('Account').update({
      Id: account?.Id!,
      Parent_Account__c: parentAccount?.Id!,
      BillingPostalCode: zipCode,
      ShippingPostalCode: zipCode,
      PersonPronouns: pronouns,
      High_School__c: highSchool,
      Academic_Level__c: academicLevel,
    })

    let application_reason = ''

    for (let i = 0; i < 3; i++) {
      const question = TEEN_APPLICATION_QUESTIONS[i]
      const answer = answers[i]

      application_reason += `${question}\n${answer}\n\n`
    }

    const newTeenApplication = {
      Account__c: account?.Id!,
      Reason__c: application_reason,
      Transportation_Required__c: needTransport,
      Attachment__c: attachmentURL,
      RecordTypeId: await getRecordTypeByName(
        'Teen_Writers_Fellowship_Application',
        'Application__c',
      ),
    }

    await conn.sobject('Application__c').create(newTeenApplication)
    return responseGenerator(true, null)
  } catch (err) {
    console.log('Error: ', err)
    return responseGenerator(false, {
      errors: ['error creating tuition application'],
    })
  }
}

export const createTuitionAssistanceApplication = async (
  firstName: string,
  lastName: string,
  email: string,
  phone: string,
  reason: string,
  percentPay: string,
) => {
  try {
    const conn = await connectToSalesforce()
    const { account } = await getOrCreateAccountServer(
      {
        FirstName: firstName,
        LastName: lastName,
        PersonEmail: email,
        Phone: phone,
        RecordTypeId: await getRecordTypeByName('PersonAccount', 'Account'),
      },
      phone,
      email,
    )

    const newTuitionAssistanceApplication = {
      Account__c: account?.Id!,
      Reason__c: reason,
      Requested_Assistance__c: percentPay,
      RecordTypeId: await getRecordTypeByName(
        'Tuition_Assistance_Application',
        'Application__c',
      ),
    }

    await conn.sobject('Application__c').create(newTuitionAssistanceApplication)
    return responseGenerator(true, null)
  } catch (err) {
    console.log('Error: ', err)
    return responseGenerator(false, {
      errors: ['error creating tuition application'],
    })
  }
}

export const createTeacherOnboardingApplication = async (
  firstName: string,
  lastName: string,
  program: string,
  email: string,
  phone: string,
  dob: string,
  addressLine1: string,
  city: string,
  state: string,
  country: string,
  zip: string,
  shippingAddressLine1: string,
  shippingCity: string,
  shippingState: string,
  shippingCountry: string,
  shippingZip: string,
  resumeS3link: string,
  classPitch: string,
) => {
  try {
    const conn = await connectToSalesforce()
    const { account } = await getOrCreateAccountServer(
      {
        FirstName: firstName,
        LastName: lastName,
        PersonEmail: email,
        Phone: phone,
        RecordTypeId: await getRecordTypeByName('PersonAccount', 'Account'),
        PersonBirthdate: dob,
      },
      phone,
      email,
    )

    await conn.sobject('Account').update({
      Id: account?.Id!,
      PersonMailingStreet: addressLine1,
      PersonMailingState: state,
      PersonMailingPostalCode: zip,
      PersonMailingCountry: country,
      PersonMailingCity: city,
      ShippingStreet: shippingAddressLine1,
      ShippingCity: shippingCity,
      ShippingState: shippingState,
      ShippingCountry: shippingCountry,
      ShippingPostalCode: shippingZip,
    })

    const newApplicationObject = {
      Account__c: account?.Id!,
      Program__c: program,
      Attachment__c: resumeS3link,
      RecordTypeId: await getRecordTypeByName(
        'Teaching_Application',
        'Application__c',
      ),
      Class_Pitch__c: classPitch,
    }

    await conn.sobject('Application__c').create(newApplicationObject)
    return responseGenerator(true, null)
  } catch (err) {
    console.log('Error: ', err)
    return responseGenerator(false, {
      errors: ['error creating onboarding application'],
    })
  }
}

export const createTeacherOnboarding = async (
  email: string,
  phone: string,
  nameOnAccunt: string,
  nameOnBank: string,
  routingNumber: string,
  accountNumber: string,
  w9Url: string,
  signatureUrl: string,
) => {
  try {
    const conn = await connectToSalesforce()

    const account = await getAccountByPhoneOrEmailServer(phone, email)
    if (!account) {
      return responseGenerator(false, {
        errors: ['account not found'],
      })
    }

    let soql = `SELECT Id, Account__c,Stage__c, Status__c,RecordType.Id, Record_Type_Name__c FROM Application__c WHERE Record_Type_Name__c = 'Teaching Application' AND Stage__c = 'Ready for Onboarding' AND Account__c = '${account.Id!}'`

    const records = (await conn.query(soql)).records
    if (records.length === 0) {
      return responseGenerator(false, {
        errors: ['no active application found'],
      })
    }

    const application = records[0]

    await conn.sobject('Application__c').update({
      Id: application.Id!,
      Status__c: 'In Review',
      Stage__c: 'Onboarding Form Completed',
    })

    await conn.sobject('Account').update({
      Id: account.Id!,
      DD_Name_on_Account__c: nameOnAccunt,
      DD_Name_of_Bank__c: nameOnBank,
      DD_Routing_Number__c: routingNumber,
      DD_Bank_Account_Number__c: accountNumber,
      W9_Form_URL__c: w9Url,
      Signature_Image_URL__c: signatureUrl,
    })
    return responseGenerator(true, null)
  } catch (err) {
    console.log('Error: ', err)
    return responseGenerator(false, {
      errors: ['error creating onboarding'],
    })
  }
}
//GET TEACHERS
export const getTeachers = async (
  searchTerm: string,
  genre?: string[],
  limit = 20,
  offset = 0,
) => {
  const conn = await connectToSalesforce()
  let soql = `SELECT Id, Name, Photo__c, Genre_Teacher__c FROM Account WHERE Id IN (SELECT Account__c FROM Role__c WHERE Role_Type__c = 'Teacher' AND Is_Active__c = true)`
  if (searchTerm.length) {
    soql += ` AND Name LIKE '%${searchTerm}%'`
  }
  if (genre && genre?.length > 0) {
    soql += ` AND Genre_Teacher__c INCLUDES ('${genre.join("','")}')`
  }
  soql += ` LIMIT ${limit} OFFSET ${offset}`

  let totalCountSoql = `SELECT COUNT(Id) FROM Account WHERE Id IN (SELECT Account__c FROM Role__c WHERE Role_Type__c = 'Teacher' AND Is_Active__c = true)`
  if (searchTerm.length) {
    totalCountSoql += ` AND Name LIKE '%${searchTerm}%'`
  }
  if (genre && genre?.length > 0) {
    totalCountSoql += ` AND Genre_Teacher__c INCLUDES ('${genre.join("','")}')`
  }

  const teachers = (await conn.query(soql)).records
  const totalTeachers = (await conn.query(totalCountSoql)).records[0].expr0
  const allTeachersDataWithClasses = await Promise.all(
    teachers.map(async (teacher) => {
      if (!teacher) return
      const teacherId = teacher.Id
      const classes = await conn.sobject('Class_Event_Club__c').find({
        Teacher__c: teacherId,
      })
      return { ...teacher, classes_count: classes.length }
    }),
  )

  return responseGenerator(true, {
    data: allTeachersDataWithClasses,
    total: totalTeachers,
  })
}

export const getTeacherById = async (teacherId: string) => {
  const conn = await connectToSalesforce()
  const soql = `SELECT Id, Name, Photo__c,Description, Genre_Teacher__c,Website,Instagram__c,Twitter__c,Facebook__c,Bio__c  FROM Account WHERE Id = '${teacherId}'`

  const teacher = await conn.query(soql)

  return responseGenerator(true, teacher.records[0])
}

export const getTeacherForClassServer = async (teacherId: string) => {
  if (!teacherId) return
  const conn = await connectToSalesforce()
  const soql = `SELECT Id, Name, Photo__c, Genre_Teacher__c FROM Account WHERE Id = '${teacherId}'`

  const teacher = await conn.query(soql)

  return teacher.records[0]
}

export const isEmailAlreadyExist = async (
  email: string,
  phone: string,
): Promise<boolean> => {
  try {
    const account = await getAccountByPhoneOrEmailServer(undefined, email)

    if (!account) {
      return false
    }

    //update account with phone
    const conn = await connectToSalesforce()
    await conn.sobject('Account').update({
      Id: account.Id!,
      Phone: phone,
    })
    return true
  } catch (error) {
    return false
  }
}

export const isPhoneAlreadyExist = async (phone: string) => {
  try {
    const account = await getAccountByPhoneOrEmailServer(phone)

    if (!account) {
      return responseGenerator(true, false)
    }

    return responseGenerator(true, true)
  } catch (err) {
    console.log('Error: ', err)
    return responseGenerator(false, false)
  }
}

export const triggerSendMagicLink = async (email: string) => {
  await sendMagicLink(email)
}

//CREATE ACCOUNT
export const createEmailAccount = async (
  accessToken: string,
  email: string,
  phone: string,
  firstName: string,
  lastName: string,
  dateOfBirth: string,
  photoURL?: string,
  parentFirstName?: string,
  parentLastName?: string,
  parentEmail?: string,
  parentPhone?: string,
  useParentAsEmergencyContact?: boolean,
  emergencyContactName?: string,
  emergencyContactEmail?: string,
  emergencyContactPhone?: string,
  emergencyContactRelation?: string,
) => {
  try {
    const conn = await connectToSalesforce()
    if (!(await verifyCustomAccountSession(accessToken))) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const accessTokenEmail = await getAccountPrimaryEmail(accessToken)
    if (accessTokenEmail !== email) {
      return responseGenerator(false, { errors: ['Unauthorized'] })
    }

    const { account } = await getOrCreateAccountServer(
      {
        FirstName: firstName,
        LastName: lastName,
        PersonBirthdate: dateOfBirth,
        PersonEmail: email,
        Phone: phone,
        Photo__c: photoURL,
        RecordTypeId: await getRecordTypeByName('PersonAccount', 'Account'),
      },
      phone,
      email,
    )

    const dateOfBirthData = dateOfBirth
    if (!isAbove18(dateOfBirthData)) {
      if (!parentFirstName || !parentLastName || !parentEmail || !parentPhone) {
        return responseGenerator(false, {
          errors: ['parent data not provided'],
        })
      }

      let parentAccount = await getAccountByPhoneOrEmailServer(
        parentPhone,
        parentEmail,
      )

      if (!parentAccount) {
        //create a parent account
        const newParentAccount = {
          PersonEmail: parentEmail,
          FirstName: parentFirstName,
          LastName: parentLastName,
          Phone: parentPhone,
          RecordTypeId: await getRecordTypeByName('PersonAccount', 'Account'),
        }

        const { account: sfParentAccount } = await getOrCreateAccountServer(
          newParentAccount,
          parentPhone,
          parentEmail,
        )

        parentAccount = sfParentAccount
      }

      //add parent to child account
      await conn.sobject('Account').update({
        Id: account?.Id!,
        Parent_Account__c: parentAccount?.Id!,
      })

      //add emergency contact details
      if (useParentAsEmergencyContact) {
        await conn.sobject('Account').update({
          Id: account?.Id!,
          Emergency_Contact_Email__c: parentEmail,
          Emergency_Contact_Name__c: parentFirstName + ' ' + parentLastName,
          Emergency_Contact_Phone__c: parentPhone,
          Emergency_Contact_Relationship__c: 'Parent',
        })
      } else {
        if (
          !emergencyContactName ||
          !emergencyContactEmail ||
          !emergencyContactPhone ||
          !emergencyContactRelation
        ) {
          return responseGenerator(false, {
            errors: ['parent data not provided'],
          })
        }

        await conn.sobject('Account').update({
          Id: account?.Id!,
          Emergency_Contact_Email__c: emergencyContactEmail,
          Emergency_Contact_Name__c: emergencyContactName,
          Emergency_Contact_Phone__c: emergencyContactPhone,
          Emergency_Contact_Relationship__c: emergencyContactRelation,
        })
      }
    }

    //update personal info reviewed field, and last used role
    await conn.sobject('Account').update({
      Id: account?.Id!,
      Personal_Information_Reviewed__c: true,
      PersonBirthdate: dateOfBirth,
      FirstName: firstName,
      LastName: lastName,
      Last_Used_Role__c: isAbove18(dateOfBirthData)
        ? 'Adult Student'
        : 'Youth Student',
    })

    const userBadges = await getAccountAwardedBadgesServer(account?.Id!)

    const returnData = {
      ...account,
      userBadges,
    }
    return responseGenerator(true, returnData)
  } catch (err) {
    console.log('Error: ', err)
    return responseGenerator(false, {
      errors: ['error while creating account'],
    })
  }
}

export const createPhoneAccount = async (
  phone: string,
  email: string,
  firstName: string,
  lastName: string,
  dateOfBirth: string,
  profilePicture?: string,
  parentFirstName?: string,
  parentLastName?: string,
  parentEmail?: string,
  parentPhone?: string,
  useParentAsEmergencyContact?: boolean,
  emergencyContactName?: string,
  emergencyContactEmail?: string,
  emergencyContactPhone?: string,
  emergencyContactRelation?: string,
) => {
  try {
    const conn = await connectToSalesforce()

    const { account, accountExistedBefore } = await getOrCreateAccountServer(
      {
        FirstName: firstName,
        LastName: lastName,
        PersonBirthdate: dateOfBirth,
        PersonEmail: email,
        Phone: phone,
        Photo__c: profilePicture,
        RecordTypeId: await getRecordTypeByName('PersonAccount', 'Account'),
      },
      phone,
      email,
    )

    const dateOfBirthData = dateOfBirth

    if (!isAbove18(dateOfBirthData)) {
      if (!parentFirstName || !parentLastName || !parentEmail || !parentPhone) {
        return responseGenerator(false, {
          errors: ['parent data not provided'],
        })
      }

      let parentAccount = await getAccountByPhoneOrEmailServer(
        parentPhone,
        parentEmail,
      )

      if (!parentAccount) {
        //create a parent account
        const newParentAccount = {
          PersonEmail: parentEmail,
          FirstName: parentFirstName,
          LastName: parentLastName,
          Phone: parentPhone,
          RecordTypeId: await getRecordTypeByName('PersonAccount', 'Account'),
        }

        const { account: sfParentAccount } = await getOrCreateAccountServer(
          newParentAccount,
          parentPhone,
          parentEmail,
        )

        parentAccount = sfParentAccount
      }

      //add parent to child account
      await conn.sobject('Account').update({
        Id: account?.Id!,
        Parent_Account__c: parentAccount?.Id!,
      })

      //add emergency contact details
      if (useParentAsEmergencyContact) {
        await conn.sobject('Account').update({
          Id: account?.Id!,
          Emergency_Contact_Email__c: parentEmail,
          Emergency_Contact_Name__c: parentFirstName + ' ' + parentLastName,
          Emergency_Contact_Phone__c: parentPhone,
          Emergency_Contact_Relationship__c: 'Parent',
        })
      } else {
        if (
          !emergencyContactName ||
          !emergencyContactEmail ||
          !emergencyContactPhone ||
          !emergencyContactRelation
        ) {
          return responseGenerator(false, {
            errors: ['parent data not provided'],
          })
        }

        await conn.sobject('Account').update({
          Id: account?.Id!,
          Emergency_Contact_Email__c: emergencyContactEmail,
          Emergency_Contact_Name__c: emergencyContactName,
          Emergency_Contact_Phone__c: emergencyContactPhone,
          Emergency_Contact_Relationship__c: emergencyContactRelation,
        })
      }
    }

    //create account in stack auth
    if (!accountExistedBefore) {
      await createCustomAccount(email, firstName + ' ' + lastName)
    }

    //update personal info reviewed field
    await conn.sobject('Account').update({
      Id: account?.Id!,
      Personal_Information_Reviewed__c: true,
      PersonBirthdate: dateOfBirth,
      FirstName: firstName,
      LastName: lastName,
      Last_Used_Role__c: isAbove18(dateOfBirthData)
        ? 'Adult Student'
        : 'Youth Student',
    })

    const userBadges = await getAccountAwardedBadgesServer(account?.Id!)

    const returnData = {
      ...account,
      userBadges,
    }

    return responseGenerator(true, returnData)
  } catch (err) {
    console.log('Error: ', err)
    return responseGenerator(false, {
      errors: ['error while creating account'],
    })
  }
}

export const signInByPhone = async (phone: string) => {
  try {
    const account = await getAccountByPhoneOrEmailServer(phone)
    if (!account) {
      //account not found
      return null
    }
    //if account exists, then it also exists in stack auth
    // STEP 1: Fetch account from salesforce
    let { PersonEmail: email } = account

    //If the email does not exists then use a dummy email
    if (!email) {
      email = generateDummyEmailFromPhone(phone)
    }

    // STEP 2: using the email in salesforce, create a session in stack auth
    const customSession = await createCustomAccountSession(email)
    // STEP 3: return the session tokens access and refresh

    const userBadges = await getAccountAwardedBadgesServer(account.Id!)
    const publishedWorks = await getAccountPublishedWorksServer(account.Id!)
    
    return { 
      ...account, 
      ...customSession, 
      userBadges,
      Published_Works__r: { records: publishedWorks }
    }
  } catch (err) {
    console.log('Error: ', err)
  }
}

export const signInByEmail = async (email: string) => {
  const account = await getAccountByPhoneOrEmailServer(
    undefined,
    email.toLowerCase(),
  )
  if (!account) {
    //account not found
    return null
  }

  const userBadges = await getAccountAwardedBadgesServer(account.Id!)
  const publishedWorks = await getAccountPublishedWorksServer(account.Id!)
  
  //if account exists, then return the account
  return { 
    ...account, 
    userBadges,
    Published_Works__r: { records: publishedWorks }
  }
}

//UPDATE ACCOUNT
export const updateAccountById = async (accessToken: string, data: any) => {
  if (!(await verifyCustomAccountSession(accessToken))) {
    return responseGenerator(false, { errors: ['Unauthorized'] }, true)
  }

  const conn = await connectToSalesforce()
  const accessTokenUserId = await getAccountId(accessToken)

  if (!accessTokenUserId) {
    return responseGenerator(false, { errors: ['Unauthorized'] }, true)
  }

  const dateToUpdate: any = {}
  if ('firstName' in data) dateToUpdate['FirstName'] = data.firstName
  if ('lastName' in data) dateToUpdate['LastName'] = data.lastName
  if ('dateOfBirth' in data) dateToUpdate['PersonBirthdate'] = data.dateOfBirth
  if ('PhotoUrl' in data) dateToUpdate['Photo__c'] = data.PhotoUrl
  if ('Last_Used_Role__c' in data) {
    dateToUpdate['Last_Used_Role__c'] = data.Last_Used_Role__c
  }
  if ('phone' in data) dateToUpdate['Phone'] = data.phone
  if ('emailNotificationsAllowed' in data) {
    dateToUpdate['Email_Notifications_Allowed__c'] =
      data.emailNotificationsAllowed == 'true' ? true : false
  }
  if ('billingCountry' in data)
    dateToUpdate['BillingCountry'] = data.billingCountry
  if ('billingState' in data) dateToUpdate['BillingState'] = data.billingState
  if ('billingCity' in data) dateToUpdate['BillingCity'] = data.billingCity
  if ('billingStreet' in data)
    dateToUpdate['BillingStreet'] = data.billingStreet
  if ('billingPostalCode' in data) {
    dateToUpdate['BillingPostalCode'] = data.billingPostalCode
  }
  if ('shippingCountry' in data) {
    dateToUpdate['ShippingCountry'] = data.shippingCountry
  }
  if ('shippingState' in data)
    dateToUpdate['ShippingState'] = data.shippingState
  if ('shippingCity' in data) dateToUpdate['ShippingCity'] = data.shippingCity
  if ('shippingStreet' in data)
    dateToUpdate['ShippingStreet'] = data.shippingStreet
  if ('shippingPostalCode' in data) {
    dateToUpdate['ShippingPostalCode'] = data.shippingPostalCode
  }
  if ('website' in data) dateToUpdate['Website'] = data.website
  if ('instagram' in data) dateToUpdate['Instagram__c'] = data.instagram
  if ('facebook' in data) dateToUpdate['Facebook__c'] = data.facebook
  if ('IsDeleted' in data) dateToUpdate['Marked_for_deletion__c'] = data.IsDeleted
  if ('twitter' in data) dateToUpdate['Twitter__c'] = data.twitter
  if ('youtube' in data) dateToUpdate['YouTube__c'] = data.youtube
  if ('substack' in data) dateToUpdate['Substack__c'] = data.substack
  if ('favoriteGenres' in data)
    dateToUpdate['Favorite_Genres__c'] = Array.isArray(data.favoriteGenres)
      ? data.favoriteGenres.join(';')
      : data.favoriteGenres
  if ('favoriteAuthors' in data)
    dateToUpdate['Favorite_Authors__c'] = Array.isArray(data.favoriteAuthors)
      ? data.favoriteAuthors.join(';')
      : data.favoriteAuthors
  if ('bio' in data) dateToUpdate['Bio__c'] = data.bio
  try {
    const updatedAccountResponse = await conn.sobject('Account').update({
      Id: accessTokenUserId,
      ...dateToUpdate,
    })

    for (const value in updatedAccountResponse) {
      if (updatedAccountResponse[value].success === false) {
        return responseGenerator(false, {
          errors: updatedAccountResponse[value].errors,
        })
      }
    }

    if ('publishedWorks' in data && Array.isArray(data.publishedWorks)) {
      try {
        const existingWorks = await conn.query(
          `SELECT Id, Title__c, Thumbnail_URL__c, Link__c FROM Published_Work__c WHERE Author__c = '${accessTokenUserId}'`
        )
        
        const existingWorksMap = new Map(existingWorks.records.map((work: any) => [work.Id, work]))
        const submittedWorkIds = new Set(data.publishedWorks.map((work: any) => work.id).filter(Boolean))
        
        const worksToUpdate = []
        const worksToCreate = []
        const worksToDelete = []
        
        // Find works to delete (existing works not in submitted array)
        for (const existingWork of existingWorks.records) {
          if (existingWork.Id && !submittedWorkIds.has(existingWork.Id)) {
            worksToDelete.push(existingWork.Id)
          }
        }
        
        // Process submitted works
        for (const work of data.publishedWorks) {
          if (work.id && existingWorksMap.has(work.id)) {
            worksToUpdate.push({
              Id: work.id,
              Title__c: work.title,
              Thumbnail_URL__c: work.image,
              Link__c: work.link || '',
            })
          } else {
            worksToCreate.push({
              Title__c: work.title,
              Thumbnail_URL__c: work.image,
              Link__c: work.link || '',
              Author__c: accessTokenUserId,
            })
          }
        }
        
        // Delete removed works
        if (worksToDelete.length > 0) {
          await conn.sobject('Published_Work__c').destroy(worksToDelete)
        }
        
        // Update existing works
        if (worksToUpdate.length > 0) {
          await conn.sobject('Published_Work__c').update(worksToUpdate)
        }
        
        // Create new works
        if (worksToCreate.length > 0) {
          await conn.sobject('Published_Work__c').create(worksToCreate)
        }
        
      } catch (error) {
        console.error('Error handling published works:', error)
      }
    }

    return responseGenerator(true, updatedAccountResponse)
  } catch (error) {
    console.log('Error while updating account: ', error)
    return responseGenerator(false, { errors: ['Error updating account'] })
  }
}

// STACK AUTH
const sendMagicLink = async (email: string) => {
  const response = await StackAuthServerAxios.post(
    '/auth/otp/send-sign-in-code',
    {
      email: email,
      callback_url: process.env.APP_BASE_URL + '/handler/magic-link-callback',
    },
  )

  return response.data
}

const createCustomAccount = async (email: string, fullName: string) => {
  const response = await StackAuthServerAxios.post('/users', {
    display_name: fullName,
    primary_email: email,
    primary_email_verified: false,
    primary_email_auth_enabled: true,
    password: process.env.STACK_CUSTOM_PASSWORD,
  })
  return response.data
}

const createCustomAccountWithoutFullName = async (email: string) => {
  const response = await StackAuthServerAxios.post('/users', {
    primary_email: email,
    primary_email_verified: false,
    primary_email_auth_enabled: true,
    password: process.env.STACK_CUSTOM_PASSWORD,
  })
  return response.data
}

const getStackAuthUserData = async (email: string) => {
  const allUsersResponse = await StackAuthServerAxios.get(
    `/users?limit=1&query=${email}`,
  )

  const allUserData = allUsersResponse.data.items
  const user = allUserData.find((user: any) => user.primary_email === email)
  return user
}

const createCustomAccountSession = async (email: string) => {
  let user = await getStackAuthUserData(email)
  if (!user) {
    await createCustomAccountWithoutFullName(email)
  }

  user = await getStackAuthUserData(email)

  const response = await StackAuthServerAxios.post('/auth/sessions', {
    user_id: user.id,
  })
  return response.data
}

export const handleEmailLogin = async () => {
  const userData: any = await stackServerApp.getUser()
  if (userData && userData?.primaryEmail) {
    const userSfData = await signInByEmail(userData?.primaryEmail)
    return userSfData
  }
  return null
}

export const getAuthorDetailsById = async (userId: string) => {
  try {
    const activeUserMemberships =
      await getActiveMembershipByUserIdServer(userId)

    if (activeUserMemberships.length === 0) {
      throw new Error('User has no active memberships')
    }

    const conn = await connectToSalesforce()

    const authorDetailSoql = `SELECT Id,Name,Photo__c,Description,YouTube__c, Facebook__c, Instagram__c, Twitter__c, Substack__c, Website, Teacher_Type__c,Favorite_Authors__c,Favorite_Genres__c FROM Account WHERE Id='${userId}'`
    const authorDetail = (await conn.query(authorDetailSoql)).records[0]

    if (!authorDetail) {
      throw new Error('Teacher details not found')
    }

    const userBadges = await getAccountAwardedBadgesServer(userId)

    const returnData = {
      ...authorDetail,
      userBadges,
    }

    return responseGenerator(true, returnData)
  } catch (error) {
    return responseGenerator(false, {
      errors: [error instanceof Error ? error.message : String(error)],
    })
  }
}

export const addParentAccount = async (
  accessToken: string,
  firstName: string,
  lastName: string,
  email: string,
  phone: string,
) => {
  try {
    if (!(await verifyCustomAccountSession(accessToken))) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const conn = await connectToSalesforce()
    const accessTokenUserId = await getAccountId(accessToken)

    if (!accessTokenUserId) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    let parentAccount = await getAccountByPhoneOrEmailServer(phone, email)

    if (!parentAccount) {
      const newParentAccount = {
        PersonEmail: email,
        FirstName: firstName,
        LastName: lastName,
        Phone: phone,
        RecordTypeId: await getRecordTypeByName('PersonAccount', 'Account'),
      }

      const { account: sfParentAccount } = await getOrCreateAccountServer(
        newParentAccount,
        phone,
        email,
      )

      parentAccount = sfParentAccount
    }

    await conn.sobject('Account').update({
      Id: accessTokenUserId,
      Parent_Account__c: parentAccount?.Id,
      Emergency_Contact_Email__c: email,
      Emergency_Contact_Name__c: firstName + ' ' + lastName,
      Emergency_Contact_Phone__c: phone,
      Emergency_Contact_Relationship__c: 'Parent',
    })
    return responseGenerator(true, parentAccount?.Id)
  } catch (error) {
    return responseGenerator(false, {
      errors: [error instanceof Error ? error.message : String(error)],
    })
  }
}

export const getAccountDropdownValues = async () => {
  try {
    const accountAutoCompleteFields = [
      'Disability_Status__c',
      'Academic_Level__c',
    ]
    const picklistObj: Record<
      string,
      ({ label: string; value: string } | undefined)[]
    > = {}

    for (const field of accountAutoCompleteFields) {
      const picklistValues = await getPicklistOptionsServer('Account', field)
      picklistObj[field] = picklistValues
    }

    return responseGenerator(true, picklistObj)
  } catch (error) {
    return responseGenerator(false, {
      errors: [error instanceof Error ? error.message : String(error)],
    })
  }
}
