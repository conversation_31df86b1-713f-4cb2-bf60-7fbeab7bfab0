'use server'

import { connectToSalesforce } from '@/lib/salesforce'
import { arrayToSqlInQuery, responseGenerator } from '@/utils/utils'
import { getAccountId } from '@/lib/actions/account.actions'
import { verifyCustomAccountSession } from '@/lib/actions/login.actions'

export const getAllFollowedTeachersServer = async (userId: string) => {
  const conn = await connectToSalesforce()
  const soql = `SELECT Id, Followed_User__r.Id, Followed_User__r.Name, Followed_User__r.Photo__c, Following_User__c FROM Follow__c WHERE Following_User__c = '${userId}'`

  const records = (await conn.query(soql)).records
  const followingData = records.map((record) => {
    return {
      id: record.Followed_User__r.Id,
      name: record.Followed_User__r.Name,
      photo: record.Followed_User__r.Photo__c,
    }
  })

  return followingData
}

export const getAllFollowedTeachers = async (accessToken: string) => {
  try {
    if (!(await verifyCustomAccountSession(accessToken))) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const accessTokenUserId = await getAccountId(accessToken)

    if (!accessTokenUserId) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const followData = await getAllFollowedTeachersServer(accessTokenUserId)

    return responseGenerator(true, followData)
  } catch (error) {
    return responseGenerator(false, {
      errors: ['error getting followed teachers'],
    })
  }
}

export const getFollowedTeachers = async (
  accessToken: string,
  limit = 10,
  offset = 0,
) => {
  try {
    if (!(await verifyCustomAccountSession(accessToken))) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const accessTokenUserId = await getAccountId(accessToken)

    if (!accessTokenUserId) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const conn = await connectToSalesforce()
    const countSoql = `SELECT Id, Followed_User__r.Id, Followed_User__r.Name, Following_User__r.Photo__c, Following_User__c FROM Follow__c WHERE Following_User__c = '${accessTokenUserId}'`
    const soql = countSoql + ` LIMIT ${limit} OFFSET ${offset}`

    const getRecordsPromise = conn.query(soql)
    const getCountRecordsPromise = conn.query(countSoql)

    const [getRecords, getCountRecords] = await Promise.all([
      getRecordsPromise,
      getCountRecordsPromise,
    ])

    const records = getRecords.records
    const countRecords = getCountRecords.records

    const followingData = records.map((record) => {
      return {
        id: record.Followed_User__r.Id,
        name: record.Followed_User__r.Name,
        photo: record.Following_User__r.Photo__c,
      }
    })

    const returnData = {
      data: followingData,
      total: countRecords.length,
    }

    return responseGenerator(true, returnData)
  } catch (error) {
    return responseGenerator(false, {
      errors: ['error getting followed teachers'],
    })
  }
}

export const followTeachers = async (
  accessToken: string,
  teacherIds: string[],
) => {
  try {
    if (!(await verifyCustomAccountSession(accessToken))) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const accessTokenUserId = await getAccountId(accessToken)

    if (!accessTokenUserId) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const followData = await getAllFollowedTeachersServer(accessTokenUserId)
    const followTeacherIds = followData.map((data) => data.id)

    teacherIds = teacherIds.filter((id) => !followTeacherIds.includes(id))

    const followItems = teacherIds.map((id) => {
      return {
        Following_User__c: accessTokenUserId,
        Followed_User__c: id,
      }
    })

    const conn = await connectToSalesforce()
    const { successfulResults } = await conn.bulk2.loadAndWaitForResults({
      object: 'Follow__c',
      operation: 'insert',
      input: followItems,
      pollTimeout: 60000,
      pollInterval: 1000,
    })

    const response = successfulResults.map((item: any) => {
      return {
        id: item.Followed_User__c,
        isCreated: item.sf__Created,
      }
    })
    return responseGenerator(true, response)
  } catch (error) {
    return responseGenerator(false, {
      errors: ['error getting followed teachers'],
    })
  }
}

export const unFollowTeachers = async (
  accessToken: string,
  teacherIds: string[],
) => {
  try {
    if (!(await verifyCustomAccountSession(accessToken))) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const accessTokenUserId = await getAccountId(accessToken)

    if (!accessTokenUserId) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const conn = await connectToSalesforce()

    // Find existing follow records to delete
    const findSoql = `SELECT Id FROM Follow__c WHERE Following_User__c = '${accessTokenUserId}' AND Followed_User__c IN ('${teacherIds.join("', '")}')`
    const existingRecords = (await conn.query(findSoql)).records

    if (existingRecords.length === 0) {
      return responseGenerator(true, [])
    }

    const recordIds = existingRecords.map((record: any) => record.Id)

    const { successfulResults } = await conn.bulk2.loadAndWaitForResults({
      object: 'Follow__c',
      operation: 'delete',
      input: recordIds.map((id) => ({ Id: id })),
      pollTimeout: 60000,
      pollInterval: 1000,
    })

    const response = successfulResults.map((item: any) => {
      return {
        id: item.Id,
        isDeleted: item.sf__Id && !item.sf__Error,
      }
    })

    return responseGenerator(true, response)
  } catch (error) {
    return responseGenerator(false, {
      errors: ['error unfollowing teachers'],
    })
  }
}
