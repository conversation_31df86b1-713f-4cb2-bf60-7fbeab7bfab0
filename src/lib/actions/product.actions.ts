'use server'

import { connectToSalesforce } from '@/lib/salesforce'
import { responseGenerator } from '@/utils/utils'

export const getAllDonationProducts = async () => {
  try {
    const conn = await connectToSalesforce()
    const soql = `SELECT Id, Name FROM Product2 WHERE Family = 'Donation' AND IsActive = true`
    const records = (await conn.query(soql)).records

    const donationsList = records.map((record) => {
      return {
        id: record.Id,
        name: record.Name,
      }
    })

    return responseGenerator(true, donationsList)
  } catch (error) {
    console.log('Error: ', error)
    return responseGenerator(false, {
      errors: ['failed to get donations'],
    })
  }
}
