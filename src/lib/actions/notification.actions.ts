'use server'
import { connectToSalesforce } from '@/lib/salesforce'
import { responseGenerator } from '@/utils/utils'

//Get notidications count for a user

export const getNotificationsCount = async (userId: string) => {
  const conn = await connectToSalesforce()
  const query = { User__c: userId, Unread__c: true }
  const notificationsCount = await conn.sobject('Notification__c').count(query)

  return responseGenerator(true, { data: notificationsCount })
}

// GET ALL NOTIFICATIONS for a user
export const getNotifications = async (userId: string) => {
  const conn = await connectToSalesforce()
  //TODO: add pagination
  let soql = `SELECT Thumbnail_URL__c, Notification_Title__c, Notification_Text__c, Unread__c, User__c, Navigation_URL__c,CreatedDate FROM Notification__c WHERE User__c = '${userId}' ORDER BY CreatedDate DESC`
  const notifications = (await conn.query(soql)).records
  const notificationsCount = notifications.length

  const notificationsWithFilledPlaceholders = await Promise.all(
    notifications.map(async (notification) => {
      if (!notification) return
      const classEventClubId = notification.Class_Event_and_Club__c
      const triggeringUserId = notification.Triggering_User__c

      //get class name
      const classEventClub = (await conn
        .sobject('Class_Event_Club__c')
        .findOne({
          Id: classEventClubId,
        })) as any
      const className = classEventClub.Name

      //get user name
      const user = (await conn.sobject('Account').findOne({
        Id: triggeringUserId,
      })) as any
      const userName = user.Name

      //fill placeholders
      notification.Notification_Title__c =
        notification.Notification_Title__c.replace('{ClassName}', className)
      notification.Notification_Text__c =
        notification.Notification_Text__c.replace('{User}', userName)

      return notification
    }),
  )

  return responseGenerator(true, {
    data: notificationsWithFilledPlaceholders,
    total: notificationsCount,
  })
}

//Read all notifications for a user
export const readNotifications = async (userId: string) => {
  const conn = await connectToSalesforce()
  const query = { User__c: userId, Unread__c: true }
  //make bulk update
  const notifications = await conn
    .sobject('Notification__c')
    .find(query)
    .update({ Unread__c: false })

  for (let notification of notifications) {
    if (!notification.success) {
      throw new Error('Failed to update notifications')
    }
  }

  return responseGenerator(true, { data: null })
}
