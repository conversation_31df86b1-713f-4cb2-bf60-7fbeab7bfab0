'use server'

import { connectToSalesforce } from '@/lib/salesforce'
import { unstable_cache } from 'next/cache'

type NavigationItem = {
  Id: string
  Parent__c?: string
  Sequence__c: number
  Slug__c?: string
  Name: string
}

interface TreeNode extends NavigationItem {
  children: TreeNode[]
}

function buildTree(data: NavigationItem[]): TreeNode[] {
  const itemMap = new Map<string, TreeNode>()
  const rootNodes: TreeNode[] = []

  data.forEach((item) => {
    itemMap.set(item.Id, { ...item, children: [] })
  })

  data.forEach((item) => {
    const node = itemMap.get(item.Id)!
    if (item.Parent__c) {
      const parent = itemMap.get(item.Parent__c)
      parent?.children.push(node)
    } else {
      rootNodes.push(node)
    }
  })

  const sortChildren = (nodes: TreeNode[]) => {
    nodes.sort((a, b) => a.Sequence__c - b.Sequence__c)
    nodes.forEach((node) => sortChildren(node.children))
  }

  sortChildren(rootNodes)

  return rootNodes
}

// Core function to fetch navigation data
const fetchNavigationTree = async () => {
  try {
    const conn = await connectToSalesforce()
    const soql = `SELECT Id, Parent__c,Sequence__c, Slug__c,Name  from Navigation_Item__c`
    const { records } = await conn.query(soql)
    const data = records.map((record: any) => record as NavigationItem)

    const navigationTree = buildTree(data)
    const navigationItems = navigationTree[0].children

    return navigationItems
  } catch (error) {
    console.error('Error fetching navigation tree:', error)
    return []
  }
}

// Cached version with 1 hour revalidation
const getCachedNavigationTree = unstable_cache(
  fetchNavigationTree,
  ['navigation-tree'],
  {
    revalidate: 3600, // 1 hour cache
    tags: ['navigation'],
  },
)

export const getNavigationTree = getCachedNavigationTree
