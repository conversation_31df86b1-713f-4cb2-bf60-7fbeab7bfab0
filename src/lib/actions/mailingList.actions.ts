'use server'

import { connectToSalesforce } from '@/lib/salesforce'
import { responseGenerator } from '@/utils/utils'
import { getAccountId } from '@/lib/actions/account.actions'
import { verifyCustomAccountSession } from '@/lib/actions/login.actions'

export const getMailingListEmailsServer = async (mailingListId: string) => {
  const conn = await connectToSalesforce()
  const soql = `SELECT Email__c  FROM Mailing_List_Item__c  WHERE Mailing_List__c  = '${mailingListId}'`

  const records = (await conn.query(soql)).records

  const emails = records.map((item) => item.Email__c)
  return emails
}

export const getMailingLists = async (
  accessToken: string,
  limit = 10,
  offset = 0,
) => {
  try {
    if (!(await verifyCustomAccountSession(accessToken))) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const accessTokenUserId = await getAccountId(accessToken)

    const conn = await connectToSalesforce()

    const allRecordsSoql = `SELECT Id, Name, Total_Contacts__c FROM Mailing_List__c WHERE Account__c = '${accessTokenUserId}'`

    const paginatedSoql = allRecordsSoql + `LIMIT ${limit} OFFSET ${offset}`

    const records = (await conn.query(paginatedSoql)).records
    const countRecords = await conn.query(allRecordsSoql)

    const returnData = records.map((record) => {
      return {
        id: record.Id,
        name: record.Name,
        total_items: record.Total_Contacts__c ?? 0,
      }
    })

    const data = {
      data: returnData,
      total: countRecords.totalSize,
    }

    return responseGenerator(true, data)
  } catch (err) {
    return responseGenerator(false, { errors: ['Error fetching mailing list'] })
  }
}

export const getMailingListItems = async (
  accessToken: string,
  mailingListId: string,
  limit = 10,
  offset = 0,
) => {
  try {
    if (!(await verifyCustomAccountSession(accessToken))) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const accessTokenUserId = await getAccountId(accessToken)

    const conn = await connectToSalesforce()

    const query = {
      Id: mailingListId,
      Account__c: accessTokenUserId,
    }

    const mailingList = await conn.sobject('Mailing_List__c').findOne(query)

    if (!mailingList) {
      return responseGenerator(false, { errors: ['mailing list not found'] })
    }

    const allRecordsSoql = `SELECT Id, Email__c FROM Mailing_List_Item__c WHERE Mailing_List__c = '${mailingList.Id}'`
    const paginatedSoql = allRecordsSoql + `LIMIT ${limit} OFFSET ${offset}`

    const records = (await conn.query(paginatedSoql)).records
    const countRecords = await conn.query(allRecordsSoql)

    const returnData = records.map((record) => {
      return {
        id: record.Id,
        email: record.Email__c,
      }
    })

    const data = {
      data: returnData,
      total: countRecords.totalSize,
    }

    return responseGenerator(true, data)
  } catch (err) {
    return responseGenerator(false, {
      errors: ['Error fetching mailing list items'],
    })
  }
}

export const addBulkEmailsToMailingList = async (
  accessToken: string,
  mailingListId: string,
  emails: string[],
) => {
  try {
    if (!(await verifyCustomAccountSession(accessToken))) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const accessTokenUserId = await getAccountId(accessToken)

    const conn = await connectToSalesforce()

    const query = {
      Id: mailingListId,
      Account__c: accessTokenUserId,
    }

    const mailingList = await conn.sobject('Mailing_List__c').findOne(query)

    if (!mailingList) {
      return responseGenerator(false, { errors: ['mailing list not found'] })
    }

    const alreadyPresentEmails = await getMailingListEmailsServer(mailingListId)

    // Convert all emails to lowercase for Salesforce consistency
    const normalizedEmails = emails.map((email) => email.toLowerCase())
    const normalizedAlreadyPresentEmails = alreadyPresentEmails.map((email) =>
      email.toLowerCase(),
    )

    const mailingListItems = normalizedEmails
      .filter((email) => !normalizedAlreadyPresentEmails.includes(email))
      .map((email) => ({
        Email__c: email,
        Mailing_List__c: mailingListId,
      }))

    const { successfulResults } = await conn.bulk2.loadAndWaitForResults({
      object: 'Mailing_List_Item__c',
      operation: 'insert',
      input: mailingListItems,
      pollTimeout: 60000,
      pollInterval: 1000,
    })

    const bulkInsertResponse = successfulResults.map((item: any) => {
      return {
        id: item.sf__Id,
        isCreated: item.sf__Created,
        email: item.Email__c,
      }
    })

    return responseGenerator(true, bulkInsertResponse)
  } catch (err) {
    console.log('Error: ', err)
    return responseGenerator(false, { errors: ['error adding emails'] })
  }
}

export const addEmailToMailingList = async (
  accessToken: string,
  mailingListId: string,
  email: string,
) => {
  try {
    if (!(await verifyCustomAccountSession(accessToken))) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const accessTokenUserId = await getAccountId(accessToken)

    const conn = await connectToSalesforce()

    const query = {
      Id: mailingListId,
      Account__c: accessTokenUserId,
    }

    const mailingList = await conn.sobject('Mailing_List__c').findOne(query)

    if (!mailingList) {
      return responseGenerator(false, { errors: ['mailing list not found'] })
    }

    // Convert email to lowercase for Salesforce consistency
    const normalizedEmail = email.toLowerCase()

    const newMailingListItem = {
      Email__c: normalizedEmail,
      Mailing_List__c: mailingListId,
    }

    const response = await conn
      .sobject('Mailing_List_Item__c')
      .create(newMailingListItem)

    return responseGenerator(true, { id: response.id })
  } catch (err) {
    return responseGenerator(false, { errors: ['error adding email'] })
  }
}

export const deleteMailingListItem = async (mailingListItemId: string) => {
  try {
    const conn = await connectToSalesforce()
    await conn.sobject('Mailing_List_Item__c').delete(mailingListItemId)

    return responseGenerator(true, null)
  } catch (err) {
    return responseGenerator(false, { errors: ['error deleting email'] })
  }
}

export const deleteMailingList = async (
  accessToken: string,
  mailingListId: string,
) => {
  try {
    if (!(await verifyCustomAccountSession(accessToken))) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const accessTokenUserId = await getAccountId(accessToken)

    const conn = await connectToSalesforce()

    const query = {
      Id: mailingListId,
      Account__c: accessTokenUserId,
    }

    const mailingList = await conn.sobject('Mailing_List__c').findOne(query)

    if (!mailingList) {
      return responseGenerator(false, { errors: ['mailing list not found'] })
    }

    await conn.sobject('Mailing_List__c').delete(mailingList.Id!)

    return responseGenerator(true, null)
  } catch (err) {
    return responseGenerator(false, { errors: ['error deleting mailing list'] })
  }
}

export const updateMailingList = async (
  accessToken: string,
  mailingListId: string,
  name: string,
) => {
  try {
    if (!(await verifyCustomAccountSession(accessToken))) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const accessTokenUserId = await getAccountId(accessToken)

    const conn = await connectToSalesforce()

    const query = {
      Id: mailingListId,
      Account__c: accessTokenUserId,
    }

    const mailingList = await conn.sobject('Mailing_List__c').findOne(query)

    if (!mailingList) {
      return responseGenerator(false, { errors: ['mailing list not found'] })
    }

    const updatedMailingListResponse = await conn
      .sobject('Mailing_List__c')
      .update({
        Id: mailingList.Id!,
        Name: name,
      })

    return responseGenerator(true, null)
  } catch (err) {
    return responseGenerator(false, { errors: ['error updating mailing list'] })
  }
}

export const createMailingList = async (accessToken: string, name: string) => {
  try {
    if (!(await verifyCustomAccountSession(accessToken))) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const accessTokenUserId = await getAccountId(accessToken)

    const conn = await connectToSalesforce()

    const query = {
      Account__c: accessTokenUserId,
      Name: name,
      Type__c: 'Custom',
    }

    const mailingList = await conn.sobject('Mailing_List__c').create(query)

    if (!mailingList) {
      return responseGenerator(false, { errors: ['mailing list not found'] })
    }

    const returnData = {
      id: mailingList.id!,
      name: name,
    }
    return responseGenerator(true, returnData)
  } catch (err) {
    return responseGenerator(false, { errors: ['error updating mailing list'] })
  }
}
