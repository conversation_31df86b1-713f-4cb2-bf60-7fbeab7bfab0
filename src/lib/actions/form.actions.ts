'use server'

import { connectToSalesforce } from '@/lib/salesforce'

export const getFormById = async (id: string) => {
  const conn = await connectToSalesforce()
  const soql = `SELECT Id, Schema__c, Form_ID__c FROM Form__c WHERE Form_ID__c = '${id}'`
  const records = (await conn.query(soql)).records
  return records[0]
}

export const createFormResponse = async (formId: string, formResponse: any) => {
  const conn = await connectToSalesforce()
  const query = {
    Form_ID__c: formId,
  }
  const form = await conn.sobject('Form__c').findOne(query)
  if (!form) {
    throw new Error('Form not found')
  }

  const formResponseObj = {
    Form__c: form.Id,
    Response_Data__c: JSON.stringify(formResponse),
  }

  await conn.sobject('Form_Response__c').create(formResponseObj)
}
