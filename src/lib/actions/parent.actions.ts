'use server'

import {
  getAccountByPhoneOrEmailServer,
  getAccountId,
  getAccountPrimaryEmail,
  getAccountRolesServer,
  getAccountStatsByIdServer,
  getOrCreateAccountServer,
  getTeacherForClassServer,
} from '@/lib/actions/account.actions'
import { verifyCustomAccountSession } from '@/lib/actions/login.actions'
import { connectToSalesforce } from '@/lib/salesforce'
import { arrayToSqlInQuery, isAbove18, responseGenerator } from '@/utils/utils'
import { ROLES } from '@/types/enum'
import {
  getApprovalPendingOrderIdsServer,
  getOrderDetailForParentServer,
} from '@/lib/actions/order.actions'
import {
  getAllMeetingsByIdServer,
  getClassMinimumPriceByIdServer,
  getClassPriceServer,
  getStudentRegisteredClassSoqlServer,
  isClassUnderApprovalServer,
  isRegisteredForClassServer,
} from '@/lib/actions/class.actions'
import jsforce from 'jsforce'
import {
  addMoneyToWalletByIdServer,
  getWalletBalanceServer,
} from '@/lib/actions/wallet.actions'
import { getRecordTypeByName } from '@/lib/actions/salesforce.actions'
import { createPaymentToken, DecodedPaymentToken } from '@/lib/jwt'
import { IPaymentToken } from '@/models/paymentToken.model'
import { PaymentType } from '@/utils/constants'
import { getTeachersOfUserByIdServer } from '@/lib/actions/teacher.actions'
import { createPaymentTokenSF } from '@/lib/actions/payment.actions'

const canAccessYouthDataServer = async (userId: string, youthId: string) => {
  const roles = await getAccountRolesServer(userId)
  if (!roles.includes(ROLES.Parent) && !roles.includes(ROLES.Teacher)) {
    return false
  }
  const youthIds = await getParentYouthAccountServer(userId)
  const youthTeacherIds = await getTeachersOfUserByIdServer(youthId)
  if (!youthIds.includes(youthId) && !youthTeacherIds.includes(userId)) {
    return false
  }

  return true
}

export const isParentSessionValidServer = async (accessToken: string) => {
  if (!(await verifyCustomAccountSession(accessToken))) {
    return false
  }

  const accessTokenUserId = await getAccountId(accessToken)

  if (!accessTokenUserId) {
    return false
  }

  const roles = await getAccountRolesServer(accessTokenUserId)

  if (!roles.includes(ROLES.Teacher)) {
    return false
  }

  return true
}

export const getParentYouthAccountServer = async (parentId: string) => {
  const conn = await connectToSalesforce()
  const soql = `SELECT Id FROM Account WHERE Parent_Account__c = '${parentId}'`
  const records = (await conn.query(soql)).records
  const accountIds = records.map((item) => item.Id!)

  return accountIds
}

export const getParentTodoList = async (accessToken: string) => {
  try {
    if (!(await verifyCustomAccountSession(accessToken))) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const accessTokenUserId = await getAccountId(accessToken)

    if (!accessTokenUserId) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const roles = await getAccountRolesServer(accessTokenUserId)

    if (!roles.includes(ROLES.Parent)) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const youthIds = await getParentYouthAccountServer(accessTokenUserId)
    const approvalPendingOrderIds =
      await getApprovalPendingOrderIdsServer(youthIds)

    const orderDetails = await Promise.all(
      approvalPendingOrderIds.map(
        async (id) => await getOrderDetailForParentServer(id),
      ),
    )

    return responseGenerator(true, orderDetails)
  } catch (error) {
    return responseGenerator(false, {
      errors: [error instanceof Error ? error.message : String(error)],
    })
  }
}

export const updateParentTodo = async (
  accessToken: string,
  orderId: string,
  type: 'Approved' | 'Rejected',
  paidBy?: 'Parent' | 'Child',
) => {
  try {
    if (!['Approved', 'Rejected'].includes(type)) {
      throw new Error('Invalid status field')
    }

    if (type === 'Approved') {
      if (!paidBy) {
        throw new Error('paidBy required')
      }
      if (!['Parent', 'Child'].includes(paidBy)) {
        throw new Error('Invalid paidBy field')
      }
    }

    if (!(await verifyCustomAccountSession(accessToken))) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const accessTokenUserId = await getAccountId(accessToken)

    if (!accessTokenUserId) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const roles = await getAccountRolesServer(accessTokenUserId)

    if (!roles.includes(ROLES.Parent)) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const conn = await connectToSalesforce()

    const order = await conn.sobject('Order').retrieve(orderId)

    if (!order) {
      throw new Error('Approval not found')
    }

    await conn.sobject('Order').update({
      Id: orderId,
      Parent_Approval_Status__c: type,
      Paid_by__c: paidBy,
    })

    return responseGenerator(true, null)
  } catch (error) {
    return responseGenerator(false, {
      errors: [error instanceof Error ? error.message : String(error)],
    })
  }
}

export const getAllParentMeetings = async (
  accessToken: string,
  startDate: string,
  endDate: string,
) => {
  try {
    if (!(await verifyCustomAccountSession(accessToken))) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const accessTokenUserId = await getAccountId(accessToken)

    if (!accessTokenUserId) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const roles = await getAccountRolesServer(accessTokenUserId)

    if (!roles.includes(ROLES.Parent)) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }
    const youthIds = await getParentYouthAccountServer(accessTokenUserId)
    const allYouthMeetingData = []

    for (const youthId of youthIds) {
      const meetingData = await getAllMeetingsByIdServer(
        youthId,
        startDate,
        endDate,
      )
      allYouthMeetingData.push(...meetingData)
    }

    return responseGenerator(true, allYouthMeetingData)
  } catch (error) {
    return responseGenerator(false, {
      errors: [error instanceof Error ? error.message : String(error)],
    })
  }
}

export const getMyKidSection = async (accessToken: string) => {
  try {
    if (!(await verifyCustomAccountSession(accessToken))) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const accessTokenUserId = await getAccountId(accessToken)

    if (!accessTokenUserId) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const roles = await getAccountRolesServer(accessTokenUserId)

    if (!roles.includes(ROLES.Parent)) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const youthIds = await getParentYouthAccountServer(accessTokenUserId)
    if (youthIds.length === 0) {
      return responseGenerator(true, [])
    }

    const conn = await connectToSalesforce()
    const account_soql = `SELECT Id, Photo__c, Name, Login_Allowed__c FROM Account WHERE Id IN ${arrayToSqlInQuery(youthIds)}`
    const records = (await conn.query(account_soql)).records

    const returnData = await Promise.all(
      records.map(async (record) => {
        const stats = await getAccountStatsByIdServer(
          record.Id!,
          'Youth Student',
        )
        return {
          ...record,
          stats: stats.studentStats,
        }
      }),
    )
    return responseGenerator(true, returnData)
  } catch (error) {
    return responseGenerator(false, {
      errors: [error instanceof Error ? error.message : String(error)],
    })
  }
}

export const getYouthAccountById = async (
  accessToken: string,
  youthId: string,
) => {
  try {
    if (!(await verifyCustomAccountSession(accessToken))) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const accessTokenUserId = await getAccountId(accessToken)

    if (!accessTokenUserId) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const isUserValid = await canAccessYouthDataServer(
      accessTokenUserId,
      youthId,
    )
    if (!isUserValid) {
      throw new Error('Not found')
    }

    const conn = await connectToSalesforce()

    const account = await conn.sobject('Account').retrieve(youthId)
    if (!account) {
      throw new Error('Not found')
    }

    const stats = await getAccountStatsByIdServer(youthId, 'Youth Student')

    const returnData = {
      ...account,
      stats: stats.studentStats,
    }
    return responseGenerator(true, returnData)
  } catch (error) {
    return responseGenerator(false, {
      errors: [error instanceof Error ? error.message : String(error)],
    })
  }
}

export const getAllYouthMeetings = async (
  accessToken: string,
  youthId: string,
  startDate: string,
  endDate: string,
) => {
  try {
    if (!(await verifyCustomAccountSession(accessToken))) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const accessTokenUserId = await getAccountId(accessToken)

    if (!accessTokenUserId) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const isUserValid = await canAccessYouthDataServer(
      accessTokenUserId,
      youthId,
    )
    if (!isUserValid) {
      throw new Error('Not found')
    }

    const meetingData = await getAllMeetingsByIdServer(
      youthId,
      startDate,
      endDate,
    )

    return responseGenerator(true, meetingData)
  } catch (error) {
    return responseGenerator(false, {
      errors: [error instanceof Error ? error.message : String(error)],
    })
  }
}

export const getYouthClasses = async (
  accessToken: string,
  youthId: string,
  classType: 'All' | 'Previous' | 'Current',
  limit = 20,
  offset = 0,
) => {
  try {
    if (!['All', 'Previous', 'Current'].includes(classType)) {
      throw new Error('classType must have values of All, Pervious, Current')
    }

    if (!(await verifyCustomAccountSession(accessToken))) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const accessTokenUserId = await getAccountId(accessToken)

    if (!accessTokenUserId) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const isUserValid = await canAccessYouthDataServer(
      accessTokenUserId,
      youthId,
    )
    if (!isUserValid) {
      throw new Error('Not found')
    }

    const accountRegisteredClassSoql =
      await getStudentRegisteredClassSoqlServer(youthId, 'Youth Class')

    const conn = await connectToSalesforce()

    let soql = `SELECT Class_and_Event__c FROM Meeting__c  WHERE  Class_and_Event__r.Id IN (${accountRegisteredClassSoql})`
    if (classType === 'Previous')
      soql += ` AND Class_and_Event__r.End_Date__c < ${jsforce.Date.TODAY}`
    if (classType === 'Current')
      soql += ` AND Class_and_Event__r.End_Date__c > ${jsforce.Date.TODAY}`

    soql += ` GROUP BY Class_and_Event__c`
    const groupedClasses = (await conn.query(soql)).records
    const classIdList = groupedClasses.map((item) => item.Class_and_Event__c)

    if (!classIdList.length) {
      return responseGenerator(true, {
        data: [],
        total: 0,
      })
    }

    soql = `SELECT Id,Name,Title__c,Type__c,Garnish__c,Genre__c,Level__c,Teacher__c,Banner_Image__c,Booked_Seats__c,Total_Seats__c,Total_Meetings_Count__c,Duration__c,Start_Date__c,End_Date__c FROM Class_Event_Club__c WHERE Id IN ${arrayToSqlInQuery(classIdList)}`

    const countSoql = soql
    soql += ` LIMIT ${limit} OFFSET ${offset}`

    const classes = (await conn.query(soql)).records

    const classesWithTeacherAndPrice = await Promise.all(
      classes.map(async (classEvent) => {
        if (!classEvent?.Teacher__c)
          return { ...classEvent, teacher_data: null }
        const teacher = await getTeacherForClassServer(classEvent.Teacher__c)
        const price = await getClassPriceServer(classEvent.Id as string)
        return { ...classEvent, teacher_data: teacher, Price__c: price }
      }),
    )

    const totalClasses = (await conn.query(countSoql)).records

    let finalClassList = classesWithTeacherAndPrice
    let finalClassesSize = totalClasses.length

    return responseGenerator(true, {
      data: finalClassList,
      total: finalClassesSize,
    })
  } catch (error) {
    return responseGenerator(false, {
      errors: [error instanceof Error ? error.message : String(error)],
    })
  }
}

export const getYouthAccounts = async (accessToken: string) => {
  try {
    if (!(await verifyCustomAccountSession(accessToken))) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const accessTokenUserId = await getAccountId(accessToken)

    if (!accessTokenUserId) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const roles = await getAccountRolesServer(accessTokenUserId)

    if (!roles.includes(ROLES.Parent)) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const youthIds = await getParentYouthAccountServer(accessTokenUserId)
    if (youthIds.length === 0) {
      return responseGenerator(true, [])
    }

    const conn = await connectToSalesforce()
    const soql = `SELECT Id, Name, Photo__c FROM Account WHERE Id IN ${arrayToSqlInQuery(youthIds)}`
    const records = (await conn.query(soql)).records
    return responseGenerator(true, records)
  } catch (error) {
    return responseGenerator(false, {
      errors: [error instanceof Error ? error.message : String(error)],
    })
  }
}

export const addMoneyToYouthAccount = async (
  accessToken: string,
  youthId: string,
  amount: number,
) => {
  try {
    if (!(await verifyCustomAccountSession(accessToken))) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const accessTokenUserId = await getAccountId(accessToken)

    if (!accessTokenUserId) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const roles = await getAccountRolesServer(accessTokenUserId)

    if (!roles.includes(ROLES.Parent)) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const youthIds = await getParentYouthAccountServer(accessTokenUserId)
    if (!youthIds.includes(youthId)) {
      throw new Error('Not found')
    }

    const paymentJwtToken = await addMoneyToWalletByIdServer(youthId, amount)

    const returnData = {
      token: paymentJwtToken,
    }
    return responseGenerator(true, returnData)
  } catch (error) {
    return responseGenerator(false, {
      errors: [error instanceof Error ? error.message : String(error)],
    })
  }
}

export const updateYouthAccount = async (
  accessToken: string,
  youthId: string,
  data: any,
) => {
  try {
    if (!(await verifyCustomAccountSession(accessToken))) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const accessTokenUserId = await getAccountId(accessToken)

    if (!accessTokenUserId) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const roles = await getAccountRolesServer(accessTokenUserId)

    if (!roles.includes(ROLES.Parent)) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const youthIds = await getParentYouthAccountServer(accessTokenUserId)
    if (!youthIds.includes(youthId)) {
      throw new Error('Not found')
    }
    const conn = await connectToSalesforce()

    const dateToUpdate: any = {}
    if ('firstName' in data) dateToUpdate['FirstName'] = data.firstName
    if ('lastName' in data) dateToUpdate['LastName'] = data.lastName
    if ('dateOfBirth' in data)
      dateToUpdate['PersonBirthdate'] = data.dateOfBirth
    if ('PhotoUrl' in data) dateToUpdate['Photo__c'] = data.PhotoUrl
    if ('disablityStatus' in data) {
      dateToUpdate['Disability_Status__c'] = data.disablityStatus
    }
    if ('phone' in data) dateToUpdate['Phone'] = data.phone
    if ('allergy' in data) dateToUpdate['Allergies__c'] = data.allergy
    if ('allergyInfo' in data)
      dateToUpdate['Allergy_Info__c'] = data.allergyInfo
    if ('loginAllowed' in data)
      dateToUpdate['Login_Allowed__c'] = data.loginAllowed
    if (data.academicLevel)
      dateToUpdate['Academic_Level__c'] = data.academicLevel
    if ('highSchool' in data) dateToUpdate['High_School__c'] = data.highSchool
    if ('photoUrl' in data) dateToUpdate['Photo__c'] = data.photoUrl

    const updatedAccountResponse = await conn.sobject('Account').update({
      Id: youthId,
      ...dateToUpdate,
    })

    for (const value in updatedAccountResponse) {
      if (updatedAccountResponse[value].success === false) {
        return responseGenerator(false, {
          errors: updatedAccountResponse[value].errors,
        })
      }
    }
    return responseGenerator(true, updatedAccountResponse)
  } catch (error) {
    return responseGenerator(false, {
      errors: [error instanceof Error ? error.message : String(error)],
    })
  }
}

export const addYouthAccount = async (
  accessToken: string,
  firstName: string,
  lastName: string,
  dateOfBirth: string,
  email: string,
  phone: string,
  photoUrl?: string,
) => {
  try {
    if (!(await verifyCustomAccountSession(accessToken))) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const accessTokenUserId = await getAccountId(accessToken)
    const accessTokenEmail = await getAccountPrimaryEmail(accessToken)

    if (email === accessTokenEmail) {
      throw new Error('Invalid Email')
    }

    if (!accessTokenUserId) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const roles = await getAccountRolesServer(accessTokenUserId)

    if (!roles.includes(ROLES.Parent)) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    if (isAbove18(dateOfBirth)) {
      throw new Error('Youth cannot be above 18 years old')
    }

    let youthAccount = await getAccountByPhoneOrEmailServer(phone, email)
    if (!youthAccount) {
      const newYouthAccount = {
        PersonEmail: email,
        FirstName: firstName,
        LastName: lastName,
        Phone: phone,
        RecordTypeId: await getRecordTypeByName('PersonAccount', 'Account'),
        PersonBirthdate: dateOfBirth,
      }

      const { account } = await getOrCreateAccountServer(
        newYouthAccount,
        phone,
        email,
      )

      youthAccount = account
    }
    const conn = await connectToSalesforce()
    await conn.sobject('Account').update({
      Id: youthAccount?.Id!,
      Parent_Account__c: accessTokenUserId,
      Photo__c: photoUrl,
    })
    return responseGenerator(true, youthAccount?.Id)
  } catch (error) {
    return responseGenerator(false, {
      errors: [error instanceof Error ? error.message : String(error)],
    })
  }
}

export const buyClassForYouth = async (
  accessToken: string,
  youthId: string,
  classId: string,
  userAgreesToPay?: number,
  attendType?: 'In Person' | 'Online',
) => {
  try {
    if (!(await verifyCustomAccountSession(accessToken))) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const accessTokenUserId = await getAccountId(accessToken)

    if (!accessTokenUserId) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const roles = await getAccountRolesServer(accessTokenUserId)

    if (!roles.includes(ROLES.Parent)) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const youthIds = await getParentYouthAccountServer(accessTokenUserId)
    if (!youthIds.includes(youthId)) {
      throw new Error('Not found')
    }

    const isAlreadyRegisteredForClass = await isRegisteredForClassServer(
      youthId,
      classId,
    )
    if (isAlreadyRegisteredForClass) {
      throw new Error('youth already registered for class')
    }

    const isAlreadyRequestedForApproval = await isClassUnderApprovalServer(
      youthId,
      classId,
    )
    if (isAlreadyRequestedForApproval) {
      throw new Error('this class is waiting for your approval')
    }

    const userWalletBalance = await getWalletBalanceServer(accessTokenUserId)
    const paymentType: PaymentType = 'One-time' //TODO: hardcoding it for now
    const { is_flexible_price, minimum_price } =
      await getClassMinimumPriceByIdServer(classId)

    if (is_flexible_price && userAgreesToPay) {
      if (userAgreesToPay < minimum_price) {
        throw new Error('price less than minimum_price')
      }
    }

    const conn = await connectToSalesforce()

    let soql = `Select Id,Name,Price__c,Family,Class__r.Type__c from Product2 where Class__c ='${classId}'`
    const product = (await conn.query(soql)).records[0]
    if (!product) {
      throw new Error('Class Product not found')
    }

    let amount = product.Price__c

    if (is_flexible_price && userAgreesToPay) {
      amount = userAgreesToPay
    }

    const paymentTokenData: DecodedPaymentToken = {
      userId: youthId,
      entityId: classId,
      entityType: 'Single Class',
      amount: amount,
      walletBalance: userWalletBalance,
      parentAccountId: accessTokenUserId,
      attendanceType: attendType,
      paymentType: 'One-time',
      isWalletEnabled: true,
    }

    const paymentJwtToken = createPaymentToken(paymentTokenData)

    //create a new payment token for the user
    const paymentToken: IPaymentToken = {
      userId: youthId,
      token: paymentJwtToken,
      status: 'active',
      amount: amount,
      paymentType: 'One-time',
      occurrences: paymentType === 'One-time' ? 1 : 9999, //9999 for ongoing subscription
    }
    await createPaymentTokenSF(paymentToken)

    const returnData = {
      token: paymentJwtToken,
    }
    return responseGenerator(true, returnData)
  } catch (error) {
    return responseGenerator(false, {
      errors: [error instanceof Error ? error.message : String(error)],
    })
  }
}
