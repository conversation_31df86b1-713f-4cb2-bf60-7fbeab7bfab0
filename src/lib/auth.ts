import axios from 'axios'
import { toast } from 'react-toastify'

export async function loginRequestOtp(
  identifier: string,
  type: 'email' | 'sms',
  token: string | null,
): Promise<void> {
  const url = `/api/otp/send`
  try {
    await axios.post(
      url,
      { identifier, type },
      { headers: { 'Content-Type': 'application/json', Authorization: token } },
    )
  } catch (error) {
    toast.error('Something went wrong!' + error, { autoClose: 1500 })
    throw error
  }
}
