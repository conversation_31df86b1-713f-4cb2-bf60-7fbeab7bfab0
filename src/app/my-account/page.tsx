import PageHeader from '@/components/PageHeader/PageHeader'
import AccountSettingsTabs from './AccountSettingsTabs'
import { handleLoggedIn } from '@/lib/actions/login.actions'
import { ROLES } from '@/types/enum'
import { getClassesGenreFilters } from '@/lib/actions/class.actions'
import { getAuthorsOption } from '@/lib/actions/account.actions'

const page = async () => {
  const [userData, genres, authors] = await Promise.all([
    handleLoggedIn(),
    getClassesGenreFilters(),
    getAuthorsOption(),
  ])

  return (
    <div
      className={`w-full ${!userData && 'flex flex-col items-center justify-center'}`}
    >
      {userData && (
        <PageHeader
          heading="Account"
          showTittle={false}
          showSearch={false}
          showMessage={false}
          showCart={userData?.role !== ROLES.Teacher}
          userData={userData}
        />
      )}
      <AccountSettingsTabs
        userData={userData}
        genres={genres?.data ?? []}
        authors={authors?.data ?? []}
      />
    </div>
  )
}

export default page
