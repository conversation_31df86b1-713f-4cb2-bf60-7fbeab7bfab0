import NotFound from '@/app/not-found'
import EditYouthAccount from '@/components/EditYouthAccount/EditYouthAccount'
import PageHeader from '@/components/PageHeader/PageHeader'
import { getToken, handleLoggedIn } from '@/lib/actions/login.actions'
import { getYouthAccountById } from '@/lib/actions/parent.actions'
import { ROLES } from '@/types/enum'

const Page = async ({ params }: { params: { id: string } }) => {
  let accessToken = await getToken('access')
  const [userData, youthDetail] = await Promise.all([
    handleLoggedIn(),
    getYouthAccountById(accessToken, params?.id),
  ])
  return (
    <div
      className={`w-full ${!userData && 'flex flex-col items-center justify-center'}`}
    >
      {userData && (
        <PageHeader
          heading="Edit Account"
          showTittle={false}
          showSearch={false}
          showMessage={false}
          showCart={userData?.role !== ROLES.Teacher}
          userData={userData}
        />
      )}
      {youthDetail?.success ? (
        <EditYouthAccount userData={youthDetail?.data} id={params?.id} />
      ) : (
        <NotFound />
      )}
    </div>
  )
}

export default Page
