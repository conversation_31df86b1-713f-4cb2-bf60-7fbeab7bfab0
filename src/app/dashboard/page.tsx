import AdultStudentDashboard from '@/components/AdultStudentDashboard/AdultStudentDashboard'
import ParentDashboard from '@/components/ParentDashboard/ParentDashboard'
import TeacherDashboard from '@/components/TeacherDashboard/TeacherDashboard'
import YouthStudentDashboard from '@/components/YouthStudentDashboard/YouthStudentDashboard'

import { handleLoggedIn } from '@/lib/actions/login.actions'
import { ROLES } from '@/types/enum'

const Dashboard = async () => {
  const userData: any = await handleLoggedIn()
  const renderDashboard = () => {
    switch (userData?.role) {
      case ROLES.Teacher:
        return <TeacherDashboard userData={userData} />

      case ROLES.Adult_Student:
        return <AdultStudentDashboard userData={userData} />

      case ROLES.Youth_Student:
        return <YouthStudentDashboard userData={userData} />

      case ROLES.Parent:
        return <ParentDashboard userData={userData} />

      default:
        return (
          <div className="p-4 text-sm text-red-500">
            No dashboard available for role: {userData?.role || 'Unknown'}
          </div>
        )
    }
  }

  return <div>{renderDashboard()}</div>
}

export default Dashboard
