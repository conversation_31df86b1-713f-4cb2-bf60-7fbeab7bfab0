import PageHeader from '@/components/PageHeader/PageHeader'
import <PERSON>CalendarView from '@/components/TeacherCalendarView/TeacherCalendarView'
import { handleLoggedIn } from '@/lib/actions/login.actions'
import React from 'react'

const page = async () => {
  const userData: any = await handleLoggedIn()
  return (
    <div className="w-screen md:w-full">
      {userData && (
        <PageHeader
          heading={`My Calendar`}
          showTittle={false}
          showSearch={false}
          showMessage={false}
          showCart={false}
          userData={userData}
        />
      )}
      <TeacherCalendarView />
    </div>
  )
}

export default page
