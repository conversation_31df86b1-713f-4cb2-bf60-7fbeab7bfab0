import PageHeader from '@/components/PageHeader/PageHeader'
import TeacherMailingList from '@/components/TeacherMailingList/TeacherMailingList'
import { handleLoggedIn } from '@/lib/actions/login.actions'
import React from 'react'

const page = async () => {
  const userData = await handleLoggedIn()

  return (
    <div className="w-screen lg:w-full">
      {userData && (
        <PageHeader
          heading={'Mailing Lists'}
          showTittle={false}
          showSearch={false}
          showMessage={false}
          showCart={false}
          userData={userData}
        />
      )}
      <TeacherMailingList />
    </div>
  )
}

export default page
