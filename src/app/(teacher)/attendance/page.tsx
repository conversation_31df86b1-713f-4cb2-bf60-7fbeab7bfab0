import PageHeader from '@/components/PageHeader/PageHeader'
import TeacherClassPage from '@/components/TeacherClassPage/TeacherClassPage'
import { handleLoggedIn } from '@/lib/actions/login.actions'
import React from 'react'

const page = async () => {
  const userData: any = await handleLoggedIn()
  return (
    <div className="w-screen md:w-full">
      {userData && (
        <PageHeader
          heading={'Attendance'}
          showTittle={false}
          showSearch={false}
          showMessage={false}
          showCart={false}
          userData={userData}
        />
      )}
      <TeacherClassPage classType={'Attendance Pending'} />
    </div>
  )
}

export default page
