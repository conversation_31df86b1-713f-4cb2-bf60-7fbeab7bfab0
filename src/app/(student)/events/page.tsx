import PageHeader from '@/components/PageHeader/PageHeader'
import React from 'react'
import {
  getCategoryDropdownValues,
  getEventCategoryFilters,
  getEvents,
  getSubcategoryDropdownValues,
} from '@/lib/actions/class.actions'
import EventsPage from '@/components/EventsPage/EventsPage'
import { handleLoggedIn } from '@/lib/actions/login.actions'

const Page = async () => {
  const [userData, eventType, categoryDropdownValues] = await Promise.all([
    handleLoggedIn(),
    getEventCategoryFilters(),
    getCategoryDropdownValues(),
  ])
  return (
    <div className="w-screen md:w-full">
      {userData && (
        <PageHeader
          heading={'Events'}
          showTittle={false}
          showSearch={false}
          showMessage={false}
          userData={userData}
        />
      )}
      <EventsPage
        userData={userData}
        eventType={eventType?.data || []}
        categoryDropdownValues={categoryDropdownValues?.data || []}
      />
    </div>
  )
}

export default Page
