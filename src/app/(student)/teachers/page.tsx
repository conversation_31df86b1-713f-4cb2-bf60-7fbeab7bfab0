import PageHeader from '@/components/PageHeader/PageHeader'
import TeacherListing from '@/components/TeacherListing/TeacherListing'
import { getClassesGenreFilters } from '@/lib/actions/class.actions'
import { handleLoggedIn } from '@/lib/actions/login.actions'
import React from 'react'

const Page = async () => {
  const [userData, genres] = await Promise.all([
    handleLoggedIn(),
    getClassesGenreFilters(),
  ])
  return (
    <div className="w-screen md:w-full">
      {userData && (
        <PageHeader
          heading={'Teachers'}
          showTittle={false}
          showSearch={false}
          showMessage={false}
          userData={userData}
        />
      )}
      <TeacherListing genres={genres?.data} />
    </div>
  )
}

export default Page
