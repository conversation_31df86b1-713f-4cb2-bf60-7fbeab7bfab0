import CartDetails from '@/components/CartDetails/CartDetails'
import PageHeader from '@/components/PageHeader/PageHeader'
import { getToken, handleLoggedIn } from '@/lib/actions/login.actions'
import { getYouthAccounts } from '@/lib/actions/parent.actions'
import { ROLES } from '@/types/enum'

const Page = async () => {
  const userData = await handleLoggedIn()
  const token = await getToken()

  let parentChildren: any = []
  if (userData?.role === ROLES.Parent) {
    const parentData = await getYouthAccounts(token)
    parentChildren = parentData?.data || []
  }
  return (
    <div className="w-screen md:w-full">
      {userData && (
        <PageHeader
          heading={'Cart'}
          showTittle={false}
          showSearch={false}
          showMessage={false}
          userData={userData}
        />
      )}
      <CartDetails userData={userData} parentChildren={parentChildren} />
    </div>
  )
}

export default Page
