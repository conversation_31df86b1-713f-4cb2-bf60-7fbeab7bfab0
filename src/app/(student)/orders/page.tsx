import OrdersListing from '@/components/OrdersListing/OrdersListing'
import PageHeader from '@/components/PageHeader/PageHeader'
import { handleLoggedIn } from '@/lib/actions/login.actions'
import React from 'react'

const Page = async () => {
  const userData = await handleLoggedIn()

  return (
    <div className="w-screen md:w-full">
      {userData && (
        <PageHeader
          heading={'Orders'}
          showTittle={false}
          showSearch={false}
          showMessage={false}
          userData={userData}
        />
      )}
      <OrdersListing />
    </div>
  )
}

export default Page
