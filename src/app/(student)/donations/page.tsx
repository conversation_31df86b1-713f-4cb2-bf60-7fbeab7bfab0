import DonationPage from '@/components/DonationPage/DonationPage'
import PageHeader from '@/components/PageHeader/PageHeader'
import { handleLoggedIn } from '@/lib/actions/login.actions'
import { getAllDonationProducts } from '@/lib/actions/product.actions'
import React from 'react'

const Page = async () => {
  const [userData, donationFundData] = await Promise.all([
    handleLoggedIn(),
    getAllDonationProducts(),
  ])
  return (
    <div className="w-screen md:w-full">
      {userData && (
        <PageHeader
          heading={'Donations'}
          showTittle={false}
          showSearch={false}
          showMessage={false}
          userData={userData}
        />
      )}
      <DonationPage donationFundTypes={donationFundData?.data || []} />
    </div>
  )
}

export default Page
