import MarkNotifications from '@/components/MarkNotifications/MarkNotifications'
import { NotificationCard } from '@/components/NotificationCard/NotificationCard'
import NotificationPage from '@/components/NotificationPage/NotificationPage'
import PageHeader from '@/components/PageHeader/PageHeader'
import { handleLoggedIn } from '@/lib/actions/login.actions'
import {
  getNotifications,
  readNotifications,
} from '@/lib/actions/notification.actions'
import { ROLES } from '@/types/enum'
import React from 'react'

const Page = async () => {
  const userData = await handleLoggedIn()
  let notifications: any = []
  if (userData && userData?.id) {
    const [notificationsRes] = await Promise.all([
      getNotifications(userData?.id),
      readNotifications(userData?.id),
    ])
    notifications = notificationsRes?.data?.data || []
  }
  return (
    <div className="w-screen md:w-full">
      {userData && (
        <PageHeader
          heading={'Notifications'}
          showTittle={false}
          showSearch={false}
          showMessage={false}
          showCart={userData?.role !== ROLES.Teacher}
          userData={userData}
        />
      )}
      <NotificationPage notifications={notifications} />
    </div>
  )
}

export default Page
