'use client'
import CustomButton from '@/components/CustomComponents/CustomButton/CustomButton'
import {
  createEmailAccount,
  createPhoneAccount,
  signInByEmail,
  signInByPhone,
} from '@/lib/actions/account.actions'
import { useUser } from '@stackframe/stack'
import {
  getToken,
  handleAuthResponse,
  logout,
} from '@/lib/actions/login.actions'
import {
  setUserPhoneNumber,
  userPhoneNumber,
} from '@/lib/redux/slices/metaSlice'
import { useRouter, useSearchParams } from 'next/navigation'
import { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { toast } from 'react-toastify'
import PhoneInput from 'react-phone-input-2'
import 'react-phone-input-2/lib/style.css'
import MobileLogin from '@/components/MobileLogin/MobileLogin'
import GreenTick from '@/components/Icons/GreenTick'
import EmailVerify from '@/components/EmailVerify/EmailVerify'
import CustomModal from '@/components/CustomComponents/CustomModal/CustomModal'
import CustomModalIcon from '@/components/Icons/CustomModalIcon'
import useModal from '@/hooks/useModal'
import { isAbove18 } from '@/utils/utils'
import CustomDatePicker from '@/components/CustomComponents/CustomDatePicker/CustomDatePicker'
import { useAuth } from '@/contexts/AuthContext'

type userDataType = {
  first_name: string
  last_name: string
  phone: string
  dob: string
}

const Page = () => {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { setUser } = useAuth()
  const dispatch = useDispatch()
  const userData: userDataType = JSON.parse(searchParams.get('user') || '{}')
  const [switchLoading, setSwitchLoading] = useState<boolean>(false)
  const [firstName, setFirstName] = useState<string>(
    userData ? userData.first_name : '',
  )
  const [lastName, setLastName] = useState<string>(
    userData ? userData.last_name : '',
  )
  const [email, setEmail] = useState<string>('')
  const [dateOfBirth, setDateOfBirth] = useState<string>(
    userData ? userData.dob : '',
  )
  const [userPhone, setUserPhone] = useState<string>(
    userData ? userData.phone : '',
  )

  const [parentFirstName, setParentFirstName] = useState<string>('')
  const [parentLastName, setParentLastName] = useState<string>('')
  const [parentEmail, setParentEmail] = useState<string>('')
  const [parentPhone, setParentPhone] = useState<string>('')

  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [showParentScreen, setShowParentScreen] = useState<boolean>(false)
  const [useSameContact, setUseSameContact] = useState(true)

  const [emergencyName, setEmergencyName] = useState<string>('')
  const [emergencyEmail, setEmergencyEmail] = useState<string>('')
  const [emergencyPhone, setEmergencyPhone] = useState<string>('')
  const [emergencyRelationship, setEmergencyRelationship] = useState<string>('')
  const [modal, showModal] = useModal()
  const [loginDisableModal, showLoginDisableModal] = useModal()
  const [deletedAccountModal, showDeletedAccountModal] = useModal()

  const phoneNumber = useSelector(userPhoneNumber)
  const [isPhoneVerified, setIsPhoneVerified] = useState<boolean>(
    (userData && userData?.phone) || phoneNumber ? true : false,
  )
  const [isEmailVerified, setIsEmailVerified] = useState<boolean>(false)
  const [disableButton, setDisableButton] = useState<boolean>(false)
  const user = useUser()
  if (!phoneNumber && !user) {
    router.push('/sign-in')
  }

  useEffect(() => {
    if (userData && userData.phone) {
      dispatch(setUserPhoneNumber(userData.phone))
    }
  }, [userData])

  useEffect(() => {
    if (disableButton) {
      showModal({
        title: '',
        contentFn: (onClose: any) => (
          <CustomModal
            isLoading={false}
            desc="We have sent a login link to your email. Please check it."
            icon={<CustomModalIcon />}
            title="Email already exists"
            showBtn={false}
          />
        ),
        closeOnClickOutside: false,
        size: 'md',
        showCloseButton: false,
      })
    }
  }, [disableButton])

  const handleLogout = async () => {
    await logout();
  }

  const handleLoginDisable = () => {
    showLoginDisableModal({
      title: '',
      contentFn: (onClose: any) => (
        <CustomModal
          isLoading={false}
          desc="Your account is disabled. Please contact support."
          icon={<CustomModalIcon />}
          title="Account Disabled"
          showBtn={false}
        />
      ),
      closeOnClickOutside: true,
      size: 'md',
      showCloseButton: false,
    })
    handleLogout();
  }

  const handleDeletedAccountModal = () => {
    showDeletedAccountModal({
      title: '',
      contentFn: (onClose: any) => (
        <CustomModal
          isLoading={false}
          desc="Your account is scheduled for deletion. If you'd like to recover your account, please contact <NAME_EMAIL>"
          icon={<CustomModalIcon />}
          title="Account Deleted"
          showBtn={false}
        />
      ),
      closeOnClickOutside: true,
      size: 'md',
      showCloseButton: false,
    })
  }

  const handleUserDetails = async (e: any) => {
    e.preventDefault()
    if (!firstName || !lastName || (!user && !email) || !dateOfBirth) {
      toast.error('Please fill all the fields!', { autoClose: 1500 })
      return
    }
    if (!isPhoneVerified && phoneNumber != '') {
      toast.error('Please verify your phone number', { autoClose: 1500 })
      return
    }
    const isAbove18Date = isAbove18(dateOfBirth)
    if (!showParentScreen && !isAbove18Date) {
      setShowParentScreen(true)
      toast.error('Please fill parent/guardian details!', { autoClose: 1500 })
      return
    }
    try {
      setIsLoading(true)
      let userDetails: any
      const accessToken = await getToken()
      const emgName = useSameContact ? parentFirstName : emergencyName
      const emgEmail = useSameContact ? parentEmail : emergencyEmail
      const parentPhoneNumber = '+' + parentPhone
      const emergencyPhoneNumber = '+' + emergencyPhone
      const emgPhone = useSameContact ? parentPhoneNumber : emergencyPhoneNumber
      const emgRelationship = useSameContact
        ? 'Parent/Guardian'
        : emergencyRelationship
      if (user && user?.primaryEmail) {
        userDetails = await createEmailAccount(
          accessToken,
          user?.primaryEmail,
          phoneNumber,
          firstName,
          lastName,
          dateOfBirth,
          user?.profileImageUrl || '',
          parentFirstName,
          parentLastName,
          parentEmail,
          parentPhoneNumber,
          useSameContact && !isAbove18Date,
          emgName,
          emgEmail,
          emgPhone,
          emgRelationship,
        )
      } else {
        userDetails = await createPhoneAccount(
          phoneNumber,
          email,
          firstName,
          lastName,
          dateOfBirth,
          user?.profileImageUrl || '',
          parentFirstName,
          parentLastName,
          parentEmail,
          parentPhoneNumber,
          useSameContact,
          emgName,
          emgEmail,
          emgPhone,
          emgRelationship,
        )
      }
      if (userDetails && userDetails?.success) {
        if (user && user?.primaryEmail) {
          userDetails = await signInByEmail(user?.primaryEmail)
        } else {
          userDetails = await signInByPhone(phoneNumber)
        }
        if (userDetails?.Login_Allowed__c === false) {
          handleLoginDisable()
          return
        }
        if (userDetails?.Marked_for_deletion__c === true) {
          handleDeletedAccountModal()
          return
        }
        toast.success('Registration successful', { autoClose: 1500 })
        const filteredData = await handleAuthResponse(userDetails)
        setUser(filteredData)
        router.push('/dashboard')
      } else if (userDetails && userDetails?.success === false) {
        toast.error(userDetails?.data?.errors[0], { autoClose: 1500 })
      }
    } catch (error) {
      toast.error('Something went wrong!' + error, { autoClose: 1500 })
    } finally {
      setIsLoading(false)
    }
  }

  const handleCheckboxChange = () => {
    setUseSameContact(!useSameContact)
    if (useSameContact) {
      setEmergencyName('')
      setEmergencyEmail('')
      setEmergencyPhone('')
      setEmergencyRelationship('')
    }
  }

  const handleChangePhone = (value: string) => {
    setParentPhone(value)
  }

  const handleSwitch = async () => {
    setSwitchLoading(true)
    await logout()
    router.replace('/sign-in')
    setSwitchLoading(false)
  }

  return (
    <div className="flex justify-center items-center my-20">
      {showParentScreen ? (
        <div className="w-full m-3 md:m-0 md:w-max bg-white py-6 md:py-7 px-3 md:px-12 rounded-2xl">
          <h1 className="text-3xl font-medium text-left">
            Parent/Guardian Details
          </h1>
          <form className="space-y-6 mt-4">
            <div className="space-y-1">
              <label htmlFor="fullName" className="block text-sm font-medium">
                First Name
              </label>
              <input
                type="text"
                onChange={(e) => setParentFirstName(e.target.value)}
                value={parentFirstName}
                className="text-sm w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-300"
                placeholder="Enter your parent first name"
                required
              />
            </div>
            <div className="space-y-1">
              <label htmlFor="fullName" className="block text-sm font-medium">
                Last Name
              </label>
              <input
                type="text"
                value={parentLastName}
                onChange={(e) => setParentLastName(e.target.value)}
                className="text-sm w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-300"
                placeholder="Enter your parent last name"
                required
              />
            </div>
            <div className="space-y-1">
              <label htmlFor="fullName" className="block text-sm font-medium">
                Email
              </label>
              <input
                type="text"
                onChange={(e) => setParentEmail(e.target.value)}
                className="text-sm w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-300"
                placeholder="Enter your parent email"
                value={parentEmail}
                required
              />
            </div>
            <div className="space-y-1">
              <label
                className="text-sm font-medium label-color mb-1"
                htmlFor="mobile"
              >
                Mobile
              </label>
              <PhoneInput
                country={'us'}
                value={parentPhone}
                onChange={handleChangePhone}
                inputStyle={{ width: '100%' }}
                containerStyle={{ marginBottom: '1rem' }}
              />
            </div>

            {/* Emergency Contact Section */}
            <div className="space-y-1">
              <label className="inline-flex items-center">
                <input
                  type="checkbox"
                  checked={useSameContact}
                  onChange={handleCheckboxChange}
                  className="form-checkbox"
                />
                <span className="ml-2 text-sm font-medium">
                  My emergency contact details are the same as my
                  parent/guardian details.
                </span>
              </label>
            </div>

            {!useSameContact && (
              <div className="space-y-6 mt-4">
                <h2 className="text-2xl font-medium">
                  Emergency Contact Details
                </h2>
                <div className="space-y-1">
                  <label
                    htmlFor="emergencyName"
                    className="block text-sm font-medium"
                  >
                    Name
                  </label>
                  <input
                    type="text"
                    value={emergencyName}
                    onChange={(e) => setEmergencyName(e.target.value)}
                    className="text-sm w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-300"
                    placeholder="Enter emergency contact name"
                    required
                  />
                </div>
                <div className="space-y-1">
                  <label
                    htmlFor="emergencyEmail"
                    className="block text-sm font-medium"
                  >
                    Email
                  </label>
                  <input
                    type="text"
                    value={emergencyEmail}
                    onChange={(e) => setEmergencyEmail(e.target.value)}
                    className="text-sm w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-300"
                    placeholder="Enter emergency contact email"
                    required
                  />
                </div>
                <div className="space-y-1">
                  <label
                    htmlFor="emergencyPhone"
                    className="block text-sm font-medium"
                  >
                    Phone
                  </label>
                  <PhoneInput
                    country={'us'}
                    value={emergencyPhone}
                    onChange={(value) => setEmergencyPhone(value)}
                    inputStyle={{ width: '100%' }}
                    containerStyle={{ marginBottom: '1rem' }}
                  />
                </div>
                <div className="space-y-1">
                  <label
                    htmlFor="relationship"
                    className="block text-sm font-medium"
                  >
                    Relationship
                  </label>
                  <input
                    type="text"
                    value={emergencyRelationship}
                    onChange={(e) => setEmergencyRelationship(e.target.value)}
                    className="text-sm w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-300"
                    placeholder="Enter relationship"
                    required
                  />
                </div>
              </div>
            )}
            <div className="space-y-1">
              <CustomButton
                title="Save and Proceed"
                onClick={handleUserDetails}
                isLoading={isLoading}
                height={12}
              />
            </div>
          </form>
        </div>
      ) : (
        <div className="w-full m-3 md:m-0 md:w-4/12 bg-white py-6 md:py-7 px-3 md:px-12 rounded-2xl">
          <h1 className="text-3xl font-medium text-left">Personal Details</h1>
          <form className="space-y-6 mt-4">
            <div className="space-y-1">
              <label htmlFor="fullName" className="block text-sm font-medium">
                First Name
              </label>
              <input
                type="text"
                onChange={(e) => setFirstName(e.target.value)}
                value={firstName}
                className="text-sm w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-300"
                placeholder="Enter your first name"
                required
              />
            </div>
            <div className="space-y-1">
              <label htmlFor="fullName" className="block text-sm font-medium">
                Last Name
              </label>
              <input
                type="text"
                onChange={(e) => setLastName(e.target.value)}
                value={lastName}
                className="text-sm w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-300"
                placeholder="Enter your last name"
                required
              />
            </div>
            {phoneNumber || isPhoneVerified ? (
              <div className="space-y-1">
                <label htmlFor="fullName" className="block text-sm font-medium">
                  Phone
                </label>
                <div className="relative">
                  <input
                    type="text"
                    value={phoneNumber || userPhone}
                    readOnly={true}
                    className="text-sm w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-300"
                    placeholder="Enter your number"
                    required
                  />
                  <span className="absolute right-3 top-3 text-sm font-semibold text-color-green-4 flex items-center gap-2 cursor-pointer">
                    <GreenTick />
                    Verified
                  </span>
                </div>
              </div>
            ) : (
              <MobileLogin
                showOtpBtn={false}
                setIsPhoneVerified={setIsPhoneVerified}
                setUserPhone={setUserPhone}
                isMobileVerificationRequired={true}
              />
            )}
            <EmailVerify
              user={user}
              email={email}
              setEmail={setEmail}
              phoneNumber={phoneNumber}
              setIsEmailVerified={setIsEmailVerified}
              isEmailVerified={isEmailVerified}
              setDisableButton={setDisableButton}
            />
            <CustomDatePicker
              key={dateOfBirth}
              title="Date of Birth"
              setDateOfBirth={setDateOfBirth}
              dateOfBirth={dateOfBirth}
            />
            <div className="space-y-5">
              {!disableButton && (
                <CustomButton
                  title="Save and Proceed"
                  onClick={handleUserDetails}
                  isLoading={isLoading}
                  height={12}
                />
              )}
              <CustomButton
                title="Switch to another account"
                onClick={handleSwitch}
                isLoading={switchLoading}
                height={12}
                backgroundColor="bg-white"
                classes="text-gray-700 border border-gray-200 hover:bg-gray-50"
              />
            </div>
          </form>
        </div>
      )}
      {modal}
      {loginDisableModal}
      {deletedAccountModal}
    </div>
  )
}

export default Page
