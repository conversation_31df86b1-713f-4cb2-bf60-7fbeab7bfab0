import BackArrow from '@/components/Icons/BackArrow'
import PageHeader from '@/components/PageHeader/PageHeader'
import React from 'react'
import { getEventById, isRegisteredForClass } from '@/lib/actions/class.actions'
import { getToken, handleLoggedIn } from '@/lib/actions/login.actions'
import EventClubDetailPage from '@/components/EventClubDetailPage/EventClubDetailPage'
interface PageProps {
  params: {
    id: string
  }
}
const Page = async ({ params }: PageProps) => {
  let [eventDetail, userData]: [any, any] = await Promise.all([
    getEventById(params?.id),
    handleLoggedIn(),
  ])
  eventDetail = eventDetail?.data
  const token = await getToken()
  let isAlreadyEnrolled = false
  if (token) {
    const enrolledClass = await isRegisteredForClass(token, params?.id)
    isAlreadyEnrolled = enrolledClass?.data
  }
  return (
    <div className="w-screen md:w-full">
      {userData && (
        <PageHeader
          heading={'Events'}
          showTittle={false}
          showSearch={false}
          showMessage={false}
          userData={userData}
        />
      )}
      <div className="pt-7 px-7">
        <BackArrow />
      </div>
      <div className="p-5 space-y-5">
        <EventClubDetailPage
          data={eventDetail}
          userData={userData}
          isAlreadyEnrolled={isAlreadyEnrolled}
        />
      </div>
    </div>
  )
}

export default Page
