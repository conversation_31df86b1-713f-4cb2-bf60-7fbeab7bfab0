import React from 'react'
import <PERSON>Header from '@/components/PageHeader/PageHeader'
import BackArrow from '@/components/Icons/BackArrow'
import { getToken, handleLoggedIn } from '@/lib/actions/login.actions'
import dayjs from 'dayjs'
import YouthDetail from '@/components/YouthDetail/YouthDetail'
import {
  getAllYouthMeetings,
  getYouthAccountById,
  getYouthClasses,
} from '@/lib/actions/parent.actions'
import NotFound from '@/app/not-found'
import { ROLES } from '@/types/enum'

const Page = async ({ params }: { params: { id: string } }) => {
  const startDate = dayjs().format('YYYY-MM-DD')
  const endDate = dayjs().endOf('month').format('YYYY-MM-DD')
  let accessToken = await getToken('access')

  const [userData, youthDetail, youthMeetings, previousClasses] =
    await Promise.all([
      handleLoggedIn(),
      getYouthAccountById(accessToken, params?.id),
      getAllYouthMeetings(accessToken, params?.id, startDate, endDate),
      getYouthClasses(accessToken, params?.id, 'Previous'),
    ])

  return (
    <div className="w-screen md:w-full">
      {userData && (
        <PageHeader
          heading={'Student Details'}
          showTittle={false}
          showSearch={false}
          showMessage={false}
          userData={userData}
          showCart={userData?.role != ROLES.Teacher}
        />
      )}
      <div className="px-6 pt-6 pb-2 max-w-6xl flex items-center">
        {' '}
        <BackArrow />
      </div>
      {youthDetail?.success ? (
        <YouthDetail
          youthDetail={youthDetail}
          youthMeetings={youthMeetings}
          previousClasses={previousClasses}
        />
      ) : (
        <NotFound />
      )}
    </div>
  )
}

export default Page
