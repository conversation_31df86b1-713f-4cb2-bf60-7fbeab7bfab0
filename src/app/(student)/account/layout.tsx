import PageHeader from '@/components/PageHeader/PageHeader'
import { handleLoggedIn } from '@/lib/actions/login.actions'
import { ROLES } from '@/types/enum'
import { headers } from 'next/headers'

export default async function Layout({
  children,
}: {
  children: React.ReactNode
}) {
  const userData: any = await handleLoggedIn()
  const pathname = headers().get('x-pathname') || '/'
  const allowed = [
    '/account',
    '/account/account-details',
    '/account/address',
    '/account/credits',
    '/account/my-muse-membership',
    '/account/my-public-profile',
    '/account/payment-methods',
  ]

  const showHeader = userData && allowed.includes(pathname)
  return (
    <div className="w-full">
      <div className="w-screen md:w-full">
        {showHeader && (
          <PageHeader
            heading={'My Account'}
            showTittle={false}
            showSearch={false}
            showCart={userData?.role != ROLES.Teacher}
            showMessage={false}
            userData={userData}
          />
        )}
      </div>
      <main className="">{children}</main>
    </div>
  )
}
