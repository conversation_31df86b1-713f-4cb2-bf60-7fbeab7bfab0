import AddressCard from '@/components/AddressCard/AddressCard'
import { handleLoggedIn } from '@/lib/actions/login.actions'
import React from 'react'

const Page = async () => {
  const userData = await handleLoggedIn()
  return (
    <>
      <div className="w-screen md:w-full border-t border-gray-200 px-7 py-2 bg-white">
        <h2 className="text-xl font-medium">Address</h2>
      </div>
      <div className="w-screen md:w-full">
        <div className="w-full flex justify-center">
          <div className="flex items-center flex-col justify-center w-11/12 md:w-6/12 my-5">
            <div className="flex  w-full flex-col gap-5 items-center justify-center mt-5">
              <AddressCard userData={userData} />
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

export default Page
