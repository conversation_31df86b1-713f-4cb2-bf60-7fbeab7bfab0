import ProfileSetting from '@/components/ProfileSetting/ProfileSetting'
import { handleLoggedIn } from '@/lib/actions/login.actions'
import React from 'react'

const Page = async () => {
  const userData = await handleLoggedIn()
  return (
    <>
      <div className="w-screen md:w-full border-t border-gray-200 px-7 py-2 bg-white">
        <h2 className="text-xl font-medium">My Public Profile</h2>
      </div>
      <div className="w-screen md:w-full">
        <div className="w-full flex justify-center">
          <div className="w-11/12 md:w-5/12 my-5">
            <div className="mt-5 bg-white rounded-2xl py-5">
              <ProfileSetting userData={userData} />
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

export default Page
