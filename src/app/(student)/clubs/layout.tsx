import PageHeader from '@/components/PageHeader/PageHeader'
import { handleLoggedIn } from '@/lib/actions/login.actions'

export default async function Layout({
  children,
}: {
  children: React.ReactNode
}) {
  const userData: any = await handleLoggedIn()
  return (
    <div className="w-full">
      <div className="w-screen md:w-full">
        {userData && (
          <PageHeader
            heading={'Clubs'}
            showTittle={false}
            showSearch={false}
            showMessage={false}
            userData={userData}
          />
        )}
      </div>
      <main className="">{children}</main>
    </div>
  )
}
