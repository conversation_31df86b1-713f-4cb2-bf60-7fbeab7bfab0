import BackArrow from '@/components/Icons/BackArrow'
import React from 'react'
import {
  getClubById,
  getEventById,
  isRegisteredForClass,
} from '@/lib/actions/class.actions'
import EventClubDetailPage from '@/components/EventClubDetailPage/EventClubDetailPage'
import { getToken, handleLoggedIn } from '@/lib/actions/login.actions'

interface PageProps {
  params: {
    id: string
  }
}

const Page = async ({ params }: PageProps) => {
  let clubDetail: any = await getClubById(params.id)
  clubDetail = clubDetail?.data
  let [eventDetail, userData]: [any, any] = await Promise.all([
    getEventById(params?.id),
    handleLoggedIn(),
  ])
  eventDetail = eventDetail?.data
  const token = await getToken()
  let isAlreadyEnrolled = false
  if (token) {
    const enrolledClass = await isRegisteredForClass(token, params?.id)
    isAlreadyEnrolled = enrolledClass?.data
  }
  return (
    <div className="w-screen md:w-full">
      <div className="p-3 md:p-7">
        <BackArrow />
      </div>

      <div className="p-5 space-y-5">
        <EventClubDetailPage
          data={clubDetail}
          userData={userData}
          isAlreadyEnrolled={isAlreadyEnrolled}
        />
      </div>
    </div>
  )
}

export default Page
