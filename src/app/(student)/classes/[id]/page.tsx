import BackArrow from '@/components/Icons/BackArrow'
import PageHeader from '@/components/PageHeader/PageHeader'
import { getAccountBadges } from '@/lib/actions/account.actions'
import {
  getClassById,
  isApprovalRequestedForClass,
  isRegisteredForClass,
} from '@/lib/actions/class.actions'
import { getToken, handleLoggedIn } from '@/lib/actions/login.actions'
import { getTeacherClassDetails } from '@/lib/actions/teacher.actions'
import { ROLES } from '@/types/enum'
import React from 'react'
import StudentClassDetail from '@/components/StudentClassDetail/StudentClassDetail'
import TeacherClassDetail from '@/components/TeacherClassDetail/TeacherClassDetail'
import { cache } from 'react'
import { getYouthAccounts } from '@/lib/actions/parent.actions'

interface PageProps {
  params: {
    id: string
  }
}

const getClassDetailsCached = cache(
  async (classId: string, role: string, token: string) => {
    if (role === ROLES.Teacher) {
      return await getTeacherClassDetails(token, classId).then(
        (res) => res?.data,
      )
    }
    return await getClassById(classId).then((res) => res?.data)
  },
)

export async function generateMetadata({ params }: PageProps) {
  const token = await getToken()
  const userData: any = await handleLoggedIn()
  const classDetails = await getClassDetailsCached(
    params?.id,
    userData?.role,
    token,
  )

  const title = classDetails?.Title__c || 'Class Detail'
  return {
    title: `${title} - The Muse Writers Center`,
  }
}

const Page = async ({ params }: PageProps) => {
  const userData: any = await handleLoggedIn()
  const classId = params?.id
  const token = await getToken()

  const [classDetails, userBadges] = await Promise.all([
    getClassDetailsCached(classId, userData?.role, token),
    token ? getAccountBadges(token) : null,
  ])

  let isPrerequisiteSatisfied = false
  let isAlreadyEnrolled = false

  if (token && userBadges && !userBadges?.data?.errors) {
    isPrerequisiteSatisfied =
      !classDetails?.prerequisiteBadges?.length ||
      classDetails?.prerequisiteBadges?.every((badge: any) =>
        userBadges?.data?.some((userBadge: any) => userBadge.id === badge.id),
      )
  }

  if (token) {
    const enrolledClass = await isRegisteredForClass(token, classId)
    isAlreadyEnrolled = enrolledClass?.data
  }

  if (!userData) isPrerequisiteSatisfied = true
  let isApprovalRaised = false
  if (userData?.role === ROLES.Youth_Student) {
    const approvalRaised = await isApprovalRequestedForClass(token, classId)
    isApprovalRaised = approvalRaised?.data
  }

  let parentChildren: any = []
  if (userData?.role === ROLES.Parent) {
    const parentData = await getYouthAccounts(token)
    parentChildren = parentData?.data || []
  }
  return (
    <div className="w-screen md:w-full">
      {userData && (
        <PageHeader
          heading={'Explore Classes'}
          showTittle={false}
          showSearch={false}
          showMessage={false}
          userData={userData}
          showCart={userData?.role != ROLES.Teacher}
        />
      )}
      <div className="p-5 space-y-5">
        <BackArrow />
        {userData?.role === ROLES.Teacher ? (
          <TeacherClassDetail classDetails={classDetails} />
        ) : (
          <StudentClassDetail
            classDetails={classDetails}
            isPrerequisiteSatisfied={isPrerequisiteSatisfied}
            isAlreadyEnrolled={isAlreadyEnrolled}
            userData={userData}
            userBadges={userBadges}
            isApprovalRaised={isApprovalRaised}
            parentChildren={parentChildren}
          />
        )}
      </div>
    </div>
  )
}

export default Page
