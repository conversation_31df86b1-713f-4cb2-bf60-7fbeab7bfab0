import DynamicForm from '@/components/DynamicForm/DynamicForm'
import ContentRenderer from '@/components/ContentRenderer/ContentRenderer'
import { getFormById } from '@/lib/actions/form.actions'
import { getPageContent } from '@/lib/actions/page.actions'
import { getClassList } from '@/lib/actions/class.actions'
import React from 'react'

interface PageProps {
  params: {
    slug: string
  }
}

const page = async ({ params }: PageProps) => {
  const pageData: any = await getPageContent(params?.slug)

  // Extract form IDs from content (supports both forms and carousels)
  const extractFormIds = (content: string): string[] => {
    const formIds: string[] = []

    // Decode HTML entities
    const decodedContent = content
      .replace(/&quot;/g, '"')
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&#91;/g, '[')
      .replace(/&#93;/g, ']')

    // Find form shortcodes only (carousels are handled separately)
    const formRegex = /\[form[^\]]*\]/gi
    let match

    while ((match = formRegex.exec(decodedContent)) !== null) {
      const shortcodeText = match[0]

      // Clean HTML tags
      const cleanShortcode = shortcodeText.replace(/<[^>]*>/g, '')

      // Extract ID
      const idMatch = cleanShortcode.match(/id\s*=\s*["']([^"']+)["']/i)
      if (idMatch) {
        formIds.push(idMatch[1])
      }
    }

    return formIds
  }

  // Extract carousel IDs from content
  const extractCarouselIds = (content: string): string[] => {
    const carouselIds: string[] = []

    // Decode HTML entities
    const decodedContent = content
      .replace(/&quot;/g, '"')
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&#91;/g, '[')
      .replace(/&#93;/g, ']')

    // Find carousel shortcodes
    const carouselRegex = /\[events-carousel[^\]]*\]/gi
    let match

    while ((match = carouselRegex.exec(decodedContent)) !== null) {
      const shortcodeText = match[0]

      // Clean HTML tags
      const cleanShortcode = shortcodeText.replace(/<[^>]*>/g, '')

      // Extract ID and type
      const idMatch = cleanShortcode.match(/id\s*=\s*["']([^"']+)["']/i)
      const typeMatch = cleanShortcode.match(/type\s*=\s*["']([^"']+)["']/i)

      if (idMatch && typeMatch && typeMatch[1] === 'classes') {
        carouselIds.push(idMatch[1])
      }
    }

    return carouselIds
  }

  // Get form IDs from content
  const formIdsFromContent = pageData?.Content__c
    ? extractFormIds(pageData.Content__c)
    : []

  // Get carousel IDs from content
  const carouselIdsFromContent = pageData?.Content__c
    ? extractCarouselIds(pageData.Content__c)
    : []

  // Include main form ID if exists
  const allFormIds = [...formIdsFromContent]
  if (pageData?.Form_ID__c && !allFormIds.includes(pageData.Form_ID__c)) {
    allFormIds.push(pageData.Form_ID__c)
  }

  const formDataMap: Record<string, any> = {}
  const classListDataMap: Record<string, any> = {}

  // Fetch form data
  for (const formId of allFormIds) {
    try {
      const formData = await getFormById(formId)
      if (formData) {
        formDataMap[formId] = formData
      }
    } catch (error) {
      console.error(`Failed to fetch form ${formId}:`, error)
    }
  }

  // Fetch class list data for carousels
  for (const carouselId of carouselIdsFromContent) {
    try {
      const classListResponse = await getClassList(carouselId)
      if (classListResponse?.success && classListResponse?.data) {
        classListDataMap[carouselId] = classListResponse.data
      }
    } catch (error) {
      console.error(`Failed to fetch class list ${carouselId}:`, error)
    }
  }

  return (
    <div className="w-3/4 py-10 mx-auto">
      <h1 className="font-bold text-5xl">{pageData?.Title__c}</h1>
      <div className="mt-10">
        {pageData?.Content__c ? (
          <ContentRenderer
            content={pageData.Content__c}
            formDataMap={formDataMap}
            classListDataMap={classListDataMap}
          />
        ) : (
          <p>No content available</p>
        )}
      </div>

      {/* Only show main form if not already in content */}
      {pageData?.Form_ID__c &&
        formDataMap[pageData.Form_ID__c] &&
        formDataMap[pageData.Form_ID__c].Schema__c &&
        !formIdsFromContent.includes(pageData.Form_ID__c) && (
          <div className="w-full flex items-start mt-10">
            <div className="w-1/2">
              {(() => {
                try {
                  const parsedSchema = JSON.parse(
                    formDataMap[pageData.Form_ID__c].Schema__c,
                  )
                  return (
                    <DynamicForm id={pageData.Form_ID__c} data={parsedSchema} />
                  )
                } catch (error) {
                  console.error('Error parsing main form schema:', error)
                  return (
                    <div className="p-4 bg-red-100 border border-red-400 rounded">
                      <p className="text-red-800">Error loading form schema</p>
                    </div>
                  )
                }
              })()}
            </div>
          </div>
        )}
    </div>
  )
}

export default page
