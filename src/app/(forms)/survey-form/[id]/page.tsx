import SurveyForm from '@/components/SurveyForm/SurveyForm'
import { getClassById } from '@/lib/actions/class.actions'
import React from 'react'

interface PageProps {
  params: {
    id: string
  }
}

const Page = async ({ params }: PageProps) => {
  let classDetails = await getClassById(params.id)
  return (
    <>
      <SurveyForm classData={classDetails?.data} />
    </>
  )
}

export default Page
