import TeacherTeachingForm from '@/components/TeacherTeachingForm/TeacherTeachingForm'
import { handleLoggedIn } from '@/lib/actions/login.actions'
import { getTeacherOboardingProgramOption } from '@/lib/actions/account.actions'
import React from 'react'

const page = async () => {
  const [userData, programOptions] = await Promise.all([
    handleLoggedIn(),
    getTeacherOboardingProgramOption(),
  ])

  return (
    <div className="flex items-center justify-center">
      <div className="w-full md:w-3/4 my-5">
        <TeacherTeachingForm 
          userData={userData} 
          programOptions={programOptions?.data || []}
        />
      </div>
    </div>
  )
}

export default page
