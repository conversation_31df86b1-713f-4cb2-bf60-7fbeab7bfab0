import TutionAssitanceForm from '@/components/TutionAssitanceForm/TutionAssitanceForm'
import { getTuitionAssistancePayOption } from '@/lib/actions/account.actions'
import { handleLoggedIn } from '@/lib/actions/login.actions'
import React from 'react'

const page = async () => {
  const [payOptions, userData] = await Promise.all([
    getTuitionAssistancePayOption(),
    handleLoggedIn(),
  ])
  return (
    <>
      <TutionAssitanceForm
        payOptions={payOptions?.data || []}
        userData={userData}
      />
    </>
  )
}

export default page
