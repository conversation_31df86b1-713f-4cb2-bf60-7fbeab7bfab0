import PageHeader from '@/components/PageHeader/PageHeader'
import TeacherClassPage from '@/components/TeacherClassPage/TeacherClassPage'
import { handleLoggedIn } from '@/lib/actions/login.actions'
import { capitalizeFirstLetter } from '@/utils/utils'
import React from 'react'

interface pageProps {
  params: {
    type: string
  }
}

const page = async ({ params }: pageProps) => {
  const userData: any = await handleLoggedIn()
  const pageTitle = capitalizeFirstLetter(params.type)
  return (
    <div className="w-screen md:w-full">
      {userData && (
        <PageHeader
          heading={`${pageTitle} Classes`}
          showTittle={false}
          showSearch={false}
          showMessage={false}
          showCart={false}
          userData={userData}
        />
      )}
      <TeacherClassPage classType={pageTitle} />
    </div>
  )
}

export default page
