'use client'
import React from 'react'
import { useRouter } from 'next/navigation'
import PaymentFailedIcon from '@/components/Icons/PaymentFailedIcon'
import { useSelector } from 'react-redux'
import jwt from 'jsonwebtoken'
import { paymentToken } from '@/lib/redux'
import { PriceDetails } from '@/utils/interface'

export default function Page() {
  const router = useRouter()
  const token = useSelector(paymentToken)

  // Only redirect if no token after a short delay (allows for token to be set)
  React.useEffect(() => {
    const timer = setTimeout(() => {
      if (!token) {
        router.push('/dashboard')
      }
    }, 1000)

    return () => clearTimeout(timer)
  }, [token, router])

  const handleRedirect = () => {
    let paymentDetails: PriceDetails = {
      userId: '',
      entityId: '',
      entityType: '',
      amount: 0,
      walletBalance: 0,
      isWalletEnabled: false,
      iat: 0,
      discountedAmount: 0,
      paymentType: '',
    }

    if (token) {
      paymentDetails = (jwt.decode(token) as PriceDetails) || paymentDetails
    }

    switch (paymentDetails.entityType) {
      case 'Cart':
        router.push('/cart')
        break
      case 'Donation':
        router.push('/donations')
        break
      default:
        router.push('/dashboard')
    }
  }

  return (
    <div className="flex item-center justify-center py-20 w-full h-[100%]">
      <div className="flex flex-col justify-center items-center">
        <PaymentFailedIcon />
        <h1 className="font-extrabold text-3xl mt-5 mb-3">Payment failed!</h1>
        <p className="text-sm">
          Your payment couldn&#39;t be processed at this time. If any amount has
          been debited
        </p>
        <p className="text-sm">
          {' '}
          from your account, it will be automatically refunded within 2-5
          working days.
        </p>
        <div className="flex items-center gap-3 text-sm mt-3 font-semibold">
          <button
            className="bg-color-yellow py-4 px-6 rounded-md h-12 flex items-center justify-center"
            onClick={handleRedirect}
          >
            Retry Payment
          </button>
        </div>
        <p className="text-xs mt-32">
          Need help or have questions? Reach out to our support team.
        </p>
      </div>
    </div>
  )
}
