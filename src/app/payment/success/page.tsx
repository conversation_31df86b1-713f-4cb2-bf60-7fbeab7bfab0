'use client'
import React, { useMemo } from 'react'
import { useRouter } from 'next/navigation'
import PaymentSuccessIcon from '@/components/Icons/PaymentSuccessIcon'
import { useSelector } from 'react-redux'
import { paymentToken } from '@/lib/redux'
import { PriceDetails } from '@/utils/interface'
import jwt from 'jsonwebtoken'

export default function Page() {
  const router = useRouter()
  const token = useSelector(paymentToken)

  // Only redirect if no token after a short delay (allows for token to be set)
  React.useEffect(() => {
    const timer = setTimeout(() => {
      if (!token) {
        router.push('/dashboard')
      }
    }, 1000)

    return () => clearTimeout(timer)
  }, [token, router])

  const tokenData = useMemo(() => {
    let paymentDetails: PriceDetails = {
      userId: '',
      entityId: '',
      entityType: '',
      amount: 0,
      walletBalance: 0,
      isWalletEnabled: false,
      iat: 0,
      discountedAmount: 0,
      paymentType: '',
    }
    if (token) {
      return jwt.decode(token) as PriceDetails
    }
    return paymentDetails
  }, [token])

  return (
    <div className="flex item-center justify-center py-20 w-full h-[100%]">
      <div className="flex flex-col justify-center items-center">
        <PaymentSuccessIcon />
        <h1 className="font-extrabold text-3xl mt-5 mb-3">
          {tokenData.entityType === 'Donation' ? 'Donation' : 'Registration'}{' '}
          Successful
        </h1>
        <p className="text-sm">
          {tokenData.entityType === 'Donation'
            ? 'Thank you for your generosity! Your contribution has been received and will directly support our mission.'
            : 'Thank you for choosing us! Your registration is successful.'}
        </p>
        <p className="text-sm">
          {tokenData.entityType === 'Donation'
            ? 'Please check your email for a receipt and confirmation details.'
            : 'Please check your email for the receipt and confirmation details.'}
        </p>
        <div className="flex items-center gap-3 text-sm mt-3 font-semibold">
          <button
            className="bg-color-yellow py-4 px-6 rounded-md h-12 flex items-center justify-center"
            onClick={() => router.push('/dashboard')}
          >
            Return to Home
          </button>
        </div>
        <p className="text-xs mt-32">
          Need help or have questions? Reach out to{' '}
          <a href="mailto:<EMAIL>" className="text-color-yellow">
            <EMAIL>
          </a>
        </p>
      </div>
    </div>
  )
}
