import PaymentForm from '@/components/PaymentForm/PaymentForm'
import { processIvoice } from '@/lib/actions/invoice.actions'
import { handleLoggedIn } from '@/lib/actions/login.actions'
import { PriceDetails } from '@/utils/interface'
import jwt from 'jsonwebtoken'

const page = async ({ params }: { params: { id: string } }) => {
  const invoiceDetails = await processIvoice(params?.id)
  const userData = await handleLoggedIn()
  let paymentDetails: PriceDetails = {
    userId: '',
    entityId: '',
    entityType: '',
    amount: 0,
    walletBalance: 0,
    isWalletEnabled: false,
    iat: 0,
    discountedAmount: 0,
    paymentType: '',
  }
  if (invoiceDetails.success) {
    paymentDetails =
      (jwt.decode(invoiceDetails.data?.token) as PriceDetails) || paymentDetails
  }
  return (
    <>
      <div></div>
      <div className="w-full flex justify-center">
        <div className="w-11/12 md:w-9/12 mt-5">
          <PaymentForm
            data={paymentDetails}
            userData={userData}
            invoiceToken={invoiceDetails.data?.token}
          />
        </div>
      </div>
    </>
  )
}

export default page
