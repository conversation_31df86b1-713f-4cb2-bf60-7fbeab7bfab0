import React from 'react'
import <PERSON>Header from '@/components/PageHeader/PageHeader'
import BackArrow from '@/components/Icons/BackArrow'
import { handleLoggedIn } from '@/lib/actions/login.actions'
import AuthorDetail from '@/components/AuthorDetail/AuthorDetail'
import { getAuthorDetailsById } from '@/lib/actions/account.actions'
import NotFound from '@/app/not-found'

const Page = async ({ params }: { params: { id: string } }) => {
  const [userData, author] = await Promise.all([
    handleLoggedIn(),
    getAuthorDetailsById(params?.id),
  ])

  return (
    <div className="w-screen md:w-full">
      {userData && (
        <PageHeader
          heading={'Author Details'}
          showTittle={false}
          showSearch={false}
          showMessage={false}
          userData={userData}
        />
      )}
      <div className="px-6 pt-6 pb-2 max-w-6xl flex items-center">
        {' '}
        <BackArrow />
      </div>
      {author?.success ? <AuthorDetail author={author} /> : <NotFound />}
    </div>
  )
}

export default Page
