import LoginComponent from '@/components/LoginComponent/LoginComponent'
import { OAuthButtonGroup } from '@stackframe/stack'

const Page = async () => {
  return (
    <div
      className="flex justify-center items-center"
      style={{ height: 'calc(100vh - 117px)' }}
    >
      <div className="mx-3 md:mx-0 w-full md:w-3/12 bg-white py-7 px-7 rounded-2xl">
        <div className="flex justify-center items-center flex-col mb-4">
          <h2 className="text-2xl font-semibold w-full text-black">
            Login or Sign up
          </h2>
        </div>
        <OAuthButtonGroup
          type="sign-in"
          mockProject={{
            config: {
              oauthProviders: [{ id: 'google' }],
            },
          }}
        />
        <div className="flex items-center justify-center my-3 stack-scope">
          <div className="mx-2 text-sm text-zinc-500">Or</div>
        </div>
        <LoginComponent />
      </div>
    </div>
  )
}

export default Page
