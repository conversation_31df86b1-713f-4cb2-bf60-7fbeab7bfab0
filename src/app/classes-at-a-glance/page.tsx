import ClassesAtGlance from '@/components/ClassesAtGlance/ClassesAtGlance'
import PageHeader from '@/components/PageHeader/PageHeader'
import { getClassesGroupedByGenre } from '@/lib/actions/class.actions'
import { handleLoggedIn } from '@/lib/actions/login.actions'
import React from 'react'

const page = async () => {
  const [userData, classesData] = await Promise.all([
    handleLoggedIn(),
    getClassesGroupedByGenre(),
  ])
  return (
    <div>
      {userData && (
        <PageHeader
          heading={'Classes at a Glance'}
          showTittle={false}
          showSearch={false}
          showMessage={false}
          userData={userData}
        />
      )}
      <ClassesAtGlance classesData={classesData?.data || []} />
    </div>
  )
}

export default page
