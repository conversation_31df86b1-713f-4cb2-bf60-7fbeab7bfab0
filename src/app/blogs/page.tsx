import BlogCard from '@/components/BlogCard/BlogCard'
import BlogThisWeek from '@/components/BlogThisWeek/BlogThisWeek'
import CustomPagination from '@/components/CustomComponents/CustomPagination/CustomPagination'
import ThunderIcon from '@/components/Icons/ThunderIcon'
import NextBlogRead from '@/components/NextBlogRead/NextBlogRead'
import RightColumnHoc from '@/components/RightColumnHoc/RightColumnHoc'
import SubscriptionForm from '@/components/SubscriptionForm/SubscriptionForm'
import { getAllBlogs } from '@/lib/actions/blog.actions'
import React from 'react'

const Page = async ({
  searchParams,
}: {
  searchParams: { page?: string; tag?: string }
}) => {
  const blogData = await getAllBlogs(
    10,
    searchParams?.page ? Number(searchParams.page) * 10 : 0,
  )
  const blog = blogData?.data || []
  return (
    <div className="w-full flex flex-col justify-between items-center bg-white">
      <div className="w-8/12 mt-14 flex flex-col justify-center gap-5">
        {searchParams?.tag ? (
          <h1 className="text-4xl font-extrabold">{searchParams.tag}</h1>
        ) : (
          <>
            <h1 className="text-4xl font-extrabold flex items-center justify-start gap-2">
              •Writer’s Bolt <ThunderIcon />
            </h1>
            <p className="font-semibold text-xl">
              Updates, Events, Inspiration, and more from our writing community.
            </p>
          </>
        )}
      </div>
      <div className="w-full border border-color-grey-2 mt-14"></div>
      <div className="w-8/12">
        <div className="grid grid-cols-10 mt-10 gap-16">
          <div className="col-span-6">
            <div className="flex flex-col gap-10">
              {blog.map((blog) => (
                <BlogCard key={blog?.id} data={blog} />
              ))}
            </div>
            <div className="my-5">
              <CustomPagination
                activePage={!searchParams?.page ? 1 : Number(searchParams.page)}
                total={blogData?.total}
              />
            </div>
          </div>
          <div className="col-span-4">
            <RightColumnHoc top={10}>
              <div className="flex flex-col gap-16">
                {searchParams?.tag && <NextBlogRead />}
                <BlogThisWeek />
                {!searchParams?.tag && <SubscriptionForm />}
              </div>
            </RightColumnHoc>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Page
