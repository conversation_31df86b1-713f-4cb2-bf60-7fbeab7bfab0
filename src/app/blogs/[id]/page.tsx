import BlogTags from '@/components/BlogTags/BlogTags'
import BlogThisWeek from '@/components/BlogThisWeek/BlogThisWeek'
import BackArrow from '@/components/Icons/BackArrow'
import InstagramSVG from '@/components/Icons/InstagramSVG'
import Linkedin<PERSON><PERSON> from '@/components/Icons/LinkedinSVG'
import Twitter<PERSON><PERSON> from '@/components/Icons/TwitterSVG'
import WebsiteGlobalcon from '@/components/Icons/WebsiteGlobalcon'
import NextBlogRead from '@/components/NextBlogRead/NextBlogRead'
import RightColumnHoc from '@/components/RightColumnHoc/RightColumnHoc'
import { getBlogById } from '@/lib/actions/blog.actions'
import { formatDateTime } from '@/utils/utils'
import Link from 'next/link'
import React from 'react'

interface PageProps {
  params: {
    id: string
  }
}

const Page = async ({ params }: PageProps) => {
  const blogData: any = await getBlogById(params.id)
  const dateTime = formatDateTime(blogData[0]?.createdDate)
  return (
    <div className="w-full flex flex-col justify-between items-center mb-10">
      <div className="w-8/12 mt-5">
        <BackArrow />
        <div className="grid grid-cols-10 mt-5 gap-10">
          <div className="col-span-7 bg-white pl-10 border-l-8 border-l-color-blue-3 rounded-r-2xl">
            <main className="max-w-3xl mx-auto px-4 py-8">
              <article className="prose lg:prose-xl">
                {/* Header */}
                <div className="mb-8">
                  <h1 className="text-3xl font-bold mb-4">
                    {blogData[0]?.title}
                  </h1>
                  <div className="flex items-center gap-2 font-semibold text-color-grey-3 mb-6">
                    <span>By</span>
                    <a href="#" className="text-color-blue-2 hover:underline">
                      {blogData[0]?.author}
                    </a>
                    <span>•</span>
                    <span>{dateTime.date + ' ' + dateTime.time}</span>
                  </div>

                  {/* Share buttons */}
                  <div className="flex items-center gap-2 font-semibold">
                    <h2 className="text-color-grey-3">Share</h2>
                    <div className="flex items-center gap-2">
                      <Link
                        className="w-9 h-9 p-2 rounded-md border border-color-grey-1 flex items-center justify-center "
                        href={'/#'}
                        target="_blank"
                      >
                        {WebsiteGlobalcon()}
                      </Link>
                      <Link
                        className="w-9 h-9 p-2 rounded-md border border-color-grey-1 flex items-center justify-center "
                        href={'/#'}
                        target="_blank"
                      >
                        {InstagramSVG()}
                      </Link>
                      <Link
                        className="w-9 h-9 p-2 rounded-md border border-color-grey-1 flex items-center justify-center "
                        href={'/#'}
                        target="_blank"
                      >
                        {LinkedinSVG()}
                      </Link>
                      <Link
                        className="w-9 h-9 p-2 rounded-md border border-color-grey-1 flex items-center justify-center "
                        href={'/#'}
                        target="_blank"
                      >
                        {TwitterSVG()}
                      </Link>
                    </div>
                  </div>
                </div>
                <div
                  dangerouslySetInnerHTML={{
                    __html: blogData[0]?.content,
                  }}
                />
              </article>
            </main>
          </div>
          <div className="col-span-3">
            <RightColumnHoc top={10}>
              <div className="flex flex-col gap-16">
                <NextBlogRead />
                <BlogThisWeek />
              </div>
            </RightColumnHoc>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Page
