'use client'

import React, { useState, useEffect } from 'react'

// Import all icons dynamically
const iconImports: Record<string, () => Promise<any>> = {
  // Main icons
  Star: () => import('@/components/Icons/Star'),
  Person: () => import('@/components/Icons/Person'),
  Dashboard: () => import('@/components/Icons/Dashboard'),
  Bell: () => import('@/components/Icons/Bell'),
  BellBorderLess: () => import('@/components/Icons/BellBorderLess'),
  Book: () => import('@/components/Icons/Book'),
  CalendarIcon: () => import('@/components/Icons/CalendarIcon'),
  CartIcon: () => import('@/components/Icons/CartIcon'),
  Clock: () => import('@/components/Icons/Clock'),
  Cross: () => import('@/components/Icons/Cross'),
  CrossIcon: () => import('@/components/Icons/CrossIcon'),
  DeleteIcon: () => import('@/components/Icons/DeleteIcon'),
  EditIcon: () => import('@/components/Icons/EditIcon'),
  EyeIcon: () => import('@/components/Icons/EyeIcon'),
  FilterIcon: () => import('@/components/Icons/FilterIcon'),
  Grid: () => import('@/components/Icons/Grid'),
  Hamburger: () => import('@/components/Icons/Hamburger'),
  Help: () => import('@/components/Icons/Help'),
  InfoIcon: () => import('@/components/Icons/InfoIcon'),
  ListIcon: () => import('@/components/Icons/ListIcon'),
  LocationIcon: () => import('@/components/Icons/LocationIcon'),
  Logout: () => import('@/components/Icons/Logout'),
  MailIcon: () => import('@/components/Icons/MailIcon'),
  Message: () => import('@/components/Icons/Message'),
  MyProfile: () => import('@/components/Icons/MyProfile'),
  Order: () => import('@/components/Icons/Order'),
  PenIcon: () => import('@/components/Icons/PenIcon'),
  PersonBorder: () => import('@/components/Icons/PersonBorder'),
  ProfileIcon: () => import('@/components/Icons/ProfileIcon'),
  Search: () => import('@/components/Icons/Search'),
  Setting: () => import('@/components/Icons/Setting'),
  Share: () => import('@/components/Icons/Share'),
  Sort: () => import('@/components/Icons/Sort'),
  TickIcon: () => import('@/components/Icons/TickIcon'),
  Upload: () => import('@/components/Icons/Upload'),
  WalletIcon: () => import('@/components/Icons/WalletIcon'),

  // Arrow icons
  BackArrow: () => import('@/components/Icons/BackArrow'),
  ForwardArrow: () => import('@/components/Icons/ForwardArrow'),
  LeftArrow: () => import('@/components/Icons/LeftArrow'),
  RightArrow: () => import('@/components/Icons/RightArrow'),
  UpArrow: () => import('@/components/Icons/UpArrow'),
  YellowRightArrow: () => import('@/components/Icons/YellowRightArrow'),

  // Tick icons
  GreenTick: () => import('@/components/Icons/GreenTick'),
  OrangeTick: () => import('@/components/Icons/OrangeTick'),
  YellowTick: () => import('@/components/Icons/YellowTick'),

  // Social media icons
  Facebook: () => import('@/components/Icons/Facebook'),
  Instagram: () => import('@/components/Icons/Instagram'),
  InstagramSVG: () => import('@/components/Icons/InstagramSVG'),
  LinkedinSVG: () => import('@/components/Icons/LinkedinSVG'),
  Twitter: () => import('@/components/Icons/Twitter'),
  TwitterSVG: () => import('@/components/Icons/TwitterSVG'),
  YoutubeSvg: () => import('@/components/Icons/YoutubeSvg'),

  // Card icons
  'CardsIcon/VisaIcon': () => import('@/components/Icons/CardsIcon/VisaIcon'),
  'CardsIcon/MasterCard': () =>
    import('@/components/Icons/CardsIcon/MasterCard'),
  'CardsIcon/AmericanExpress': () =>
    import('@/components/Icons/CardsIcon/AmericanExpress'),
  'CardsIcon/Discovery': () => import('@/components/Icons/CardsIcon/Discovery'),
  'CardsIcon/JCB': () => import('@/components/Icons/CardsIcon/JCB'),
  'CardsIcon/DinnerClub': () =>
    import('@/components/Icons/CardsIcon/DinnerClub'),
  'CardsIcon/UnionPay': () => import('@/components/Icons/CardsIcon/UnionPay'),

  // Other icons
  Bag: () => import('@/components/Icons/Bag'),
  BigCalendar: () => import('@/components/Icons/BigCalendar'),
  BlackCalendar: () => import('@/components/Icons/BlackCalendar'),
  BlueUploadIcon: () => import('@/components/Icons/BlueUploadIcon'),
  BookClosedIcon: () => import('@/components/Icons/BookClosedIcon'),
  BookOpenBlue: () => import('@/components/Icons/BookOpenBlue'),
  BookOpenOrange: () => import('@/components/Icons/BookOpenOrange'),
  BookWithPenIcon: () => import('@/components/Icons/BookWithPenIcon'),
  Class: () => import('@/components/Icons/Class'),
  Club: () => import('@/components/Icons/Club'),
  CoinIcon: () => import('@/components/Icons/CoinIcon'),
  CopyIcon: () => import('@/components/Icons/CopyIcon'),
  CustomModalIcon: () => import('@/components/Icons/CustomModalIcon'),
  Donation: () => import('@/components/Icons/Donation'),
  EditCartIcon: () => import('@/components/Icons/EditCartIcon'),
  FilledVideoPlayerIcon: () =>
    import('@/components/Icons/FilledVideoPlayerIcon'),
  FlowerIcon: () => import('@/components/Icons/FlowerIcon'),
  Genre: () => import('@/components/Icons/Genre'),
  Globe: () => import('@/components/Icons/Globe'),
  HeaderIcon: () => import('@/components/Icons/HeaderIcon'),
  Hybrid: () => import('@/components/Icons/Hybrid'),
  IEIcon: () => import('@/components/Icons/IEIcon'),
  InkPot: () => import('@/components/Icons/InkPot'),
  Level: () => import('@/components/Icons/Level'),
  Month: () => import('@/components/Icons/Month'),
  MyClassIcon: () => import('@/components/Icons/MyClassIcon'),
  NoBorderCrossIcon: () => import('@/components/Icons/NoBorderCrossIcon'),
  NotAvailableTicketIcon: () =>
    import('@/components/Icons/NotAvailableTicketIcon'),
  OnGoingBookIcon: () => import('@/components/Icons/OnGoingBookIcon'),
  OnGoingEventIcon: () => import('@/components/Icons/OnGoingEventIcon'),
  PaymentFailedIcon: () => import('@/components/Icons/PaymentFailedIcon'),
  PaymentSuccessIcon: () => import('@/components/Icons/PaymentSuccessIcon'),
  PlansIcon: () => import('@/components/Icons/PlansIcon'),
  PurplePot: () => import('@/components/Icons/PurplePot'),
  Substack: () => import('@/components/Icons/Substack'),
  SwitchRole: () => import('@/components/Icons/SwitchRole'),
  ThunderIcon: () => import('@/components/Icons/ThunderIcon'),
  TypeWriterIcon: () => import('@/components/Icons/TypeWriterIcon'),
  VideoPlayerIcon: () => import('@/components/Icons/VideoPlayerIcon'),
  WebsiteGlobalcon: () => import('@/components/Icons/WebsiteGlobalcon'),
  Week: () => import('@/components/Icons/Week'),
  AvailableTicketIcon: () => import('@/components/Icons/AvailableTicketIcon'),
}

interface IconData {
  name: string
  component: React.ComponentType
  code: string
}

export default function MuseIconPage() {
  const [icons, setIcons] = useState<IconData[]>([])
  const [filteredIcons, setFilteredIcons] = useState<IconData[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedIcon, setSelectedIcon] = useState<IconData | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [copyState, setCopyState] = useState<'idle' | 'copied'>('idle')

  useEffect(() => {
    const loadIcons = async () => {
      const iconData: IconData[] = []

      for (const [name, importFn] of Object.entries(iconImports)) {
        try {
          const iconModule = await importFn()
          // Handle both default exports and named exports
          const IconComponent = (iconModule as any).default || iconModule

          // Verify that we have a valid React component
          if (typeof IconComponent === 'function') {
            // Create a wrapper component that provides default props
            const WrappedIcon = (props: any) => {
              // Provide default props for icons that need them
              const defaultProps = {
                isActive: false,
                width: '24px',
                height: '24px',
                className: '',
                showBorder: false,
                onClick: undefined,
                ...props,
              }
              return <IconComponent {...defaultProps} />
            }

            // Get the component code
            const response = await fetch(`/api/icon-code?name=${name}`)
            const code = await response.text()

            iconData.push({
              name,
              component: WrappedIcon,
              code: code || `// Code for ${name} component`,
            })
          } else {
            console.warn(`Icon ${name} is not a valid React component`)
          }
        } catch (error) {
          console.error(`Failed to load icon ${name}:`, error)
        }
      }

      setIcons(iconData)
      setFilteredIcons(iconData)
      setIsLoading(false)
    }

    loadIcons()
  }, [])

  useEffect(() => {
    const filtered = icons.filter((icon) =>
      icon.name.toLowerCase().includes(searchTerm.toLowerCase()),
    )
    setFilteredIcons(filtered)
  }, [searchTerm, icons])

  const handleIconClick = (icon: IconData) => {
    setSelectedIcon(icon)
    setIsModalOpen(true)
  }

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopyState('copied')
      setTimeout(() => {
        setCopyState('idle')
      }, 3000)
    } catch (err) {
      console.error('Failed to copy text: ', err)
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading icons...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Muse Icon Library
              </h1>
              <p className="text-gray-600 mt-1">
                {filteredIcons.length} of {icons.length} icons
              </p>
            </div>
            <div className="relative w-96">
              <svg
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
              <input
                type="text"
                placeholder="Search icons..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Icons Grid */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-4">
          {filteredIcons.map((icon) => (
            <div
              key={icon.name}
              onClick={() => handleIconClick(icon)}
              className="bg-white rounded-lg border border-gray-200 p-4 hover:border-blue-300 hover:shadow-md transition-all duration-200 cursor-pointer group"
            >
              <div className="flex flex-col items-center space-y-3">
                <div className="w-12 h-12 flex items-center justify-center bg-gray-50 rounded-lg group-hover:bg-blue-50 transition-colors">
                  <icon.component />
                </div>
                <div className="text-center w-full">
                  <p className="text-xs font-medium text-gray-900 break-words leading-tight px-1">
                    {icon.name}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>

        {filteredIcons.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500 text-lg">
              No icons found matching &quot;{searchTerm}&quot;
            </p>
          </div>
        )}
      </div>

      {/* Icon Modal */}
      {isModalOpen && selectedIcon && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] no-scrollbar overflow-y-auto">
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h2 className="text-xl font-semibold">{selectedIcon.name}</h2>
              <button
                onClick={() => setIsModalOpen(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg
                  className="h-6 w-6"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>

            <div className="p-6 space-y-6">
              {/* Icon Preview */}
              <div className="flex items-center justify-center p-8 bg-gray-50 rounded-lg">
                <div className="w-24 h-24 flex items-center justify-center">
                  <selectedIcon.component />
                </div>
              </div>

              {/* Code Section */}
              <div>
                <div className="flex items-center justify-between mb-2">
                  <h3 className="text-lg font-semibold">Component Code</h3>
                  <button
                    onClick={() => copyToClipboard(selectedIcon.code)}
                    className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 flex items-center space-x-2 transition-all duration-200"
                  >
                    {copyState === 'copied' ? (
                      <svg
                        className="h-4 w-4 text-green-600"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M5 13l4 4L19 7"
                        />
                      </svg>
                    ) : (
                      <svg
                        className="h-4 w-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"
                        />
                      </svg>
                    )}
                    <span
                      className={
                        copyState === 'copied'
                          ? 'text-green-600 font-medium'
                          : ''
                      }
                    >
                      {copyState === 'copied' ? 'Copied!' : 'Copy'}
                    </span>
                  </button>
                </div>
                <pre className="bg-gray-900 text-green-400 p-4 rounded-lg overflow-x-auto text-sm">
                  <code>{selectedIcon.code}</code>
                </pre>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
