import { getAccountDataByAccessTokenServer } from '@/lib/actions/account.actions'
import { verifyCustomAccountSession } from '@/lib/actions/login.actions'
import { NextRequest } from 'next/server'

export async function GET(req: NextRequest) {
  if (req.method !== 'GET') {
    return new Response(
      JSON.stringify({
        message: 'Only GET Requests allowed',
      }),
      {
        status: 405,
      },
    )
  }

  const requestHeaders = new Headers(req.headers)
  const authHeader = requestHeaders.get('authorization')

  if (!authHeader) {
    return new Response(
      JSON.stringify({ message: 'Authorization header missing or malformed' }),
      { status: 401 },
    )
  }

  if (!(await verifyCustomAccountSession(authHeader))) {
    return new Response(JSON.stringify({ message: 'Unauthorized' }), {
      status: 401,
    })
  }

  const accountData = await getAccountDataByAccessTokenServer(authHeader)
  return new Response(JSON.stringify(accountData), { status: 200 })
}
