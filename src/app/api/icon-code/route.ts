import { NextRequest, NextResponse } from 'next/server'
import fs from 'fs'
import path from 'path'

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const name = searchParams.get('name')

  if (!name) {
    return NextResponse.json(
      { error: 'Icon name is required' },
      { status: 400 },
    )
  }

  try {
    let filePath: string

    // Handle CardsIcon subdirectory
    if (name.startsWith('CardsIcon/')) {
      const iconName = name.replace('CardsIcon/', '')
      filePath = path.join(
        process.cwd(),
        'src',
        'components',
        'Icons',
        'CardsIcon',
        `${iconName}.tsx`,
      )
    } else {
      filePath = path.join(
        process.cwd(),
        'src',
        'components',
        'Icons',
        `${name}.tsx`,
      )
    }

    // Check if file exists
    if (!fs.existsSync(filePath)) {
      return NextResponse.json({ error: 'Icon not found' }, { status: 404 })
    }

    // Read the file content
    const code = fs.readFileSync(filePath, 'utf-8')

    return new NextResponse(code, {
      headers: {
        'Content-Type': 'text/plain',
      },
    })
  } catch (error) {
    console.error('Error reading icon file:', error)
    return NextResponse.json(
      { error: 'Failed to read icon file' },
      { status: 500 },
    )
  }
}
