import SpecialClasses from '@/components/SpecialClasses/SpecialClasses'
import { getClassList, getSpecials } from '@/lib/actions/class.actions'
import React from 'react'

const page = async () => {
  const [specialClasses, specialPoetryClasses] = await Promise.all([
    getSpecials('class'),
    getSpecials('poetry'),
  ])

  let specialClassesData = specialClasses?.success ? specialClasses?.data : []
  let specialPoetryClassesData = specialPoetryClasses?.success
    ? specialPoetryClasses?.data
    : []
  return (
    <SpecialClasses
      specialClasses={specialClassesData}
      specialPoetryClasses={specialPoetryClassesData}
    />
  )
}

export default page
