{"name": "muse-web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier \"src/**/*.{ts,tsx,json}\" --ignore-path .gitignore", "format:check": "npm run format -- --check", "format:write": "npm run format -- --write", "validate": "npm run format:write && npm run lint && npm run build"}, "dependencies": {"@aws-sdk/client-s3": "^3.699.0", "@aws-sdk/credential-provider-node": "^3.699.0", "@aws-sdk/s3-request-presigner": "^3.699.0", "@fullcalendar/daygrid": "^6.1.15", "@fullcalendar/react": "^6.1.15", "@reduxjs/toolkit": "^2.3.0", "@stackframe/stack": "^2.8.12", "@types/react-datepicker": "^6.2.0", "axios": "^1.7.7", "browser-image-compression": "^2.0.2", "clsx": "^2.1.1", "date-fns": "^3.6.0", "dayjs": "^1.11.13", "formBuilder": "^3.19.12", "jquery": "^3.7.1", "jquery-ui-sortable": "^1.0.0", "jsforce": "^3.4.0", "jsonwebtoken": "^9.0.2", "luxon": "^3.6.1", "mongoose": "^8.5.2", "next": "14.2.5", "prop-types": "^15.8.1", "react": "^18", "react-acceptjs": "^0.4.0", "react-country-state-city": "^1.1.11", "react-datepicker": "^8.1.0", "react-dom": "^18", "react-google-recaptcha": "^3.1.0", "react-infinite-scroll-component": "^6.1.0", "react-input-mask": "^2.0.4", "react-phone-input-2": "^2.15.1", "react-redux": "^9.1.2", "react-select": "^5.10.2", "react-toastify": "^10.0.5", "recharts": "^2.13.0", "redis": "^5.5.6", "server-only": "^0.0.1", "swiper": "^11.2.6", "twilio": "^5.2.2"}, "devDependencies": {"@types/jquery": "^3.5.30", "@types/jsforce": "^1.11.5", "@types/jsonwebtoken": "^9.0.7", "@types/luxon": "^3.6.2", "@types/react": "^18.3.18", "@types/react-dom": "^18", "@types/react-google-recaptcha": "^2.1.9", "@types/react-input-mask": "^3.0.6", "eslint": "^8", "eslint-config-next": "14.2.5", "prettier": "^3.3.3", "stream-browserify": "^3.0.0", "tailwindcss": "^3.4.1", "typescript": "^5"}}